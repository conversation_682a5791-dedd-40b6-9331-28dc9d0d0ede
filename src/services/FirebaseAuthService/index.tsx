import appleAuth from '@invertase/react-native-apple-authentication';
import auth, {FirebaseAuthTypes} from '@react-native-firebase/auth';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import {Alert} from 'react-native';
import Config from 'react-native-config';
import OneSignal from 'react-native-onesignal';
import {auth as authLogger, timing, error} from '~Utils/debugLogger';

class FirebaseAuthService {
  auth = auth();

  constructor() {
    GoogleSignin.configure({
      iosClientId: String(Config.IOS_CLIENT_ID),
      webClientId: String(Config.WEB_CLIENT_ID), // client ID of type WEB for your server (needed to verify user ID and offline access)
      offlineAccess: true, // if you want to access Google API on behalf of the user FROM YOUR SERVER
      profileImageSize: 512, // [iOS] The desired height (and width) of the profile image. Increased from 120px to 512px for better quality
    });
    this.signInWithGoogle = this.signInWithGoogle.bind(this);
    this.signInWithServices = this.signInWithServices.bind(this);
    this.signInEmailPassword = this.signInEmailPassword.bind(this);
    this.signUp = this.signUp.bind(this);
    this.signInWithApple = this.signInWithApple.bind(this);
    this.sendChangePasswordLink = this.sendChangePasswordLink.bind(this);
    this.logOut = this.logOut.bind(this);
    this.reauthenticateGoogle = this.reauthenticateGoogle.bind(this);
    this.reauthenticateApple = this.reauthenticateApple.bind(this);
    this.reAuthWithCredentials = this.reAuthWithCredentials.bind(this);
    this.reAuthWithEmailAndPassword = this.reAuthWithEmailAndPassword.bind(this);
    this.deleteAccount = this.deleteAccount.bind(this);
  }

  async signInEmailPassword(email: string, password: string, setIsLoading: any) {
    try {
      const response = await this.auth.signInWithEmailAndPassword(email, password);
      console.log(response, 'responseresponse');

      if (!response?.user?.emailVerified) {
        response.user.sendEmailVerification();
        setIsLoading(false);
        Alert.alert(
          'Verify your email',
          "Your email isn't verified. A verification link has been sent to your email. If you don't see it, please check your spam folder.",
        );
        return;
      }

      console.log('response.user', response.user);
      OneSignal.disablePush(false);
      return {user: response.user};
    } catch (error: any) {
      if (error.code === 'auth/internal-error') {
        setIsLoading(false);
        console.log('The password used was incorrect. Please try again with the correct password.');
        Alert.alert('The email or password you entered is incorrect. Please try again.');
      }

      if (error.code === 'auth/invalid-login') {
        setIsLoading(false);
        console.log('The password used was incorrect. Please try again with the correct password.');
        Alert.alert('The email or password you entered is incorrect. Please try again.');
      }

      if (error.code === 'auth/email-already-in-use') {
        setIsLoading(false);
        console.log('That email address is already in use!');
        Alert.alert('That email address is already in use. Please try to sign in with the correct password.');
      }

      if (error.code === 'auth/invalid-email') {
        setIsLoading(false);
        console.log('That email address is invalid.');
        Alert.alert('That email address is invalid.');
      }

      if (error.code === 'auth/wrong-password') {
        setIsLoading(false);
        console.log('The email or password you entered is incorrect. Please try again.');
        Alert.alert('The email or password you entered is incorrect. Please try again.');
      }

      if (error.code === 'auth/user-not-found') {
        setIsLoading(false);
        console.log('The email or password you entered is incorrect. Please try again.');
        Alert.alert('The email or password you entered is incorrect. Please try again.');
      }
      if (error.code === 'auth/network-request-failed') {
        setIsLoading(false);
        console.log('A network error (such as timeout, interrupted connection or unreachable host) has occurred.');
        Alert.alert('A network error (such as timeout, interrupted connection or unreachable host) has occurred.');
      }
      return;
    }
  }

  async signUp(email: string, password: string, confirmPassword: string, setIsLoading: any) {
    try {
      if (confirmPassword !== password) {
        setIsLoading(false);
        Alert.alert("Confirm password doesn't match with password");
        return false;
      }
      const {user} = await this.auth.createUserWithEmailAndPassword(email, password);
      await user.sendEmailVerification();
      setIsLoading(false);
      Alert.alert(
        'A verification link has been sent to your email.',
        'If you don’t see it, please check your spam folder.',
      );

      await this.logOut();
      return {user};
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        setIsLoading(false);
        Alert.alert('That email address is already in use.');
      }

      if (error.code === 'auth/invalid-email') {
        setIsLoading(false);
        Alert.alert('That email address is invalid.');
      }
      return false;
    }
  }

  async signInWithServices(credential: FirebaseAuthTypes.AuthCredential) {
    try {
      const {user, additionalUserInfo} = await this.auth.signInWithCredential(credential);
      OneSignal.disablePush(false);

      // Check if this is a new user (first time signing up with Google)
      if (additionalUserInfo?.isNewUser) {
        Alert.alert(
          'Welcome to Pyxi!',
          'Your account has been successfully created and your email has been verified via Google. Welcome to the community!',
        );
      }

      return {user};
    } catch (error: any) {
      if (error.code === 'auth/account-exists-with-different-credential') {
        Alert.alert('That email address is already in use with a different account.');
      }

      if (error.code === 'auth/invalid-email') {
        Alert.alert('That email address is invalid.');
      }

      if (error.code === 'auth/user-not-found') {
        Alert.alert("This user can't be found. Please try signing up below.");
      }
      return false;
    }
  }

  async signInWithGoogle() {
    try {
      // Check if your device supports Google Play
      await GoogleSignin.hasPlayServices({showPlayServicesUpdateDialog: true});

      // Get the users ID token - Updated for v14 response format
      const response = await GoogleSignin.signIn();

      // Handle the new response format
      if (response.type === 'success') {
        const {idToken} = response.data;

        // Create a Google credential with the token
        const googleCredential = auth.GoogleAuthProvider.credential(idToken);

        const authResponse = await this.signInWithServices(googleCredential);

        return authResponse;
      } else {
        console.log('Google Sign-In failed:', response.type);
        return false;
      }
    } catch (error: any) {
      console.log(error);
      return false;
    }
  }

  async signInWithApple() {
    try {
      // Start the sign-in request
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });

      // Ensure Apple returned a user identityToken
      if (!appleAuthRequestResponse.identityToken) {
        Alert.alert('Apple Sign-In failed', 'No identify token returned');
        return false;
      }

      // Create a Firebase credential from the response
      const {identityToken, nonce} = appleAuthRequestResponse;
      const appleCredential = auth.AppleAuthProvider.credential(identityToken, nonce);

      const authResponse = await this.signInWithServices(appleCredential);

      return authResponse;
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  async sendChangePasswordLink(email: string) {
    try {
      await this.auth.sendPasswordResetEmail(email);
      Alert.alert(
        'Password reset link sent',
        "A password reset link has been sent to your email. If you don't see it, please check your spam folder.",
      );
      return true;
    } catch (error: any) {
      let errorMessage = 'Something went wrong. Please try again.';

      if (error.code === 'auth/user-not-found') {
        errorMessage = 'No account found with this email address.';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Please enter a valid email address.';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many requests. Please try again later.';
      }

      Alert.alert('Error', errorMessage);
      return false;
    }
  }

  async logOut() {
    try {
      // Before signing out, disable OneSignal push notifications
      OneSignal.disablePush(true);
      await this.auth.signOut();
      return true;
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  async reauthenticateGoogle() {
    try {
      // Get the current user
      const user = this.auth.currentUser;
      // Get the account - Updated for v14 response format
      const response = await GoogleSignin.signInSilently();

      // Handle the new response format
      if (response.type === 'success') {
        const {idToken} = response.data;
        const credential = auth.GoogleAuthProvider.credential(idToken);
        await user?.reauthenticateWithCredential(credential);

        return true;
      } else if (response.type === 'noSavedCredentialFound') {
        // No saved credential found, user needs to sign in again
        console.log('No saved credential found for reauthentication');
        return false;
      }
      return false;
    } catch (error: any) {
      Alert.alert('Something went wrong', error.message);
      return false;
    }
  }

  async reauthenticateApple() {
    try {
      const response = await this.signInWithApple();
      if (response) {
        return true;
      }
      Alert.alert('Something went wrong', 'Try re-login and make it again or Ask Pyxi for help');
      return false;
    } catch (error: any) {
      Alert.alert('Something went wrong', error.message);
      return false;
    }
  }

  async reAuthWithCredentials() {
    try {
      const user = this.auth.currentUser;
      const providerData = user?.providerData[0].providerId;
      if (providerData === 'google.com') {
        await this.reauthenticateGoogle();
        return 1;
      }

      if (providerData === 'apple.com') {
        await this.reauthenticateApple();
        return 1;
      }
      return 0;
    } catch (error) {
      return -1;
    }
  }

  async reAuthWithEmailAndPassword(password: string) {
    try {
      const user = this.auth.currentUser!;
      const credential = auth.EmailAuthProvider.credential(user.email!, password);
      const reauthResponse = await user?.reauthenticateWithCredential(credential);

      return !!reauthResponse;
    } catch (error) {
      return false;
    }
  }

  async deleteAccount() {
    try {
      await this.auth.currentUser!.delete();
      return;
    } catch (error: any) {
      Alert.alert('Something went wrong', error.message);
    }
  }

  async getAuthToken() {
    try {
      authLogger('Getting Firebase auth token');
      const tokenStart = Date.now();
      const token = await this.auth.currentUser?.getIdToken();
      timing('Firebase auth token obtained', tokenStart);
      return token || '';
    } catch (err) {
      error('Failed to get Firebase auth token', err);
      return '';
    }
  }
}

const FirebaseAuth = new FirebaseAuthService();

export default FirebaseAuth;
