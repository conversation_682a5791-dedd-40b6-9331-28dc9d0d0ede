import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import 'react-native-get-random-values';
import {v4 as uuidv4} from 'uuid';
import messaging from '@react-native-firebase/messaging';
// import { Alert } from 'react-native'; // Імпортуємо Alert для відображення сповіщень в додатку
import {
  BUTTON_STATUS_ENUM,
  CHAT_MESSAGE_TYPE_ENUM,
  CHAT_TYPE_ENUM,
  ChatType,
  ConciergeChatType,
  CONCIERGE_CHAT_TYPE_ENUM,
  ChatMessageType,
} from '../../types/chat';

import moment from 'moment';
import FirebaseAuth from '~services/FirebaseAuthService';
import {BASE_API_URL} from '@env';
import {Event} from '~types/api/event';
import i18n from 'i18next';
// import OneSignalService from '~services/FirebaseNotificationsService';
// import {Notifier, NotifierComponents} from 'react-native-notifier';

class FirebaseChats {
  deleteGroupChat(arg0: {event_id: number}) {
    throw new Error('Method not implemented.');
  }
  firestore = firestore();
  auth = auth();

  constructor() {
    // Remove redundant assignments since they're already done above
    const messagingInstance = messaging();

    this.createNewGroupChat = this.createNewGroupChat.bind(this);
    this.createPrivateChat = this.createPrivateChat.bind(this);
    this.createPyxiConciergeChat = this.createPyxiConciergeChat.bind(this);
    this.removeUserFromTheGroupChat = this.removeUserFromTheGroupChat.bind(this);
    this.pushConciergeMessage = this.pushConciergeMessage.bind(this);
    this.pushMessageForUsers = this.pushMessageForUsers.bind(this);
    this.updateUserFcmToken = this.updateUserFcmToken.bind(this);
    this.createOrganisationChat = this.createOrganisationChat.bind(this);
    this.createContactUsChat = this.createContactUsChat.bind(this);
    // this.handleForegroundNotification = this.handleForegroundNotification.bind(this);
    // Викликаємо метод для обробки сповіщень на передньому плані
    // this.handleForegroundNotification();
  }

  async updateUserFcmToken(token: string) {
    try {
      const currentUser = this.auth.currentUser;
      if (currentUser) {
        const userDocRef = this.firestore.collection('users').doc(currentUser.uid);
        await userDocRef.set(
          {
            deviceFcmToken: token,
          },
          {merge: true},
        );
      } else {
        console.log('No user is signed in.');
      }
    } catch (error) {
      console.error('Error updating FCM token in Firestore:', error);
    }
  }

  async sendPushNotification(token: string, title: string, message: string, data: object) {
    const serverKey =
      'AAAA3g8xYT0:APA91bEs7-It_xwurSWKqQ0pCUQIhR19CS0nEEIpv3j04zKczi6MTuwE7Ed4NAN28EyliGWppc3KFiymdM1fTSpKMQF_kBSjGb6patTJqkC_1Z3cbRTM42mm_9egAiO87hexW683ONVT';
    try {
      const response = await fetch('https://fcm.googleapis.com/fcm/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `key=${serverKey}`,
        },
        body: JSON.stringify({
          to: token,
          notification: {
            title: title,
            body: message,
          },
          data: {
            ...data,
          },
        }),
      });

      const responseData = await response.json();
      console.log('FCM response:', responseData);
    } catch (error) {
      console.error('Error sending FCM message:', error);
    }
  }

  // async handleForegroundNotification() {
  //   messaging.onMessage(async remoteMessage => {
  //     console.log('A new FCM message arrived!', JSON.stringify(remoteMessage));
  //     // Відображаємо сповіщення в додатку за допомогою Alert
  //     Notifier.showNotification({
  //       title: remoteMessage?.notification?.title,
  //       description: remoteMessage?.notification?.body,
  //       Component: NotifierComponents.Alert,
  //       componentProps: {
  //         alertType: 'info',
  //       },
  //     });
  //   });
  // }

  async createNewGroupChat({
    event_id,
    event_image,
    event_name,
    user_name,
    user_id,
    otherUsersId,
    otherUsersName,
  }: {
    event_id: number;
    event_image: string;
    event_name: string;
    user_name: string;
    user_id: string;
    otherUsersId: string[];
    otherUsersName: string[];
  }): Promise<string | undefined> {
    try {
      const existingChat = await this.firestore.collection('chats').where('eventId', '==', event_id).get();
      if (existingChat.docs[0]?.id) {
        return existingChat.docs[0].id;
      }

      await this.firestore.collection('chats').add({
        type: CHAT_TYPE_ENUM.GROUP,
        userIds: [user_id, ...otherUsersId],
        users: [user_name, ...otherUsersName],
        history: [
          {
            message_id: uuidv4(),
            message: `Welcome to ${event_name}!`,
            type: CHAT_MESSAGE_TYPE_ENUM.INFO,
            sender_image: event_image,
            sender_id: '',
            sender: '',
            readUserIds: [],
            timestamp: moment().toISOString() as unknown,
          },
        ],
        eventImage: event_image,
        eventName: event_name,
        eventId: event_id,
      } as ChatType);

      const querySnapshot = await this.firestore.collection('chats').where('eventId', '==', event_id).get();
      if (querySnapshot.docs[0]?.id) {
        return querySnapshot.docs[0].id;
      }
    } catch (e) {
      console.error(e);
    }
    return undefined; // Додано повернення undefined у випадку помилки або відсутності значення
  }

  async addNewUserToTheGroupChat({
    user_id,
    event_id,
    user_name,
    user_image,
  }: {
    user_id: string;
    event_id: number;
    user_name: string;
    user_image: string;
  }) {
    try {
      const querySnapshot = await this.firestore.collection('chats').where('eventId', '==', event_id).get();

      querySnapshot.forEach(async snap => {
        const currentChat = snap.data() as ChatType;

        await snap.ref.update({
          history: [
            ...currentChat.history,
            {
              message_id: uuidv4(),
              message: `${user_name} has joined the event`,
              type: CHAT_MESSAGE_TYPE_ENUM.INFO,
              sender_image: user_image,
              sender_id: '',
              sender: user_name,
              readUserIds: [],
              timestamp: moment().toISOString() as unknown,
            },
          ],
          users: [...currentChat.users, user_name],
          userIds: [...currentChat.userIds, user_id],
        });
      });
    } catch (_) {}
  }

  async createContactUsChat({
    user_id1,
    user_id2,
    user_name1,
    user_name2,
  }: {
    user_id1: string;
    user_id2: string;
    user_name1: string;
    user_name2: string;
  }) {
    try {
      const existingChat = await this.firestore.collection('chats').where('userIds', 'array-contains', user_id1).get();

      const existChatSnap = existingChat.docs.find(doc => {
        const chat = doc.data() as ChatType;
        return chat.type === 'contact-pyxi' && chat.userIds.includes(user_id2);
      });

      if (existChatSnap) {
        return existChatSnap.id;
      } else {
        const newChatData: ChatType = {
          type: 'contact-pyxi',
          userIds: [user_id1, user_id2],
          users: [user_name1, user_name2],
          progress: '',
          history: [],
        };

        const newChat = await this.firestore.collection('chats').add(newChatData);
        return newChat.id;
      }
    } catch (_) {
      return '';
    }
  }

  async createPrivateChat({
    user_id1,
    user_id2,
    user_name1,
    user_name2,
    user_image,
    event,
  }: {
    user_id1: string;
    user_id2: string;
    user_name1: string;
    user_name2: string;
    user_image: string;
    event?: Event;
  }) {
    console.log('🚀 FirebaseChats: createPrivateChat START');
    try {
      console.log('FirebaseChats: createPrivateChat called with:', {
        user_id1,
        user_id2,
        user_name1,
        user_name2,
        user_image,
        event: event ? 'event provided' : 'no event',
      });

      // Validate required parameters
      if (!user_id1) {
        throw new Error('user_id1 is required but was: ' + user_id1);
      }
      if (!user_id2) {
        throw new Error('user_id2 is required but was: ' + user_id2);
      }
      if (!user_name1 || user_name1.trim() === '') {
        throw new Error('user_name1 is required but was: "' + user_name1 + '"');
      }
      if (!user_name2 || user_name2.trim() === '') {
        throw new Error('user_name2 is required but was: "' + user_name2 + '"');
      }
      if (!user_image) {
        throw new Error('user_image is required but was: ' + user_image);
      }

      // Clean up the names to remove extra whitespace
      const cleanUser1Name = user_name1.trim();
      const cleanUser2Name = user_name2.trim();
      const existingChat = await this.firestore.collection('chats').where('userIds', 'array-contains', user_id1).get();

      const existChatSnap = existingChat.docs.find(doc => {
        const chat = doc.data() as ChatType;
        return chat.type === CHAT_TYPE_ENUM.PRIVATE && chat.userIds.includes(user_id2);
      });

      if (existChatSnap) {
        console.log('FirebaseChats: Found existing private chat:', existChatSnap.id);
        const chatRef = this.firestore.collection('chats').doc(existChatSnap.id);

        // Only add event message if event is provided
        if (event) {
          const dataRef = (await chatRef.get()).data()! as ChatType;

          const updated = [
            ...dataRef.history,
            {
              message_id: uuidv4(),
              message: '',
              type: CHAT_MESSAGE_TYPE_ENUM.EVENT,
              sender_image: user_image,
              sender_id: user_id1,
              sender: cleanUser1Name,
              readUserIds: [],
              timestamp: moment().toISOString(),
              event: event,
            },
          ];

          await chatRef.update({
            history: updated,
          });
        }

        console.log('FirebaseChats: Updated existing private chat:', existChatSnap.id);
        return existChatSnap.id;
      } else {
        const history: ChatMessageType[] = [
          {
            message_id: uuidv4(),
            message: `Hi ${cleanUser2Name}`,
            type: CHAT_MESSAGE_TYPE_ENUM.MESSAGE,
            sender_image: user_image,
            sender_id: user_id1,
            sender: cleanUser1Name,
            readUserIds: [],
            timestamp: moment().toISOString(),
          },
        ];
        if (event) {
          history.push({
            message_id: uuidv4(),
            message: '',
            type: CHAT_MESSAGE_TYPE_ENUM.EVENT,
            sender_image: user_image,
            sender_id: user_id1,
            sender: cleanUser1Name,
            readUserIds: [],
            timestamp: moment().toISOString(),
            event: event,
          });
        }
        const newChatData: ChatType = {
          type: CHAT_TYPE_ENUM.PRIVATE,
          userIds: [user_id1, user_id2],
          users: [cleanUser1Name, cleanUser2Name],
          status: 'open',
          progress: 'new',
          history: history,
          event: event,
        };

        console.log('FirebaseChats: Creating new private chat...');
        const newChat = await this.firestore.collection('chats').add(newChatData);
        console.log('FirebaseChats: Created new private chat:', newChat.id);
        return newChat.id;
      }
    } catch (error) {
      console.error('🚨 FirebaseChats: createPrivateChat CRITICAL ERROR:', error);
      console.error('🚨 Error details:', JSON.stringify(error, null, 2));
      throw error; // Don't return empty string, let the error bubble up
    }
  }

  async createOrganisationChat({
    user_id1,
    user_id2,
    user_name1,
    user_name2,
    user_image,
    isTechnical,
    event,
  }: {
    user_id1: string;
    user_id2: string;
    user_name1: string;
    user_name2: string;
    user_image: string;
    isTechnical?: boolean;
    event: Event | null | undefined;
  }) {
    try {
      const existingChat = await this.firestore.collection('chats').where('userIds', 'array-contains', user_id1).get();
      const chatsSnapshot = await this.firestore.collection('chats').get();
      const totalChats = chatsSnapshot.size;

      const existChatSnap = existingChat.docs.find(doc => {
        const chat = doc.data() as ChatType;
        return chat.type === CHAT_TYPE_ENUM.ORGANISATION && chat.userIds.includes(user_id2);
      });

      if (existChatSnap) {
        const chatRef = this.firestore.collection('chats').doc(existChatSnap.id);
        chatRef.update({status: 'open', event: event});
        if (event) {
          const dataRef = (await chatRef.get()).data()! as ChatType;

          const updated = [
            ...dataRef.history,
            {
              message_id: uuidv4(),
              message: '',
              type: CHAT_MESSAGE_TYPE_ENUM.EVENT,
              sender_image: user_image,
              sender_id: user_id1,
              sender: user_name1,
              readUserIds: [],
              timestamp: moment().toISOString(),
              event: event,
            },
          ];

          await chatRef.update({
            history: updated,
          });
        }
        return existChatSnap.id;
      } else {
        if (isTechnical) {
          const newChatData: ChatType = {
            type: CHAT_TYPE_ENUM.ORGANISATION,
            userIds: [user_id1, user_id2],
            users: [user_name1, user_name2],
            status: 'open',
            progress: 'new',
            event: event,
            issueNumber: [totalChats],
            history: [
              {
                message_id: uuidv4(),
                message: i18n.t('technical_issue', {name: 'Pyxi'}),
                type: CHAT_MESSAGE_TYPE_ENUM.MESSAGE,
                sender_image: user_image,
                sender_id: user_id1,
                sender: user_name1,
                readUserIds: [],
                timestamp: moment().toISOString(),
              },
            ],
          };

          const newChat = await this.firestore.collection('chats').add(newChatData);
          return newChat.id;
        } else {
          const newChatData: ChatType = {
            type: CHAT_TYPE_ENUM.ORGANISATION,
            userIds: [user_id1, user_id2],
            users: [user_name1, user_name2],
            status: 'open',
            progress: 'new',
            event: event,
            issueNumber: [totalChats],
            history: [
              {
                message_id: uuidv4(),
                message: i18n.t('greeting', {user_name1: user_name1}),
                type: CHAT_MESSAGE_TYPE_ENUM.MESSAGE,
                sender_image: user_image,
                sender_id: user_id1,
                sender: user_name1,
                readUserIds: [],
                timestamp: moment().toISOString(),
              },
            ],
          };

          const newChat = await this.firestore.collection('chats').add(newChatData);
          return newChat.id;
        }
      }
    } catch (_) {
      return '';
    }
  }

  async createPyxiConciergeChat({
    user_id,
    user_name,
    user_image,
  }: {
    user_id: string;
    user_name: string;
    user_image: string;
  }) {
    const conciergeChatExists = await this.firestore.collection('conciergeChat').where('userId', '==', user_id).get();

    conciergeChatExists.empty &&
      (await this.firestore.collection('conciergeChat').add({
        type: CONCIERGE_CHAT_TYPE_ENUM.ONE_ON_ONE,
        userId: user_id,
        userName: user_name,
        userImage: user_image,
        messages: [
          {
            timestamp: moment().toISOString() as unknown,
            senderId: user_id,
            sender: user_name,
            status: BUTTON_STATUS_ENUM.EMPTY,
            readUserIds: [],
            message: 'Welcome to Pyxi, your personal assistant for all things social. How may I help you today?',
          },
        ],
      } as ConciergeChatType));
  }

  async removeUserFromTheGroupChat({
    user_id,
    event_id,
    user_name,
    event_image,
  }: {
    user_id: string;
    event_id: number;
    user_name: string;
    event_image: string;
  }) {
    const querySnapshot = await this.firestore.collection('chats').where('eventId', '==', event_id).get();

    if (querySnapshot.empty) {
      return;
    }
    querySnapshot.forEach(async snap => {
      const currentChat = snap.data() as ChatType;
      await snap.ref.update({
        userIds: [...currentChat.userIds.filter(userId => userId !== user_id)],
        history: [
          ...currentChat.history,
          {
            message_id: uuidv4(),
            message: `${user_name} has exited the chat`,
            type: CHAT_MESSAGE_TYPE_ENUM.INFO,
            sender_image: event_image,
            sender_id: '',
            sender: user_name,
            readUserIds: [...currentChat.userIds.filter(userId => userId !== user_id)],
            timestamp: moment().toISOString() as unknown,
          },
        ],
      });
    });
  }

  async callEmailNotificationApi(message_id: string, chat_id: string) {
    try {
      const token = await FirebaseAuth.getAuthToken();
      const url = `${BASE_API_URL}/firestore/chats/unread-users/${chat_id}/${message_id}`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      const responseData = await response.json();
      console.log('callEmailNotificationApi', responseData);
    } catch (error) {
      console.log('callEmailNotificationApi', error);
    }
  }

  async pushMessageForUsers({
    chat_id,
    user_id,
    user_name,
    text,
    user_image,
    temp_message_id,
  }: {
    chat_id: string;
    user_id: string;
    user_image: string;
    user_name: string;
    text: string;
    temp_message_id?: string;
  }): Promise<string> {
    try {
      console.log('FirebaseChats: pushMessageForUsers called with:', {chat_id, user_id, user_name, text});

      if (!chat_id) {
        throw new Error('chat_id is required');
      }
      if (!user_id) {
        throw new Error('user_id is required');
      }
      if (!text) {
        throw new Error('text is required');
      }

      const datetime = moment().toISOString();
      const chatRef = this.firestore.collection('chats').doc(chat_id);

      console.log('FirebaseChats: Getting chat document...');
      const chatDoc = await chatRef.get();

      if (!chatDoc.exists) {
        throw new Error(`Chat document with id ${chat_id} does not exist`);
      }

      const dataRef = chatDoc.data() as ChatType;
      console.log('FirebaseChats: Current chat data:', dataRef);

      const message_id = temp_message_id || uuidv4();
      const newMessage = {
        message_id: message_id,
        message: text,
        sender: user_name,
        sender_image: user_image,
        sender_id: user_id,
        timestamp: datetime,
        type: CHAT_MESSAGE_TYPE_ENUM.MESSAGE,
        readUserIds: [user_id],
        status: 'sent',
      };

      const updated = [...(dataRef.history || []), newMessage];

      console.log('FirebaseChats: Updating chat with new message...');
      await chatRef.update({
        history: updated,
      });

      console.log('FirebaseChats: Message sent successfully');
      this.callEmailNotificationApi(message_id, chat_id);

      const userUidsArr = [...(dataRef.userIds || []).filter(id => id !== user_id)];

      if (userUidsArr.length > 0) {
        const requestNotificationBody = {
          headings: {
            en: user_name,
          },
          contents: {
            en: text,
          },
          data: {
            chat_id: chat_id,
            type: 'ChatNotification',
            chat_type: dataRef.type,
            clickAction: 'OPEN_CHAT',
          },
          include_external_user_ids: userUidsArr,
        };

        const token = await FirebaseAuth.getAuthToken();
        const url = `${BASE_API_URL}/send-notification`;
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: token,
          },
          body: JSON.stringify(requestNotificationBody),
        });

        const responseData = await response.json();
        console.log('new Message', responseData);
      }

      return message_id;
    } catch (error) {
      console.error('FirebaseChats: pushMessageForUsers error:', error);
      throw error; // Re-throw the error so it can be caught by the calling function
    }
  }
  async markMessageAsRead({
    chat_id,
    message_id,
    user_id,
    chat_type = 'user',
  }: {
    chat_id: string;
    message_id: string;
    user_id: string;
    chat_type?: 'user' | 'concierge';
  }) {
    try {
      console.log('FirebaseChats: markMessageAsRead called with:', {chat_id, message_id, user_id, chat_type});

      const collection = chat_type === 'concierge' ? 'conciergeChat' : 'chats';
      const chatRef = this.firestore.collection(collection).doc(chat_id);
      const chatDoc = await chatRef.get();

      if (!chatDoc.exists) {
        throw new Error(`Chat document with id ${chat_id} does not exist`);
      }

      const chatData = chatDoc.data();
      const messages = chat_type === 'concierge' ? chatData?.messages : chatData?.history;

      if (!messages) {
        console.log('No messages found in chat');
        return;
      }

      // Find and update the specific message
      const updatedMessages = messages.map((msg: any) => {
        const msgId = chat_type === 'concierge' ? msg.messageId : msg.message_id;
        if (msgId === message_id) {
          return {
            ...msg,
            status: 'read',
            messageStatus: 'read',
            readUserIds: [...(msg.readUserIds || []), user_id].filter((id, index, arr) => arr.indexOf(id) === index),
          };
        }
        return msg;
      });

      const updateField = chat_type === 'concierge' ? 'messages' : 'history';
      await chatRef.update({
        [updateField]: updatedMessages,
      });

      console.log('FirebaseChats: Message marked as read successfully');
    } catch (error) {
      console.error('FirebaseChats: markMessageAsRead error:', error);
      throw error;
    }
  }

  async pushConciergeMessage({
    chat_id,
    user_id,
    user_name,
    text,
    temp_message_id,
  }: {
    chat_id: string;
    user_id: string;
    user_name: string;
    text: string;
    temp_message_id?: string;
  }): Promise<string> {
    try {
      console.log('FirebaseChats: pushConciergeMessage called with:', {
        chat_id,
        user_id,
        user_name,
        text,
        temp_message_id,
      });

      if (!chat_id) {
        throw new Error('chat_id is required');
      }
      if (!user_id) {
        throw new Error('user_id is required');
      }
      if (!text) {
        throw new Error('text is required');
      }

      const datetime = moment().toISOString();
      const messageId = temp_message_id || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const chatRef = this.firestore.collection('conciergeChat').doc(chat_id);

      console.log('FirebaseChats: Getting chat document...');
      const chatDoc = await chatRef.get();

      if (!chatDoc.exists) {
        throw new Error(`Chat document with id ${chat_id} does not exist`);
      }

      const dataRef = chatDoc.data();
      console.log('FirebaseChats: Current chat data:', dataRef);

      const newMessage = {
        messageId,
        senderId: user_id,
        message: text,
        sender: user_name,
        timestamp: datetime,
        readUserIds: [user_id],
        messageStatus: 'sent',
      };

      const updated = [...(dataRef?.messages || []), newMessage];

      console.log('FirebaseChats: Updating chat with new message...');
      await chatRef.update({
        messages: updated,
      });

      console.log('FirebaseChats: Message sent successfully');

      // let data = {
      //   uid: user_id,
      //   title: user_name,
      //   message: text,
      //   additional_data: {
      //     chat_id: chat_id,
      //     type: 'AdminChat',
      //   },
      // };
      try {
        // await OneSignalService.sendPushNotification(data);
      } catch (error) {
        console.log('FirebaseChats: Push notification error:', error);
      }
      return messageId;
    } catch (error) {
      console.error('FirebaseChats: pushConciergeMessage error:', error);
      throw error; // Re-throw the error so it can be caught by the calling function
    }
  }
}

const FirebaseChatsService = new FirebaseChats();

export default FirebaseChatsService;
