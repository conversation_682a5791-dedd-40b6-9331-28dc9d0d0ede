import {QueryClient} from 'react-query';
import {queryKeys} from '~Utils/performance/queryConfig';
import {performanceMonitor} from '~Utils/performance/performanceMonitor';
import FirebaseAuth from '~services/FirebaseAuthService';
import {BASE_API_URL} from '@env';
import {TABS, ORDER_BY, ORDER_DIR} from '~types/events';

interface PrefetchStrategy {
  priority: 'high' | 'medium' | 'low';
  delay: number;
  condition?: () => boolean;
}

class PrefetchService {
  private queryClient: QueryClient;
  private prefetchQueue: Array<{fn: () => Promise<void>; priority: string; delay: number}> = [];
  private isProcessing = false;
  private userBehaviorData = {
    mostViewedCategories: [] as string[],
    preferredTimeframes: [] as string[],
    averageSessionDuration: 0,
    searchPatterns: [] as string[],
  };

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
    this.startBackgroundProcessing();
  }

  // Prefetch popular events
  async prefetchPopularEvents(strategy: PrefetchStrategy = {priority: 'medium', delay: 2000}) {
    this.addToPrefetchQueue(
      async () => {
        try {
          await this.queryClient.prefetchInfiniteQuery({
            queryKey: queryKeys.events.list({
              tab: TABS.DISCOVER,
              order_by: ORDER_BY.LIKES_COUNT,
              order_dir: ORDER_DIR.DESC,
              limit: 20,
            }),
            queryFn: async () => {
              const token = await FirebaseAuth.getAuthToken();
              const response = await fetch(`${BASE_API_URL}events/`, {
                method: 'GET',
                headers: {Authorization: token, Accept: 'application/json'},
                body: JSON.stringify({
                  tab: TABS.DISCOVER,
                  order_by: ORDER_BY.LIKES_COUNT,
                  order_dir: ORDER_DIR.DESC,
                  limit: 20,
                  offset: 0,
                }),
              });
              return response.json();
            },
            staleTime: 1000 * 60 * 10, // 10 minutes
          });
          performanceMonitor.recordPrefetchSuccess();
        } catch (error) {
          console.warn('Failed to prefetch popular events:', error);
        }
      },
      strategy.priority,
      strategy.delay,
    );
  }

  // Prefetch nearby events based on user location
  async prefetchNearbyEvents(
    lat: number,
    lng: number,
    radius: number = 10,
    strategy: PrefetchStrategy = {priority: 'high', delay: 1000},
  ) {
    this.addToPrefetchQueue(
      async () => {
        try {
          await this.queryClient.prefetchInfiniteQuery({
            queryKey: queryKeys.events.nearby(lat, lng, radius),
            queryFn: async () => {
              const token = await FirebaseAuth.getAuthToken();
              const response = await fetch(`${BASE_API_URL}events/`, {
                method: 'GET',
                headers: {Authorization: token, Accept: 'application/json'},
                body: JSON.stringify({
                  tab: TABS.DISCOVER,
                  order_by: ORDER_BY.START_DATE,
                  order_dir: ORDER_DIR.ASC,
                  distance_km: radius,
                  limit: 20,
                  offset: 0,
                }),
              });
              return response.json();
            },
            staleTime: 1000 * 60 * 5, // 5 minutes for location-based data
          });
          performanceMonitor.recordPrefetchSuccess();
        } catch (error) {
          console.warn('Failed to prefetch nearby events:', error);
        }
      },
      strategy.priority,
      strategy.delay,
    );
  }

  // Prefetch user's liked events
  async prefetchLikedEvents(strategy: PrefetchStrategy = {priority: 'medium', delay: 3000}) {
    this.addToPrefetchQueue(
      async () => {
        try {
          await this.queryClient.prefetchInfiniteQuery({
            queryKey: queryKeys.events.list({tab: 'liked'}),
            queryFn: async () => {
              const token = await FirebaseAuth.getAuthToken();
              const response = await fetch(`${BASE_API_URL}events/liked`, {
                method: 'GET',
                headers: {Authorization: token, Accept: 'application/json'},
              });
              return response.json();
            },
            staleTime: 1000 * 60 * 15, // 15 minutes
          });
          performanceMonitor.recordPrefetchSuccess();
        } catch (error) {
          console.warn('Failed to prefetch liked events:', error);
        }
      },
      strategy.priority,
      strategy.delay,
    );
  }

  // Prefetch subcategories
  async prefetchSubcategories(strategy: PrefetchStrategy = {priority: 'low', delay: 5000}) {
    this.addToPrefetchQueue(
      async () => {
        try {
          await this.queryClient.prefetchQuery({
            queryKey: queryKeys.subcategories.all,
            queryFn: async () => {
              const token = await FirebaseAuth.getAuthToken();
              const response = await fetch(`${BASE_API_URL}subcategories`, {
                method: 'GET',
                headers: {Authorization: token, Accept: 'application/json'},
              });
              return response.json();
            },
            staleTime: 1000 * 60 * 60, // 1 hour
          });
          performanceMonitor.recordPrefetchSuccess();
        } catch (error) {
          console.warn('Failed to prefetch subcategories:', error);
        }
      },
      strategy.priority,
      strategy.delay,
    );
  }

  // Intelligent prefetching based on user behavior
  async intelligentPrefetch(userContext: {
    currentTab?: string;
    searchHistory?: string[];
    viewedEvents?: number[];
    location?: {lat: number; lng: number};
  }) {
    const {currentTab, searchHistory, viewedEvents, location} = userContext;

    // Prefetch based on current context
    if (currentTab === TABS.DISCOVER && location) {
      await this.prefetchNearbyEvents(location.lat, location.lng, 15, {priority: 'high', delay: 500});
    }

    // Prefetch based on search patterns
    if (searchHistory && searchHistory.length > 0) {
      const recentSearches = searchHistory.slice(-3);
      for (const search of recentSearches) {
        this.prefetchSearchResults(search, {priority: 'medium', delay: 2000});
      }
    }

    // Prefetch event details for recently viewed events
    if (viewedEvents && viewedEvents.length > 0) {
      const recentEvents = viewedEvents.slice(-5);
      for (const eventId of recentEvents) {
        this.prefetchEventDetails(eventId, {priority: 'low', delay: 4000});
      }
    }
  }

  // Prefetch search results
  private async prefetchSearchResults(query: string, strategy: PrefetchStrategy) {
    this.addToPrefetchQueue(
      async () => {
        try {
          await this.queryClient.prefetchQuery({
            queryKey: queryKeys.events.search(query),
            queryFn: async () => {
              // Implement search API call
              const token = await FirebaseAuth.getAuthToken();
              const response = await fetch(`${BASE_API_URL}events/search?q=${encodeURIComponent(query)}`, {
                method: 'GET',
                headers: {Authorization: token, Accept: 'application/json'},
              });
              return response.json();
            },
            staleTime: 1000 * 60 * 5,
          });
          performanceMonitor.recordPrefetchSuccess();
        } catch (error) {
          console.warn(`Failed to prefetch search results for "${query}":`, error);
        }
      },
      strategy.priority,
      strategy.delay,
    );
  }

  // Prefetch event details
  private async prefetchEventDetails(eventId: number, strategy: PrefetchStrategy) {
    this.addToPrefetchQueue(
      async () => {
        try {
          await this.queryClient.prefetchQuery({
            queryKey: queryKeys.events.detail(eventId),
            queryFn: async () => {
              const token = await FirebaseAuth.getAuthToken();
              const response = await fetch(`${BASE_API_URL}events/${eventId}`, {
                method: 'GET',
                headers: {Authorization: token, Accept: 'application/json'},
              });
              return response.json();
            },
            staleTime: 1000 * 60 * 10,
          });
          performanceMonitor.recordPrefetchSuccess();
        } catch (error) {
          console.warn(`Failed to prefetch event details for ${eventId}:`, error);
        }
      },
      strategy.priority,
      strategy.delay,
    );
  }

  // Add task to prefetch queue
  private addToPrefetchQueue(fn: () => Promise<void>, priority: string, delay: number) {
    this.prefetchQueue.push({fn, priority, delay});
    this.prefetchQueue.sort((a, b) => {
      const priorityOrder = {high: 0, medium: 1, low: 2};
      return (
        priorityOrder[a.priority as keyof typeof priorityOrder] -
        priorityOrder[b.priority as keyof typeof priorityOrder]
      );
    });
  }

  // Background processing of prefetch queue
  private async startBackgroundProcessing() {
    if (this.isProcessing) {
      return;
    }
    this.isProcessing = true;

    while (this.prefetchQueue.length > 0) {
      const task = this.prefetchQueue.shift();
      if (task) {
        await new Promise(resolve => setTimeout(resolve, task.delay));
        try {
          await task.fn();
        } catch (error) {
          console.warn('Prefetch task failed:', error);
        }
      }
    }

    this.isProcessing = false;

    // Restart processing if new tasks were added
    if (this.prefetchQueue.length > 0) {
      setTimeout(() => this.startBackgroundProcessing(), 1000);
    }
  }

  // Clear prefetch queue
  clearQueue() {
    this.prefetchQueue = [];
  }

  // Get queue status
  getQueueStatus() {
    return {
      queueLength: this.prefetchQueue.length,
      isProcessing: this.isProcessing,
      tasksByPriority: {
        high: this.prefetchQueue.filter(t => t.priority === 'high').length,
        medium: this.prefetchQueue.filter(t => t.priority === 'medium').length,
        low: this.prefetchQueue.filter(t => t.priority === 'low').length,
      },
    };
  }
}

export default PrefetchService;
