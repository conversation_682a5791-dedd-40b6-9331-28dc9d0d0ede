import AsyncStorage from '@react-native-async-storage/async-storage';
import {ChatType<PERSON><PERSON><PERSON>ey, ConciergeChatTypeWithKey, ChatMessageType} from '~types/chat';
import {MessageStatus} from '~constants/messageStatus';

// Storage keys
const STORAGE_KEYS = {
  CHATS: '@offline_chats',
  UNSENT_MESSAGES: '@unsent_messages',
  LAST_SYNC: '@last_sync_timestamp',
} as const;

// Types for offline storage
export interface UnsentMessage {
  id: string;
  chatId: string;
  text: string;
  timestamp: string;
  tempMessageId: string;
  userId: string;
  userName: string;
  userImage?: string;
  retryCount: number;
  status: MessageStatus;
  lastRetryAttempt?: string;
  isBeingSent?: boolean; // Flag to prevent concurrent sending
}

export interface OfflineChatData {
  chats: (ChatTypeWithKey | ConciergeChatTypeWithKey)[];
  lastUpdated: string;
}

class OfflineChatStorageService {
  private static instance: OfflineChatStorageService;
  private sendingMessages = new Set<string>(); // Track messages currently being sent
  private readonly MAX_RETRY_COUNT = 3;
  private readonly RETRY_DELAY_BASE = 1000; // Base delay in ms

  public static getInstance(): OfflineChatStorageService {
    if (!OfflineChatStorageService.instance) {
      OfflineChatStorageService.instance = new OfflineChatStorageService();
    }
    return OfflineChatStorageService.instance;
  }

  // ============================================================================
  // CHAT STORAGE METHODS
  // ============================================================================

  /**
   * Store chats locally for offline access
   */
  async storeChats(chats: (ChatTypeWithKey | ConciergeChatTypeWithKey)[]): Promise<void> {
    try {
      const offlineData: OfflineChatData = {
        chats,
        lastUpdated: new Date().toISOString(),
      };

      await AsyncStorage.setItem(STORAGE_KEYS.CHATS, JSON.stringify(offlineData));
      console.log('✅ OfflineChatStorage: Chats stored locally', {count: chats.length});
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to store chats', error);
    }
  }

  /**
   * Retrieve stored chats for offline viewing
   */
  async getStoredChats(): Promise<(ChatTypeWithKey | ConciergeChatTypeWithKey)[]> {
    try {
      const storedData = await AsyncStorage.getItem(STORAGE_KEYS.CHATS);
      if (!storedData) {
        return [];
      }

      const offlineData: OfflineChatData = JSON.parse(storedData);
      console.log('📱 OfflineChatStorage: Retrieved stored chats', {
        count: offlineData.chats.length,
        lastUpdated: offlineData.lastUpdated,
      });

      return offlineData.chats;
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to retrieve stored chats', error);
      return [];
    }
  }

  /**
   * Get a specific chat by ID from local storage
   */
  async getStoredChat(chatId: string): Promise<ChatTypeWithKey | ConciergeChatTypeWithKey | null> {
    try {
      const chats = await this.getStoredChats();
      const chat = chats.find(c => c.key === chatId);
      return chat || null;
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to get stored chat', error);
      return null;
    }
  }

  // ============================================================================
  // UNSENT MESSAGES METHODS
  // ============================================================================

  /**
   * Store an unsent message for later retry
   */
  async storeUnsentMessage(
    message: Omit<UnsentMessage, 'id' | 'retryCount' | 'status' | 'lastRetryAttempt' | 'isBeingSent'>,
  ): Promise<string> {
    try {
      const messageId = `unsent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Check for duplicate messages by tempMessageId to prevent duplicates
      const existingMessages = await this.getUnsentMessages();
      const duplicateMessage = existingMessages.find(msg => msg.tempMessageId === message.tempMessageId);

      if (duplicateMessage) {
        console.log('📤 OfflineChatStorage: Duplicate message detected, skipping storage', {
          tempMessageId: message.tempMessageId,
        });
        return duplicateMessage.id;
      }

      const unsentMessage: UnsentMessage = {
        ...message,
        id: messageId,
        retryCount: 0,
        status: 'pending',
        lastRetryAttempt: undefined,
        isBeingSent: false,
      };

      const updatedMessages = [...existingMessages, unsentMessage];

      await AsyncStorage.setItem(STORAGE_KEYS.UNSENT_MESSAGES, JSON.stringify(updatedMessages));
      console.log('📤 OfflineChatStorage: Unsent message stored', {messageId, chatId: message.chatId});

      return messageId;
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to store unsent message', error);
      throw error;
    }
  }

  /**
   * Get all unsent messages
   */
  async getUnsentMessages(): Promise<UnsentMessage[]> {
    try {
      const storedData = await AsyncStorage.getItem(STORAGE_KEYS.UNSENT_MESSAGES);
      if (!storedData) {
        return [];
      }

      const messages: UnsentMessage[] = JSON.parse(storedData);
      return messages;
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to retrieve unsent messages', error);
      return [];
    }
  }

  /**
   * Get unsent messages for a specific chat
   */
  async getUnsentMessagesForChat(chatId: string): Promise<UnsentMessage[]> {
    try {
      const allUnsent = await this.getUnsentMessages();
      return allUnsent.filter(msg => msg.chatId === chatId);
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to get unsent messages for chat', error);
      return [];
    }
  }

  /**
   * Update unsent message status
   */
  async updateUnsentMessageStatus(messageId: string, status: MessageStatus, incrementRetry = false): Promise<void> {
    try {
      const messages = await this.getUnsentMessages();
      const updatedMessages = messages.map(msg => {
        if (msg.id === messageId) {
          return {
            ...msg,
            status,
            retryCount: incrementRetry ? msg.retryCount + 1 : msg.retryCount,
            lastRetryAttempt: incrementRetry ? new Date().toISOString() : msg.lastRetryAttempt,
            isBeingSent: status === 'sending',
          };
        }
        return msg;
      });

      await AsyncStorage.setItem(STORAGE_KEYS.UNSENT_MESSAGES, JSON.stringify(updatedMessages));
      console.log('🔄 OfflineChatStorage: Updated message status', {messageId, status});
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to update message status', error);
    }
  }

  /**
   * Check if a message is currently being sent to prevent concurrent sends
   */
  async isMessageBeingSent(tempMessageId: string): Promise<boolean> {
    try {
      const messages = await this.getUnsentMessages();
      const message = messages.find(msg => msg.tempMessageId === tempMessageId);
      return message?.isBeingSent || false;
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to check message sending status', error);
      return false;
    }
  }

  /**
   * Get messages that are ready for retry (not currently being sent and within retry limits)
   */
  async getMessagesReadyForRetry(chatId?: string): Promise<UnsentMessage[]> {
    try {
      const messages = await this.getUnsentMessages();
      const now = new Date().getTime();

      return messages.filter(msg => {
        // Filter by chat if specified
        if (chatId && msg.chatId !== chatId) return false;

        // Skip if currently being sent
        if (msg.isBeingSent) return false;

        // Skip if exceeded retry count
        if (msg.retryCount >= this.MAX_RETRY_COUNT) return false;

        // Skip if recently retried (exponential backoff)
        if (msg.lastRetryAttempt) {
          const lastRetryTime = new Date(msg.lastRetryAttempt).getTime();
          const backoffDelay = this.RETRY_DELAY_BASE * Math.pow(2, msg.retryCount);
          if (now - lastRetryTime < backoffDelay) return false;
        }

        return msg.status === 'pending' || msg.status === 'failed';
      });
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to get messages ready for retry', error);
      return [];
    }
  }

  /**
   * Remove an unsent message (after successful send)
   */
  async removeUnsentMessage(messageId: string): Promise<void> {
    try {
      const messages = await this.getUnsentMessages();
      const filteredMessages = messages.filter(msg => msg.id !== messageId);

      await AsyncStorage.setItem(STORAGE_KEYS.UNSENT_MESSAGES, JSON.stringify(filteredMessages));
      console.log('✅ OfflineChatStorage: Removed unsent message', {messageId});
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to remove unsent message', error);
    }
  }

  /**
   * Remove an unsent message by tempMessageId (after successful send)
   */
  async removeUnsentMessageByTempId(tempMessageId: string): Promise<void> {
    try {
      const messages = await this.getUnsentMessages();
      const filteredMessages = messages.filter(msg => msg.tempMessageId !== tempMessageId);

      await AsyncStorage.setItem(STORAGE_KEYS.UNSENT_MESSAGES, JSON.stringify(filteredMessages));
      console.log('✅ OfflineChatStorage: Removed unsent message by tempId', {tempMessageId});
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to remove unsent message by tempId', error);
    }
  }

  /**
   * Clear all unsent messages (for testing or reset)
   */
  async clearUnsentMessages(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.UNSENT_MESSAGES);
      console.log('🗑️ OfflineChatStorage: Cleared all unsent messages');
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to clear unsent messages', error);
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Check if we have offline data available
   */
  async hasOfflineData(): Promise<boolean> {
    try {
      const chatsData = await AsyncStorage.getItem(STORAGE_KEYS.CHATS);
      return !!chatsData;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get last sync timestamp
   */
  async getLastSyncTimestamp(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC);
    } catch (error) {
      return null;
    }
  }

  /**
   * Update last sync timestamp
   */
  async updateLastSyncTimestamp(): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to update sync timestamp', error);
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<{
    hasChats: boolean;
    hasUnsentMessages: boolean;
    chatCount: number;
    unsentMessageCount: number;
    lastSync: string | null;
  }> {
    try {
      const [chatsData, unsentMessages, lastSync] = await Promise.all([
        AsyncStorage.getItem(STORAGE_KEYS.CHATS),
        this.getUnsentMessages(),
        this.getLastSyncTimestamp(),
      ]);

      let chatCount = 0;
      if (chatsData) {
        try {
          const offlineData: OfflineChatData = JSON.parse(chatsData);
          chatCount = offlineData.chats.length;
        } catch (error) {
          console.error('❌ OfflineChatStorage: Failed to parse chat data for stats', error);
        }
      }

      return {
        hasChats: !!chatsData && chatCount > 0,
        hasUnsentMessages: unsentMessages.length > 0,
        chatCount,
        unsentMessageCount: unsentMessages.length,
        lastSync,
      };
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to get storage stats', error);
      return {
        hasChats: false,
        hasUnsentMessages: false,
        chatCount: 0,
        unsentMessageCount: 0,
        lastSync: null,
      };
    }
  }

  /**
   * Alias for getLastSyncTimestamp for consistency with other storage services
   */
  async getLastSync(): Promise<string | null> {
    return this.getLastSyncTimestamp();
  }

  /**
   * Clear all offline data
   */
  async clearAllData(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(STORAGE_KEYS.CHATS),
        AsyncStorage.removeItem(STORAGE_KEYS.UNSENT_MESSAGES),
        AsyncStorage.removeItem(STORAGE_KEYS.LAST_SYNC),
      ]);
      console.log('🗑️ OfflineChatStorage: Cleared all offline data');
    } catch (error) {
      console.error('❌ OfflineChatStorage: Failed to clear offline data', error);
    }
  }
}

export default OfflineChatStorageService.getInstance();
