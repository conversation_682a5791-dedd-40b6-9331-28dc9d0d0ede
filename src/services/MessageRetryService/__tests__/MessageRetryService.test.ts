import MessageRetryService from '../MessageRetryService';
import OfflineChatStorage from '../../OfflineChatStorage/OfflineChatStorage';

// Mock dependencies
jest.mock('../../OfflineChatStorage/OfflineChatStorage');
jest.mock('@react-native-community/netinfo', () => ({
  addEventListener: jest.fn(() => jest.fn()),
}));

describe('MessageRetryService', () => {
  let retryService: MessageRetryService;
  let mockSendMessage: jest.Mock;

  beforeEach(() => {
    retryService = MessageRetryService.getInstance();
    mockSendMessage = jest.fn();
    jest.clearAllMocks();
  });

  afterEach(() => {
    retryService.cleanup();
  });

  describe('Duplicate Prevention', () => {
    it('should prevent concurrent retry attempts for the same chat', async () => {
      const mockMessages = [
        {
          id: 'msg1',
          chatId: 'chat1',
          text: 'Hello',
          tempMessageId: 'temp1',
          userId: 'user1',
          userName: 'User 1',
          retryCount: 0,
          status: 'pending' as const,
          timestamp: new Date().toISOString(),
          isBeingSent: false,
        },
      ];

      (OfflineChatStorage.getMessagesReadyForRetry as jest.Mock).mockResolvedValue(mockMessages);
      (OfflineChatStorage.isMessageBeingSent as jest.Mock).mockResolvedValue(false);
      mockSendMessage.mockResolvedValue('real-msg-id');

      // Start two concurrent retry attempts
      const promise1 = retryService.retryChatMessages('chat1', mockSendMessage);
      const promise2 = retryService.retryChatMessages('chat1', mockSendMessage);

      const [result1, result2] = await Promise.all([promise1, promise2]);

      // Only one should succeed, the other should be skipped
      expect(result1.successCount + result2.successCount).toBe(1);
      expect(result1.failureCount + result2.failureCount).toBe(0);
    });

    it('should skip messages that are already being sent', async () => {
      const mockMessages = [
        {
          id: 'msg1',
          chatId: 'chat1',
          text: 'Hello',
          tempMessageId: 'temp1',
          userId: 'user1',
          userName: 'User 1',
          retryCount: 0,
          status: 'pending' as const,
          timestamp: new Date().toISOString(),
          isBeingSent: true, // Already being sent
        },
      ];

      (OfflineChatStorage.getMessagesReadyForRetry as jest.Mock).mockResolvedValue(mockMessages);
      (OfflineChatStorage.isMessageBeingSent as jest.Mock).mockResolvedValue(true);

      const result = await retryService.retryChatMessages('chat1', mockSendMessage);

      expect(result.successCount).toBe(0);
      expect(result.failureCount).toBe(0);
      expect(mockSendMessage).not.toHaveBeenCalled();
    });
  });

  describe('Retry Logic', () => {
    it('should successfully retry and remove messages from storage', async () => {
      const mockMessages = [
        {
          id: 'msg1',
          chatId: 'chat1',
          text: 'Hello',
          tempMessageId: 'temp1',
          userId: 'user1',
          userName: 'User 1',
          retryCount: 0,
          status: 'pending' as const,
          timestamp: new Date().toISOString(),
          isBeingSent: false,
        },
      ];

      (OfflineChatStorage.getMessagesReadyForRetry as jest.Mock).mockResolvedValue(mockMessages);
      (OfflineChatStorage.isMessageBeingSent as jest.Mock).mockResolvedValue(false);
      (OfflineChatStorage.removeUnsentMessageByTempId as jest.Mock).mockResolvedValue(undefined);
      mockSendMessage.mockResolvedValue('real-msg-id');

      const mockCallbacks = {
        onMessageStatusChange: jest.fn(),
        onOfflineMessagesSent: jest.fn(),
      };

      const result = await retryService.retryChatMessages('chat1', mockSendMessage, mockCallbacks);

      expect(result.successCount).toBe(1);
      expect(result.failureCount).toBe(0);
      expect(OfflineChatStorage.removeUnsentMessageByTempId).toHaveBeenCalledWith('temp1');
      expect(mockCallbacks.onMessageStatusChange).toHaveBeenCalledWith('temp1', 'sending');
      expect(mockCallbacks.onMessageStatusChange).toHaveBeenCalledWith('temp1', 'sent', 'real-msg-id');
      expect(mockCallbacks.onOfflineMessagesSent).toHaveBeenCalledWith(1, 0);
    });

    it('should handle failed retries and update status', async () => {
      const mockMessages = [
        {
          id: 'msg1',
          chatId: 'chat1',
          text: 'Hello',
          tempMessageId: 'temp1',
          userId: 'user1',
          userName: 'User 1',
          retryCount: 0,
          status: 'pending' as const,
          timestamp: new Date().toISOString(),
          isBeingSent: false,
        },
      ];

      (OfflineChatStorage.getMessagesReadyForRetry as jest.Mock).mockResolvedValue(mockMessages);
      (OfflineChatStorage.isMessageBeingSent as jest.Mock).mockResolvedValue(false);
      (OfflineChatStorage.updateUnsentMessageStatus as jest.Mock).mockResolvedValue(undefined);
      mockSendMessage.mockRejectedValue(new Error('Network error'));

      const mockCallbacks = {
        onMessageStatusChange: jest.fn(),
        onOfflineMessagesSent: jest.fn(),
      };

      const result = await retryService.retryChatMessages('chat1', mockSendMessage, mockCallbacks);

      expect(result.successCount).toBe(0);
      expect(result.failureCount).toBe(1);
      expect(OfflineChatStorage.updateUnsentMessageStatus).toHaveBeenCalledWith('msg1', 'failed', true);
      expect(mockCallbacks.onMessageStatusChange).toHaveBeenCalledWith('temp1', 'failed');
      expect(mockCallbacks.onOfflineMessagesSent).toHaveBeenCalledWith(0, 1);
    });
  });

  describe('Service Management', () => {
    it('should initialize and cleanup properly', () => {
      expect(() => {
        retryService.initialize();
        retryService.cleanup();
      }).not.toThrow();
    });

    it('should track retry status correctly', () => {
      expect(retryService.isCurrentlyRetrying()).toBe(false);
      expect(retryService.getPendingRetryCount()).toBe(0);
    });
  });
});
