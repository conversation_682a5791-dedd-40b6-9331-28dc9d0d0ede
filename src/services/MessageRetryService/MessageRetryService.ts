import NetInfo from '@react-native-community/netinfo';
import OfflineChatStorage, {UnsentMessage} from '../OfflineChatStorage/OfflineChatStorage';
import {MessageStatus} from '~constants/messageStatus';

interface RetryCallbacks {
  onMessageStatusChange?: (tempId: string, status: MessageStatus, realMessageId?: string) => void;
  onOfflineMessagesSent?: (successCount: number, failureCount: number) => void;
}

interface SendMessageFunction {
  (text: string, tempMessageId?: string): Promise<string>;
}

class MessageRetryService {
  private static instance: MessageRetryService;
  private isRetrying = false;
  private retryTimeouts = new Map<string, NodeJS.Timeout>();
  private networkListener: (() => void) | null = null;

  static getInstance(): MessageRetryService {
    if (!MessageRetryService.instance) {
      MessageRetryService.instance = new MessageRetryService();
    }
    return MessageRetryService.instance;
  }

  /**
   * Initialize the service with network monitoring
   */
  initialize() {
    if (this.networkListener) {
      return; // Already initialized
    }

    this.networkListener = NetInfo.addEventListener(state => {
      const isOnline = !!(state.isConnected && state.isInternetReachable);
      
      if (isOnline && !this.isRetrying) {
        // Debounce network state changes to prevent multiple retry attempts
        setTimeout(() => {
          this.retryAllUnsentMessages();
        }, 1000);
      }
    });

    console.log('📡 MessageRetryService: Network monitoring initialized');
  }

  /**
   * Cleanup the service
   */
  cleanup() {
    if (this.networkListener) {
      this.networkListener();
      this.networkListener = null;
    }

    // Clear all pending timeouts
    this.retryTimeouts.forEach(timeout => clearTimeout(timeout));
    this.retryTimeouts.clear();

    console.log('📡 MessageRetryService: Cleanup completed');
  }

  /**
   * Retry unsent messages for a specific chat
   */
  async retryChatMessages(
    chatId: string,
    sendMessageFn: SendMessageFunction,
    callbacks?: RetryCallbacks
  ): Promise<{successCount: number; failureCount: number}> {
    if (this.isRetrying) {
      console.log('🔄 MessageRetryService: Retry already in progress, skipping');
      return {successCount: 0, failureCount: 0};
    }

    try {
      this.isRetrying = true;
      console.log('🔄 MessageRetryService: Starting retry for chat:', chatId);

      const messagesToRetry = await OfflineChatStorage.getMessagesReadyForRetry(chatId);
      
      if (messagesToRetry.length === 0) {
        console.log('📤 MessageRetryService: No messages ready for retry');
        return {successCount: 0, failureCount: 0};
      }

      console.log(`📤 MessageRetryService: Found ${messagesToRetry.length} messages ready for retry`);

      let successCount = 0;
      let failureCount = 0;

      // Process messages sequentially to avoid overwhelming the server
      for (const message of messagesToRetry) {
        try {
          // Check if message is already being sent by another process
          const isBeingSent = await OfflineChatStorage.isMessageBeingSent(message.tempMessageId);
          if (isBeingSent) {
            console.log('⏭️ MessageRetryService: Message already being sent, skipping:', message.tempMessageId);
            continue;
          }

          console.log('📤 MessageRetryService: Retrying message:', message.tempMessageId);

          // Mark as sending
          await OfflineChatStorage.updateUnsentMessageStatus(message.id, 'sending');
          callbacks?.onMessageStatusChange?.(message.tempMessageId, 'sending');

          // Attempt to send
          const realMessageId = await sendMessageFn(message.text, message.tempMessageId);

          if (realMessageId) {
            // Success - remove from storage
            await OfflineChatStorage.removeUnsentMessageByTempId(message.tempMessageId);
            callbacks?.onMessageStatusChange?.(message.tempMessageId, 'sent', realMessageId);
            successCount++;
            console.log('✅ MessageRetryService: Successfully sent message:', message.tempMessageId);
          } else {
            throw new Error('No message ID returned');
          }
        } catch (error) {
          console.error('❌ MessageRetryService: Failed to send message:', message.tempMessageId, error);
          
          // Update status with retry increment
          await OfflineChatStorage.updateUnsentMessageStatus(message.id, 'failed', true);
          callbacks?.onMessageStatusChange?.(message.tempMessageId, 'failed');
          failureCount++;

          // Schedule next retry if within limits
          this.scheduleRetry(message, sendMessageFn, callbacks);
        }
      }

      // Notify about results
      if (successCount > 0 || failureCount > 0) {
        callbacks?.onOfflineMessagesSent?.(successCount, failureCount);
        console.log(`📤 MessageRetryService: Retry completed - ${successCount} success, ${failureCount} failed`);
      }

      return {successCount, failureCount};
    } catch (error) {
      console.error('❌ MessageRetryService: Error during retry:', error);
      return {successCount: 0, failureCount: 0};
    } finally {
      this.isRetrying = false;
    }
  }

  /**
   * Retry all unsent messages across all chats
   */
  private async retryAllUnsentMessages() {
    try {
      const allMessages = await OfflineChatStorage.getMessagesReadyForRetry();
      
      if (allMessages.length === 0) {
        return;
      }

      console.log(`📤 MessageRetryService: Found ${allMessages.length} total messages ready for retry`);

      // Group messages by chat
      const messagesByChat = allMessages.reduce((acc, message) => {
        if (!acc[message.chatId]) {
          acc[message.chatId] = [];
        }
        acc[message.chatId].push(message);
        return acc;
      }, {} as Record<string, UnsentMessage[]>);

      // Note: We can't retry here without the specific sendMessageFn for each chat
      // This method is mainly for logging and monitoring
      console.log(`📤 MessageRetryService: Messages pending across ${Object.keys(messagesByChat).length} chats`);
    } catch (error) {
      console.error('❌ MessageRetryService: Error checking all unsent messages:', error);
    }
  }

  /**
   * Schedule a retry for a failed message with exponential backoff
   */
  private scheduleRetry(
    message: UnsentMessage,
    sendMessageFn: SendMessageFunction,
    callbacks?: RetryCallbacks
  ) {
    const maxRetries = 3;
    if (message.retryCount >= maxRetries) {
      console.log('❌ MessageRetryService: Max retries reached for message:', message.tempMessageId);
      return;
    }

    const baseDelay = 1000; // 1 second
    const delay = baseDelay * Math.pow(2, message.retryCount); // Exponential backoff

    console.log(`⏰ MessageRetryService: Scheduling retry for message ${message.tempMessageId} in ${delay}ms`);

    const timeoutId = setTimeout(async () => {
      try {
        await this.retryChatMessages(message.chatId, sendMessageFn, callbacks);
      } catch (error) {
        console.error('❌ MessageRetryService: Scheduled retry failed:', error);
      } finally {
        this.retryTimeouts.delete(message.tempMessageId);
      }
    }, delay);

    this.retryTimeouts.set(message.tempMessageId, timeoutId);
  }

  /**
   * Cancel scheduled retry for a message
   */
  cancelRetry(tempMessageId: string) {
    const timeout = this.retryTimeouts.get(tempMessageId);
    if (timeout) {
      clearTimeout(timeout);
      this.retryTimeouts.delete(tempMessageId);
      console.log('⏹️ MessageRetryService: Cancelled retry for message:', tempMessageId);
    }
  }

  /**
   * Get retry status
   */
  isCurrentlyRetrying(): boolean {
    return this.isRetrying;
  }

  /**
   * Get pending retry count
   */
  getPendingRetryCount(): number {
    return this.retryTimeouts.size;
  }
}

export default MessageRetryService;
