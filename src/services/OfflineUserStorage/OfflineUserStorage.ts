import AsyncStorage from '@react-native-async-storage/async-storage';
import {User} from '~types/api/user';
import {Business} from '~types/api/business';

// Storage keys
const STORAGE_KEYS = {
  USER_DATA: '@offline_user_data',
  BUSINESS_DATA: '@offline_business_data',
  USER_TYPE: '@offline_user_type',
  AUTH_STATE: '@offline_auth_state',
  LAST_SYNC: '@offline_user_last_sync',
} as const;

// Types for offline storage
export interface OfflineUserData {
  user: Omit<User, 'subcategories' | 'groups'> | null;
  lastUpdated: string;
  syncStatus: 'synced' | 'pending' | 'failed';
}

export interface OfflineBusinessData {
  business: Business | null;
  lastUpdated: string;
  syncStatus: 'synced' | 'pending' | 'failed';
}

export interface OfflineAuthState {
  isAuthenticated: boolean;
  userId: string | null;
  userType: 'personal' | 'business' | null;
  lastUpdated: string;
}

class OfflineUserStorageService {
  private static instance: OfflineUserStorageService;

  static getInstance(): OfflineUserStorageService {
    if (!OfflineUserStorageService.instance) {
      OfflineUserStorageService.instance = new OfflineUserStorageService();
    }
    return OfflineUserStorageService.instance;
  }

  // ============================================================================
  // USER DATA STORAGE METHODS
  // ============================================================================

  /**
   * Store user data locally for offline access
   */
  async storeUserData(user: Omit<User, 'subcategories' | 'groups'>): Promise<void> {
    try {
      const offlineData: OfflineUserData = {
        user,
        lastUpdated: new Date().toISOString(),
        syncStatus: 'synced',
      };

      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(offlineData));
      console.log('✅ OfflineUserStorage: User data stored locally', {userId: user.uid});
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to store user data', error);
    }
  }

  /**
   * Retrieve stored user data for offline access
   */
  async getStoredUserData(): Promise<Omit<User, 'subcategories' | 'groups'> | null> {
    try {
      const storedData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      if (!storedData) {
        return null;
      }

      const offlineData: OfflineUserData = JSON.parse(storedData);
      console.log('📱 OfflineUserStorage: Retrieved stored user data', {
        userId: offlineData.user?.uid,
        lastUpdated: offlineData.lastUpdated,
        syncStatus: offlineData.syncStatus,
      });

      return offlineData.user;
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to retrieve stored user data', error);
      return null;
    }
  }

  // ============================================================================
  // BUSINESS DATA STORAGE METHODS
  // ============================================================================

  /**
   * Store business data locally for offline access
   */
  async storeBusinessData(business: Business): Promise<void> {
    try {
      const offlineData: OfflineBusinessData = {
        business,
        lastUpdated: new Date().toISOString(),
        syncStatus: 'synced',
      };

      await AsyncStorage.setItem(STORAGE_KEYS.BUSINESS_DATA, JSON.stringify(offlineData));
      console.log('✅ OfflineUserStorage: Business data stored locally', {businessId: business.uid});
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to store business data', error);
    }
  }

  /**
   * Retrieve stored business data for offline access
   */
  async getStoredBusinessData(): Promise<Business | null> {
    try {
      const storedData = await AsyncStorage.getItem(STORAGE_KEYS.BUSINESS_DATA);
      if (!storedData) {
        return null;
      }

      const offlineData: OfflineBusinessData = JSON.parse(storedData);
      console.log('📱 OfflineUserStorage: Retrieved stored business data', {
        businessId: offlineData.business?.uid,
        lastUpdated: offlineData.lastUpdated,
        syncStatus: offlineData.syncStatus,
      });

      return offlineData.business;
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to retrieve stored business data', error);
      return null;
    }
  }

  // ============================================================================
  // USER TYPE STORAGE METHODS
  // ============================================================================

  /**
   * Store user type locally
   */
  async storeUserType(userType: 'personal' | 'business'): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_TYPE, userType);
      console.log('✅ OfflineUserStorage: User type stored locally', {userType});
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to store user type', error);
    }
  }

  /**
   * Retrieve stored user type
   */
  async getStoredUserType(): Promise<'personal' | 'business' | null> {
    try {
      const userType = await AsyncStorage.getItem(STORAGE_KEYS.USER_TYPE);
      console.log('📱 OfflineUserStorage: Retrieved stored user type', {userType});
      return userType as 'personal' | 'business' | null;
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to retrieve stored user type', error);
      return null;
    }
  }

  // ============================================================================
  // AUTH STATE STORAGE METHODS
  // ============================================================================

  /**
   * Store authentication state locally
   */
  async storeAuthState(authState: Omit<OfflineAuthState, 'lastUpdated'>): Promise<void> {
    try {
      const offlineAuthState: OfflineAuthState = {
        ...authState,
        lastUpdated: new Date().toISOString(),
      };

      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_STATE, JSON.stringify(offlineAuthState));
      console.log('✅ OfflineUserStorage: Auth state stored locally', {
        isAuthenticated: authState.isAuthenticated,
        userId: authState.userId,
        userType: authState.userType,
      });
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to store auth state', error);
    }
  }

  /**
   * Retrieve stored authentication state
   */
  async getStoredAuthState(): Promise<OfflineAuthState | null> {
    try {
      const storedData = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_STATE);
      if (!storedData) {
        return null;
      }

      const authState: OfflineAuthState = JSON.parse(storedData);
      console.log('📱 OfflineUserStorage: Retrieved stored auth state', {
        isAuthenticated: authState.isAuthenticated,
        userId: authState.userId,
        userType: authState.userType,
        lastUpdated: authState.lastUpdated,
      });

      return authState;
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to retrieve stored auth state', error);
      return null;
    }
  }

  // ============================================================================
  // SYNC MANAGEMENT METHODS
  // ============================================================================

  /**
   * Mark data as needing sync
   */
  async markForSync(dataType: 'user' | 'business'): Promise<void> {
    try {
      const storageKey = dataType === 'user' ? STORAGE_KEYS.USER_DATA : STORAGE_KEYS.BUSINESS_DATA;
      const storedData = await AsyncStorage.getItem(storageKey);
      
      if (storedData) {
        const data = JSON.parse(storedData);
        data.syncStatus = 'pending';
        await AsyncStorage.setItem(storageKey, JSON.stringify(data));
        console.log(`🔄 OfflineUserStorage: Marked ${dataType} data for sync`);
      }
    } catch (error) {
      console.error(`❌ OfflineUserStorage: Failed to mark ${dataType} data for sync`, error);
    }
  }

  /**
   * Update last sync timestamp
   */
  async updateLastSync(): Promise<void> {
    try {
      const timestamp = new Date().toISOString();
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC, timestamp);
      console.log('✅ OfflineUserStorage: Updated last sync timestamp', {timestamp});
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to update last sync timestamp', error);
    }
  }

  /**
   * Get last sync timestamp
   */
  async getLastSync(): Promise<string | null> {
    try {
      const timestamp = await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC);
      return timestamp;
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to get last sync timestamp', error);
      return null;
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Check if data is stale (older than specified minutes)
   */
  isDataStale(lastUpdated: string, maxAgeMinutes: number = 30): boolean {
    const now = new Date().getTime();
    const dataTime = new Date(lastUpdated).getTime();
    const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds
    
    return (now - dataTime) > maxAge;
  }

  /**
   * Clear all offline user data
   */
  async clearAllData(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA),
        AsyncStorage.removeItem(STORAGE_KEYS.BUSINESS_DATA),
        AsyncStorage.removeItem(STORAGE_KEYS.USER_TYPE),
        AsyncStorage.removeItem(STORAGE_KEYS.AUTH_STATE),
        AsyncStorage.removeItem(STORAGE_KEYS.LAST_SYNC),
      ]);
      console.log('🗑️ OfflineUserStorage: Cleared all offline user data');
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to clear offline user data', error);
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<{
    hasUserData: boolean;
    hasBusinessData: boolean;
    hasAuthState: boolean;
    lastSync: string | null;
  }> {
    try {
      const [userData, businessData, authState, lastSync] = await Promise.all([
        AsyncStorage.getItem(STORAGE_KEYS.USER_DATA),
        AsyncStorage.getItem(STORAGE_KEYS.BUSINESS_DATA),
        AsyncStorage.getItem(STORAGE_KEYS.AUTH_STATE),
        AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC),
      ]);

      return {
        hasUserData: !!userData,
        hasBusinessData: !!businessData,
        hasAuthState: !!authState,
        lastSync,
      };
    } catch (error) {
      console.error('❌ OfflineUserStorage: Failed to get storage stats', error);
      return {
        hasUserData: false,
        hasBusinessData: false,
        hasAuthState: false,
        lastSync: null,
      };
    }
  }
}

export default OfflineUserStorageService.getInstance();
