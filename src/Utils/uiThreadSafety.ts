/**
 * UI Thread Safety Utilities
 * 
 * This module provides utilities to ensure UI updates happen on the main thread,
 * preventing Android accessibility errors and thread violations.
 */

import { Platform, InteractionManager, DeviceEventEmitter } from 'react-native';

/**
 * Ensures a callback runs on the UI thread
 * 
 * @param callback - Function to execute on UI thread
 */
export const runOnUIThread = (callback: () => void): void => {
  if (Platform.OS === 'android') {
    // Use InteractionManager to ensure we're on the main thread
    InteractionManager.runAfterInteractions(() => {
      // Double-check we're still mounted and safe to update UI
      requestAnimationFrame(callback);
    });
  } else {
    // iOS - use requestAnimationFrame to ensure main thread
    requestAnimationFrame(callback);
  }
};

/**
 * Safe DeviceEventEmitter wrapper for UI thread safety
 * 
 * @param eventName - Name of the event to emit
 * @param data - Optional data to send with the event
 */
export const safeEmit = (eventName: string, data?: any): void => {
  runOnUIThread(() => {
    DeviceEventEmitter.emit(eventName, data);
  });
};

/**
 * Safe state setter wrapper for UI thread safety
 * 
 * @param setter - State setter function
 * @param value - Value to set
 */
export const safeSetState = <T>(setter: (value: T) => void, value: T): void => {
  runOnUIThread(() => {
    setter(value);
  });
};

/**
 * Safe async operation wrapper that ensures UI updates happen on main thread
 * 
 * @param operation - Async operation to perform
 * @param onSuccess - Callback for successful completion (runs on UI thread)
 * @param onError - Callback for error handling (runs on UI thread)
 */
export const safeAsyncOperation = async <T>(
  operation: () => Promise<T>,
  onSuccess?: (result: T) => void,
  onError?: (error: Error) => void
): Promise<void> => {
  try {
    const result = await operation();
    if (onSuccess) {
      runOnUIThread(() => onSuccess(result));
    }
  } catch (error) {
    if (onError) {
      runOnUIThread(() => onError(error as Error));
    }
  }
};

/**
 * Safe map animation wrapper
 * 
 * @param mapRef - Reference to the map component
 * @param animationFn - Function that performs the map animation
 */
export const safeMapAnimation = (
  mapRef: React.RefObject<any>,
  animationFn: (map: any) => void
): void => {
  runOnUIThread(() => {
    if (mapRef.current) {
      try {
        animationFn(mapRef.current);
      } catch (error) {
        console.warn('Map animation failed:', error);
      }
    }
  });
};

/**
 * Safe BottomSheet operation wrapper
 * 
 * @param sheetRef - Reference to the BottomSheet component
 * @param operation - Function that performs the BottomSheet operation
 */
export const safeBottomSheetOperation = (
  sheetRef: React.RefObject<any>,
  operation: (sheet: any) => void
): void => {
  runOnUIThread(() => {
    if (sheetRef.current) {
      try {
        operation(sheetRef.current);
      } catch (error) {
        console.warn('BottomSheet operation failed:', error);
      }
    }
  });
};

/**
 * Safe keyboard operation wrapper
 * 
 * @param operation - Keyboard operation to perform
 */
export const safeKeyboardOperation = (operation: () => void): void => {
  runOnUIThread(operation);
};

/**
 * Creates a safe event listener that ensures callbacks run on UI thread
 * 
 * @param eventName - Name of the event to listen for
 * @param callback - Callback function to execute (will be wrapped for UI thread safety)
 * @returns Cleanup function to remove the listener
 */
export const createSafeEventListener = (
  eventName: string,
  callback: (data?: any) => void
): (() => void) => {
  const listener = DeviceEventEmitter.addListener(eventName, (data) => {
    runOnUIThread(() => callback(data));
  });

  return () => listener.remove();
};

/**
 * Safe navigation operation wrapper
 * 
 * @param navigation - Navigation object
 * @param operation - Navigation operation to perform
 */
export const safeNavigationOperation = (
  navigation: any,
  operation: (nav: any) => void
): void => {
  runOnUIThread(() => {
    try {
      operation(navigation);
    } catch (error) {
      console.warn('Navigation operation failed:', error);
    }
  });
};

/**
 * Debounced UI thread operation
 * 
 * @param callback - Function to execute
 * @param delay - Delay in milliseconds
 * @returns Cleanup function to cancel the operation
 */
export const debouncedUIOperation = (
  callback: () => void,
  delay: number = 300
): (() => void) => {
  const timeoutId = setTimeout(() => {
    runOnUIThread(callback);
  }, delay);

  return () => clearTimeout(timeoutId);
};

/**
 * Safe component update wrapper for preventing updates on unmounted components
 * 
 * @param isMountedRef - Ref that tracks if component is mounted
 * @param updateFn - Update function to execute
 * @param errorContext - Optional context for error logging
 */
export const safeComponentUpdate = (
  isMountedRef: React.RefObject<boolean>,
  updateFn: () => void,
  errorContext?: string
): void => {
  runOnUIThread(() => {
    if (isMountedRef.current) {
      try {
        updateFn();
      } catch (error) {
        if (__DEV__ && errorContext) {
          console.warn(`Component update error in ${errorContext}:`, error);
        }
      }
    } else if (__DEV__ && errorContext) {
      console.warn(`Skipping update for unmounted component: ${errorContext}`);
    }
  });
};
