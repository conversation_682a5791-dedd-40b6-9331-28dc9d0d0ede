import OneSignal from 'react-native-onesignal';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * OneSignal Helper Utilities
 * Provides functions to manage OneSignal subscription state and troubleshoot issues
 */

export interface OneSignalDeviceState {
  isPushDisabled?: boolean;
  isSubscribed?: boolean;
  userId?: string;
  pushToken?: string;
  emailUserId?: string;
  emailAddress?: string;
  smsUserId?: string;
  smsNumber?: string;
}

/**
 * Get current OneSignal device state with detailed logging
 */
export const getOneSignalDeviceState = async (): Promise<OneSignalDeviceState | null> => {
  try {
    const deviceState = await OneSignal.getDeviceState();
    console.log('📱 OneSignal Device State:', JSON.stringify(deviceState, null, 2));
    return deviceState;
  } catch (error) {
    console.error('❌ Failed to get OneSignal device state:', error);
    return null;
  }
};

/**
 * Check if user should be subscribed based on app settings
 */
export const shouldUserBeSubscribed = async (): Promise<boolean> => {
  try {
    const notificationSettings = await AsyncStorage.getItem('notifications-storage');
    if (notificationSettings) {
      const settings = JSON.parse(notificationSettings);
      return settings.state?.isNotificationsEnabled !== false;
    }
    // Default to true for new users
    return true;
  } catch (error) {
    console.error('❌ Failed to check notification settings:', error);
    return true; // Default to enabled
  }
};

/**
 * Fix OneSignal subscription state if it's incorrect
 */
export const fixOneSignalSubscription = async (): Promise<boolean> => {
  try {
    console.log('🔧 Checking OneSignal subscription state...');
    
    const deviceState = await getOneSignalDeviceState();
    const shouldBeSubscribed = await shouldUserBeSubscribed();
    
    if (!deviceState) {
      console.error('❌ Could not get device state');
      return false;
    }
    
    console.log(`📊 Should be subscribed: ${shouldBeSubscribed}`);
    console.log(`📊 Is push disabled: ${deviceState.isPushDisabled}`);
    console.log(`📊 Is subscribed: ${deviceState.isSubscribed}`);
    
    // If user should be subscribed but push is disabled
    if (shouldBeSubscribed && deviceState.isPushDisabled) {
      console.log('🔧 Fixing: User should be subscribed but push is disabled');
      OneSignal.disablePush(false);
      
      // Wait and verify
      setTimeout(async () => {
        const newState = await getOneSignalDeviceState();
        console.log('✅ Fixed OneSignal state:', newState);
      }, 2000);
      
      return true;
    }
    
    // If user should not be subscribed but push is enabled
    if (!shouldBeSubscribed && !deviceState.isPushDisabled) {
      console.log('🔧 Fixing: User should not be subscribed but push is enabled');
      OneSignal.disablePush(true);
      return true;
    }
    
    console.log('✅ OneSignal subscription state is correct');
    return true;
    
  } catch (error) {
    console.error('❌ Failed to fix OneSignal subscription:', error);
    return false;
  }
};

/**
 * Enable OneSignal push notifications
 */
export const enableOneSignalPush = async (): Promise<boolean> => {
  try {
    console.log('🔔 Enabling OneSignal push notifications...');
    OneSignal.disablePush(false);
    
    // Update app settings
    const notificationSettings = {
      state: { isNotificationsEnabled: true }
    };
    await AsyncStorage.setItem('notifications-storage', JSON.stringify(notificationSettings));
    
    // Verify after a delay
    setTimeout(async () => {
      const deviceState = await getOneSignalDeviceState();
      if (deviceState && !deviceState.isPushDisabled) {
        console.log('✅ OneSignal push notifications enabled successfully');
      } else {
        console.error('❌ Failed to enable OneSignal push notifications');
      }
    }, 2000);
    
    return true;
  } catch (error) {
    console.error('❌ Failed to enable OneSignal push:', error);
    return false;
  }
};

/**
 * Disable OneSignal push notifications
 */
export const disableOneSignalPush = async (): Promise<boolean> => {
  try {
    console.log('🔕 Disabling OneSignal push notifications...');
    OneSignal.disablePush(true);
    
    // Update app settings
    const notificationSettings = {
      state: { isNotificationsEnabled: false }
    };
    await AsyncStorage.setItem('notifications-storage', JSON.stringify(notificationSettings));
    
    return true;
  } catch (error) {
    console.error('❌ Failed to disable OneSignal push:', error);
    return false;
  }
};

/**
 * Comprehensive OneSignal diagnostic function
 */
export const diagnoseOneSignal = async (): Promise<void> => {
  console.log('🔍 === OneSignal Diagnostic Report ===');
  
  try {
    // Get device state
    const deviceState = await getOneSignalDeviceState();
    
    // Get app settings
    const shouldBeSubscribed = await shouldUserBeSubscribed();
    
    // Get notification settings from storage
    const notificationSettings = await AsyncStorage.getItem('notifications-storage');
    console.log('📱 App notification settings:', notificationSettings);
    
    // Summary
    console.log('📊 === Diagnostic Summary ===');
    console.log(`Should be subscribed: ${shouldBeSubscribed}`);
    console.log(`Push disabled: ${deviceState?.isPushDisabled}`);
    console.log(`Is subscribed: ${deviceState?.isSubscribed}`);
    console.log(`User ID: ${deviceState?.userId || 'Not set'}`);
    console.log(`Push token: ${deviceState?.pushToken ? 'Present' : 'Missing'}`);
    
    // Recommendations
    if (shouldBeSubscribed && deviceState?.isPushDisabled) {
      console.log('⚠️ ISSUE: User should be subscribed but push is disabled');
      console.log('💡 SOLUTION: Call fixOneSignalSubscription() or enableOneSignalPush()');
    } else if (!shouldBeSubscribed && !deviceState?.isPushDisabled) {
      console.log('⚠️ ISSUE: User should not be subscribed but push is enabled');
      console.log('💡 SOLUTION: Call disableOneSignalPush()');
    } else {
      console.log('✅ OneSignal state appears correct');
    }
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
  
  console.log('🔍 === End Diagnostic Report ===');
};
