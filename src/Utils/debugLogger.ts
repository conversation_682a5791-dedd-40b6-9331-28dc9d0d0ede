// Debug logging utility that can be easily controlled and stripped in production
class DebugLogger {
  private isDevelopment: boolean;

  constructor() {
    // Check if we're in development mode - simplified to avoid Config issues
    this.isDevelopment = __DEV__;
  }

  // App startup logging
  appStartup(message: string, data?: any) {
    if (this.isDevelopment) {
      console.log(`🚀 [APP_STARTUP] ${message}`, data || '');
    }
  }

  // Navigation logging
  navigation(message: string, data?: any) {
    if (this.isDevelopment) {
      console.log(`🧭 [NAVIGATION] ${message}`, data || '');
    }
  }

  // API call logging
  api(message: string, data?: any) {
    if (this.isDevelopment) {
      console.log(`🔍 [API] ${message}`, data || '');
    }
  }

  // Authentication logging
  auth(message: string, data?: any) {
    if (this.isDevelopment) {
      console.log(`🔐 [AUTH] ${message}`, data || '');
    }
  }

  // Performance timing logging
  timing(label: string, startTime: number, data?: any) {
    if (this.isDevelopment) {
      const duration = Date.now() - startTime;
      console.log(`⏱️ [TIMING] ${label}: ${duration}ms`, data || '');
    }
  }

  // Error logging (always enabled, even in production)
  error(message: string, error?: any) {
    console.error(`❌ [ERROR] ${message}`, error || '');
  }

  // Warning logging (always enabled, even in production)
  warn(message: string, data?: any) {
    console.warn(`⚠️ [WARNING] ${message}`, data || '');
  }

  // Success logging
  success(message: string, data?: any) {
    if (this.isDevelopment) {
      console.log(`✅ [SUCCESS] ${message}`, data || '');
    }
  }

  // Loading state logging
  loading(message: string, data?: any) {
    if (this.isDevelopment) {
      console.log(`⏳ [LOADING] ${message}`, data || '');
    }
  }

  // Generic debug logging
  debug(message: string, data?: any) {
    if (this.isDevelopment) {
      console.log(`🐛 [DEBUG] ${message}`, data || '');
    }
  }

  // Performance measurement utility
  measurePerformance<T>(label: string, fn: () => T): T {
    if (this.isDevelopment) {
      const startTime = Date.now();
      const result = fn();
      this.timing(label, startTime);
      return result;
    }
    return fn();
  }

  // Async performance measurement utility
  async measureAsyncPerformance<T>(label: string, fn: () => Promise<T>): Promise<T> {
    if (this.isDevelopment) {
      const startTime = Date.now();
      const result = await fn();
      this.timing(label, startTime);
      return result;
    }
    return await fn();
  }
}

// Export singleton instance
export const debugLogger = new DebugLogger();

// Export individual methods for convenience
export const {
  appStartup,
  navigation,
  api,
  auth,
  timing,
  error,
  warn,
  success,
  loading,
  debug,
  measurePerformance,
  measureAsyncPerformance,
} = debugLogger;
