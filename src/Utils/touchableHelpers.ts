/**
 * Utilities to help with touchable component issues and debugging
 */

import {Platform} from 'react-native';

/**
 * Enhanced TouchableOpacity props for better clickability
 */
export const getEnhancedTouchableProps = () => ({
  activeOpacity: 0.7,
  delayPressIn: 0,
  delayPressOut: 0,
  delayLongPress: 500,
  accessible: true,
  accessibilityRole: 'button' as const,
});

/**
 * Enhanced Pressable props for better clickability
 */
export const getEnhancedPressableProps = (colors: any) => ({
  delayLongPress: 500,
  accessible: true,
  accessibilityRole: 'button' as const,
  android_ripple: {
    color: colors.primary + '20',
    borderless: false,
  },
});

/**
 * Debug function to log touch events
 */
export const debugTouchEvent = (componentName: string, eventType: string) => {
  if (__DEV__) {
    console.log(`🔘 [${componentName}] Touch event: ${eventType}`);
  }
};

/**
 * Safe touch handler wrapper that prevents multiple rapid taps
 */
export const createSafeTouchHandler = (handler: () => void, delay: number = 300) => {
  let lastTap = 0;
  
  return () => {
    const now = Date.now();
    if (now - lastTap < delay) {
      return; // Prevent rapid taps
    }
    lastTap = now;
    handler();
  };
};

/**
 * Props to ensure child views don't interfere with parent touch events
 */
export const getNonInterferenceProps = () => ({
  pointerEvents: 'none' as const,
});

/**
 * Props for containers that should capture all touch events
 */
export const getTouchCaptureProps = () => ({
  pointerEvents: 'box-only' as const,
});

/**
 * Accessibility props generator
 */
export const createAccessibilityProps = (label: string, hint?: string) => ({
  accessible: true,
  accessibilityRole: 'button' as const,
  accessibilityLabel: label,
  accessibilityHint: hint,
});

/**
 * Debug function to check if a component is properly touchable
 */
export const debugTouchableComponent = (componentName: string, props: any) => {
  if (__DEV__) {
    const issues = [];
    
    if (!props.onPress && !props.onLongPress) {
      issues.push('No onPress or onLongPress handler');
    }
    
    if (props.disabled) {
      issues.push('Component is disabled');
    }
    
    if (props.pointerEvents === 'none') {
      issues.push('pointerEvents is set to none');
    }
    
    if (issues.length > 0) {
      console.warn(`⚠️ [${componentName}] Potential touch issues:`, issues);
    } else {
      console.log(`✅ [${componentName}] Touch configuration looks good`);
    }
  }
};

/**
 * Platform-specific touch optimizations
 */
export const getPlatformTouchProps = () => {
  if (Platform.OS === 'android') {
    return {
      background: Platform.Version >= 21 
        ? {type: 'ripple', color: 'rgba(0,0,0,0.1)'} 
        : undefined,
    };
  }
  return {};
};

/**
 * Props for list items to ensure proper touch handling
 */
export const getListItemTouchProps = (colors: any) => ({
  ...getEnhancedTouchableProps(),
  ...createAccessibilityProps('List item'),
  style: {
    // Ensure minimum touch target size
    minHeight: 44,
    // Prevent touch conflicts
    zIndex: 1,
  },
});

/**
 * Wrapper to make any component safely touchable
 */
export const makeSafelyTouchable = (onPress: () => void, options?: {
  debounceMs?: number;
  hapticFeedback?: boolean;
}) => {
  const {debounceMs = 300, hapticFeedback = true} = options || {};
  
  return createSafeTouchHandler(() => {
    if (hapticFeedback) {
      // Import haptics dynamically to avoid circular dependencies
      import('~utils/haptics').then(({haptics}) => {
        haptics.light();
      });
    }
    onPress();
  }, debounceMs);
};
