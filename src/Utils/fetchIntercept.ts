import fetchIntercept from 'fetch-intercept';
import {BASE_API_URL} from '@env';
// Conditionally import Firebase Performance (temporarily disabled)
let perf: any = null;
try {
  // perf = require('@react-native-firebase/perf').default;
  // Temporarily disabled Firebase Performance due to dependency conflicts
  console.log('Firebase Performance monitoring temporarily disabled');
} catch (error) {
  console.log('Firebase Performance not available:', error);
}

// Define regex patterns for different cases
export const EVENTS_PATTERNS = [
  {
    regex: /^events\/group\/([^/]+)$/,
    traceName: 'events',
    placeholderUrl: '/events/group/{id}',
  }, // Matches "events/group/*"
  {
    regex: /^events\/cancel\/([^/]+)$/,
    traceName: 'events',
    placeholderUrl: '/events/cancel/{id}',
  }, // Matches "events/cancel/*"
  {
    regex: /^events\/group\/cancel\/([^/]+)$/,
    traceName: 'events',
    placeholderUrl: '/events/group/cancel/{id}',
  }, // Matches "events/group/cancel/*"
  {
    regex: /^events\/([^/]+)\/subcategories$/,
    traceName: 'events',
    placeholderUrl: '/events/{id}/subcategories',
  }, // Matches "events/*/subcategories"
  {
    regex: /^events\/([^/]+)\/subcategories\/([^/]+)\/status$/,
    traceName: 'events',
    placeholderUrl: '/events/{id}/subcategories/{id}/status',
  }, // Matches "events//subcategories//status"
  {
    regex: /^events\/([^/]+)\/subcategories\/([^/]+)$/,
    traceName: 'events',
    placeholderUrl: '/events/{id}/subcategories/{id}',
  }, // Matches "events//subcategories/"
  {
    regex: /^events\/([^/]+)\/like$/,
    traceName: 'events',
    placeholderUrl: '/events/{id}/like',
  }, // Matches "events/*/like"
  {
    regex: /^events\/([^/]+)\/comments$/,
    traceName: 'events',
    placeholderUrl: '/events/{id}/comments',
  }, // Matches "events/*/comments"
  {
    regex: /^events\/([^/]+)\/subscriptions$/,
    traceName: 'events',
    placeholderUrl: '/events/{id}/subscriptions',
  }, // Matches "events/*/subscriptions"
  {
    regex: /^events\/([^/]+)$/,
    traceName: 'events',
    placeholderUrl: '/events/{id}',
  }, // Matches "events/*"
  {
    regex: /^events$/,
    traceName: 'events',
    placeholderUrl: '/events',
  }, // Matches "events/"
];

export const BUSINESSES_PATTERNS = [
  {
    regex: /^businesses$/,
    traceName: 'businesses',
    placeholderUrl: '/businesses',
  }, // Matches "businesses/"
  {
    regex: /^businesses\/([^/]+)$/,
    traceName: 'businesses',
    placeholderUrl: '/businesses/{id}',
  }, // Matches "businesses/*"
  {
    regex: /^businesses\/([^/]+)\/target-audience$/,
    traceName: 'businesses',
    placeholderUrl: '/businesses/{id}/target-audience',
  }, // Matches "businesses/*/target-audience"
  {
    regex: /^businesses\/target-audience\/([^/]+)$/,
    traceName: 'businesses',
    placeholderUrl: '/businesses/target-audience/{id}',
  }, // Matches "businesses/target-audience/*"
];

export const USERS_PATTERNS = [
  {
    regex: /^users$/,
    traceName: 'users',
    placeholderUrl: '/users',
  }, // Matches "users"
  {
    regex: /^users\/([^/]+)$/,
    traceName: 'users',
    placeholderUrl: '/users/{id}',
  }, // Matches "users/*"
  {
    regex: /^users\/([^/]+)\/groups$/,
    traceName: 'users',
    placeholderUrl: '/users/{id}/groups',
  }, // Matches "users/*/groups"
  {
    regex: /^users\/groups\/([^/]+)$/,
    traceName: 'users',
    placeholderUrl: '/users/groups/{id}',
  }, // Matches "users/groups/*"
  {
    regex: /^users\/([^/]+)\/subcategories$/,
    traceName: 'users',
    placeholderUrl: '/users/{id}/subcategories',
  }, // Matches "users/*/subcategories"
  {
    regex: /^users\/subcategories\/([^/]+)$/,
    traceName: 'users',
    placeholderUrl: '/users/subcategories/{id}',
  }, // Matches "users/subcategories/*"
  {
    regex: /^users\/([^/]+)\/postal-code$/,
    traceName: 'users',
    placeholderUrl: '/users/{id}/postal-code',
  }, // Matches "users/*/postal-code"
];

export const CHATS_PATTERNS = [
  {
    regex: /^chats$/,
    traceName: 'chats',
    placeholderUrl: '/chats',
  }, // Matches "chats"
  {
    regex: /^chats\/user\/([^/]+)$/,
    traceName: 'chats',
    placeholderUrl: '/chats/user/{userId}',
  }, // Matches "chats/user/*"
  {
    regex: /^chats\/([^/]+)\/messages$/,
    traceName: 'chats',
    placeholderUrl: '/chats/{chatId}/messages',
  }, // Matches "chats/*/messages"
  {
    regex: /^chats\/([^/]+)\/users\/([^/]+)\/status$/,
    traceName: 'chats',
    placeholderUrl: '/chats/{chatId}/users/{userId}/status',
  }, // Matches "chats//users//status"
];

export const BASE_PATTERNS = [
  {
    regex: /^categories$/,
    traceName: 'base',
    placeholderUrl: '/categories',
  }, // Matches "categories"
  {
    regex: /^categories\/([^/]+)\/subcategories$/,
    traceName: 'base',
    placeholderUrl: '/categories/{categoryId}/subcategories',
  }, // Matches "categories/*/subcategories"
  {
    regex: /^subcategories$/,
    traceName: 'base',
    placeholderUrl: '/subcategories',
  }, // Matches "subcategories"
  {
    regex: /^groups$/,
    traceName: 'base',
    placeholderUrl: '/groups',
  }, // Matches "groups"
  {
    regex: /^accounts\/([^/]+)$/,
    traceName: 'base',
    placeholderUrl: '/accounts/{accountId}',
  }, // Matches "accounts/*"
  {
    regex: /^active-users$/,
    traceName: 'base',
    placeholderUrl: '/active-users',
  }, // Matches "active-users"
];

function getTraceDetails(url: any) {
  url = url.replace(BASE_API_URL, '');
  console.log('url', url);
  // Remove query parameters (everything after '?')
  url = url.split('?')[0];

  // Remove trailing slash if it exists
  if (url.endsWith('/')) {
    url = url.slice(0, -1);
  }

  let patterns;

  switch (true) {
    case url.startsWith('events'):
      patterns = EVENTS_PATTERNS;
      break;
    case url.startsWith('businesses'):
      patterns = BUSINESSES_PATTERNS;
      break;
    case url.startsWith('users'):
      patterns = USERS_PATTERNS;
      break;
    case url.startsWith('chats'):
      patterns = CHATS_PATTERNS;
      break;
    default:
      patterns = BASE_PATTERNS;
  }

  // Iterate over the patterns to find a match
  if (patterns) {
    for (const pattern of patterns) {
      const match = url.match(pattern.regex);
      if (match) {
        return {
          traceName: pattern.traceName,
          placeholderUrl: pattern.placeholderUrl,
        };
      }
    }
  }

  // Default return for unmatched URLs
  return {
    traceName: 'unknown_api_trace',
    placeholderUrl: url,
  };
}

// Utility function to generate a UUID
function generateUUID() {
  return 'xxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

// Create a Map to store trace details
const traceMap = new Map();

// Set a timeout to automatically remove entries after 5 minutes
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes

function cleanupOldEntries() {
  const now = Date.now();
  for (const [key, {timestamp}] of traceMap) {
    if (now - timestamp > CLEANUP_INTERVAL) {
      traceMap.delete(key);
    }
  }
}

// Run cleanup periodically
setInterval(cleanupOldEntries, CLEANUP_INTERVAL);

fetchIntercept.register({
  request: async function (url: any, config: any) {
    // Generate a unique key for the map using UUID
    const uniqueKey = generateUUID();

    // Get trace details based on the URL
    const {traceName, placeholderUrl} = getTraceDetails(url);

    // Start a performance trace before the request is sent
    if (perf) {
      const t = await perf().startTrace(traceName);

      // Store trace and metadata in the map with a timestamp
      traceMap.set(uniqueKey, {
        trace: t,
        placeholderUrl: placeholderUrl,
        timestamp: Date.now(),
      });

      // Add unique key to request headers
      config.headers = {
        ...config.headers,
        'x-unique-key': uniqueKey,
      };
    }

    return [url, config];
  },

  requestError: function (error: any) {
    // Called when an error occurred during another 'request' interceptor call
    return Promise.reject(error);
  },

  response: function (response: any) {
    const uniqueKey = response.request.headers.get('x-unique-key');
    if (uniqueKey) {
      const traceData = traceMap.get(uniqueKey);
      if (traceData) {
        const trace = traceData.trace;
        const placeholderUrl = traceData.placeholderUrl || response.url;
        const truncatedBaseUrl = placeholderUrl.length > 32 ? placeholderUrl.substring(0, 32) : placeholderUrl;
        trace.putAttribute('url', truncatedBaseUrl);
        trace.putAttribute('http_method', response.request.method);
        trace.putMetric('http_status', response.status);
        trace.stop();
        traceMap.delete(uniqueKey); // Remove from the map after processing
      }
    }

    return response;
  },

  responseError: function (error: any) {
    return Promise.reject(error);
  },
});
