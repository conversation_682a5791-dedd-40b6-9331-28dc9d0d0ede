/**
 * Simple error logging utility for React Native
 * Specifically handles view hierarchy errors and other unhandled exceptions
 */

interface ViewHierarchyError extends Error {
  nativeStackAndroid?: string[];
  userInfo?: any;
}

export const isViewHierarchyError = (error: Error | string): boolean => {
  const message = typeof error === 'string' ? error : error.message || '';
  const stack = typeof error === 'string' ? '' : error.stack || '';

  return (
    message.includes('ViewManager') ||
    message.includes('NativeViewHierarchyManager') ||
    message.includes('could not be found') ||
    message.includes('view tag') ||
    message.includes('IllegalViewOperationException') ||
    stack.includes('NativeViewHierarchyManager') ||
    stack.includes('ViewManager')
  );
};

export const isReanimatedError = (error: Error | string): boolean => {
  const message = typeof error === 'string' ? error : error.message || '';
  const stack = typeof error === 'string' ? '' : error.stack || '';

  return (
    message.includes('reanimated') ||
    message.includes('Reanimated') ||
    stack.includes('reanimated') ||
    stack.includes('NodesManager') ||
    stack.includes('AndroidUIScheduler')
  );
};

export const logViewHierarchyError = (error: ViewHierarchyError | string) => {
  if (__DEV__) {
    if (typeof error === 'string') {
      console.warn('🔴 View Hierarchy Error:', {
        message: error,
        timestamp: new Date().toISOString(),
        type: 'VIEW_HIERARCHY_ERROR',
      });
    } else {
      console.warn('🔴 View Hierarchy Error:', {
        message: error.message,
        name: error.name,
        stack: error.stack?.split('\n').slice(0, 8).join('\n'),
        nativeStack: error.nativeStackAndroid?.slice(0, 5),
        userInfo: error.userInfo,
        timestamp: new Date().toISOString(),
        type: 'VIEW_HIERARCHY_ERROR',
      });
    }
  }
};

export const logReanimatedError = (error: Error | string) => {
  if (__DEV__) {
    if (typeof error === 'string') {
      console.warn('🟡 Reanimated Error:', {
        message: error,
        timestamp: new Date().toISOString(),
        type: 'REANIMATED_ERROR',
      });
    } else {
      console.warn('🟡 Reanimated Error:', {
        message: error.message,
        name: error.name,
        stack: error.stack?.split('\n').slice(0, 8).join('\n'),
        timestamp: new Date().toISOString(),
        type: 'REANIMATED_ERROR',
      });
    }
  }
};

/**
 * Simple initialization function that just logs that error handling utilities are available
 * This replaces the complex global error handler that was causing issues
 */
export const initializeGlobalErrorHandler = () => {
  if (__DEV__) {
    console.log('✅ Error handling utilities initialized for view hierarchy errors');
    console.log('📝 Use logViewHierarchyError() and logReanimatedError() for enhanced error logging');
  }
};

/**
 * No-op restore function for compatibility
 */
export const restoreOriginalErrorHandler = () => {
  // No-op since we're not overriding global handlers
};

export default {
  initialize: initializeGlobalErrorHandler,
  restore: restoreOriginalErrorHandler,
  isViewHierarchyError,
  isReanimatedError,
  logViewHierarchyError,
  logReanimatedError,
};
