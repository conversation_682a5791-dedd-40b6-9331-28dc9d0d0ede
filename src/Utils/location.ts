import axios from 'axios';
import {GOOGLE_API_KEY} from '@env';
import {Coords} from '~types/api/user';

export const getAddressComponent = (addressComponents: any, type: string) => {
  const component = addressComponents.find(component => component.types.includes(type));
  return component ? component.long_name : '';
};

export const reverseGeocode = async ({
  withAccurateAdrs,
  coords,
}: {
  withAccurateAdrs?: boolean;
  coords: {latitude?: number; longitude?: number};
}) => {
  if (coords?.latitude && coords?.longitude) {
    const apiKey = GOOGLE_API_KEY;
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${coords.latitude},${coords.longitude}&key=${apiKey}&language=en`;

    try {
      const response = await axios.get(url);
      const addressComponents = response.data.results[0].address_components;
      const city = getAddressComponent(addressComponents, 'locality');
      const city2 = getAddressComponent(addressComponents, 'postal_town');
      const state = getAddressComponent(addressComponents, 'administrative_area_level_1');
      const country = getAddressComponent(addressComponents, 'country');
      return withAccurateAdrs
        ? (response.data.results[0].formatted_address as string)
        : `${city || city2 || state}, ${country}`;
    } catch (error) {
      console.error(error);
      return '';
    }
  }
};
