/**
 * Rectangular bounds utility for smart caching
 * 
 * This implements the logic where we:
 * 1. Take the 4 corners of the current map view
 * 2. Reduce coordinates to 1/2 to create a rectangle
 * 3. Only fetch new data when user moves outside this rectangle
 */

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface RectangularBounds {
  north: number;
  south: number;
  east: number;
  west: number;
  center: Coordinates;
  radius: number;
}

export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

/**
 * Calculate rectangular bounds from map region
 * Reduces the coordinates to 1/2 to create a smaller rectangle for cache validation
 */
export const calculateRectangularBounds = (
  region: MapRegion,
  radius: number
): RectangularBounds => {
  // Calculate the 4 corners of the current map view
  const north = region.latitude + region.latitudeDelta / 2;
  const south = region.latitude - region.latitudeDelta / 2;
  const east = region.longitude + region.longitudeDelta / 2;
  const west = region.longitude - region.longitudeDelta / 2;

  // Reduce coordinates to 1/2 to create a smaller rectangle
  const reducedLatDelta = region.latitudeDelta / 4; // Half of half = quarter
  const reducedLngDelta = region.longitudeDelta / 4; // Half of half = quarter

  const bounds: RectangularBounds = {
    north: region.latitude + reducedLatDelta,
    south: region.latitude - reducedLatDelta,
    east: region.longitude + reducedLngDelta,
    west: region.longitude - reducedLngDelta,
    center: {
      latitude: region.latitude,
      longitude: region.longitude,
    },
    radius: radius,
  };

  console.log(`📐 [BOUNDS] Calculated rectangular bounds:`, {
    original: { north, south, east, west },
    reduced: {
      north: bounds.north,
      south: bounds.south,
      east: bounds.east,
      west: bounds.west,
    },
    center: bounds.center,
    radius: bounds.radius,
  });

  return bounds;
};

/**
 * Check if a coordinate is within the rectangular bounds
 */
export const isWithinBounds = (
  coordinate: Coordinates,
  bounds: RectangularBounds
): boolean => {
  const within = (
    coordinate.latitude >= bounds.south &&
    coordinate.latitude <= bounds.north &&
    coordinate.longitude >= bounds.west &&
    coordinate.longitude <= bounds.east
  );

  console.log(`📍 [BOUNDS] Checking if coordinate is within bounds:`, {
    coordinate,
    bounds: {
      north: bounds.north,
      south: bounds.south,
      east: bounds.east,
      west: bounds.west,
    },
    within,
  });

  return within;
};

/**
 * Check if current location is within cached bounds for a given radius
 */
export const shouldUseCachedData = (
  currentLocation: Coordinates,
  cachedBounds: RectangularBounds | null,
  currentRadius: number
): boolean => {
  if (!cachedBounds) {
    console.log(`📍 [BOUNDS] No cached bounds available, need to fetch`);
    return false;
  }

  // Check if radius matches (allow small tolerance)
  const radiusMatches = Math.abs(cachedBounds.radius - currentRadius) <= 5; // 5km tolerance
  if (!radiusMatches) {
    console.log(`📍 [BOUNDS] Radius mismatch: cached=${cachedBounds.radius}km, current=${currentRadius}km`);
    return false;
  }

  // Check if current location is within bounds
  const withinBounds = isWithinBounds(currentLocation, cachedBounds);
  
  console.log(`📍 [BOUNDS] Cache decision:`, {
    currentLocation,
    cachedRadius: cachedBounds.radius,
    currentRadius,
    radiusMatches,
    withinBounds,
    shouldUseCache: withinBounds,
  });

  return withinBounds;
};

/**
 * Generate a cache key for rectangular bounds
 */
export const generateBoundsKey = (bounds: RectangularBounds, filters: any): string => {
  // Round coordinates to 3 decimal places for consistent caching
  const roundedBounds = {
    north: Math.round(bounds.north * 1000) / 1000,
    south: Math.round(bounds.south * 1000) / 1000,
    east: Math.round(bounds.east * 1000) / 1000,
    west: Math.round(bounds.west * 1000) / 1000,
    radius: Math.round(bounds.radius),
  };

  const filterKey = JSON.stringify(filters);
  const key = `${roundedBounds.north},${roundedBounds.south},${roundedBounds.east},${roundedBounds.west},${roundedBounds.radius},${filterKey}`;
  
  console.log(`🔑 [BOUNDS] Generated cache key:`, key);
  return key;
};

/**
 * Calculate distance between two coordinates in kilometers
 */
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};
