/**
 * Debug utilities to help identify and fix touch/click issues
 */

import {Platform} from 'react-native';

/**
 * Debug function to test if touch events are working
 */
export const debugTouchEvent = (componentName: string, eventType: string, additionalInfo?: any) => {
  if (__DEV__) {
    console.log(`🔘 [${componentName}] Touch event: ${eventType}`, additionalInfo || '');
  }
};

/**
 * Enhanced onPress handler with debugging
 */
export const createDebugOnPress = (originalHandler: () => void, componentName: string) => {
  return () => {
    debugTouchEvent(componentName, 'onPress triggered');
    try {
      originalHandler();
      debugTouchEvent(componentName, 'onPress completed successfully');
    } catch (error) {
      console.error(`❌ [${componentName}] onPress error:`, error);
    }
  };
};

/**
 * Check if a component has proper touch configuration
 */
export const validateTouchableComponent = (componentName: string, props: any) => {
  if (!__DEV__) return;

  const issues: string[] = [];
  const warnings: string[] = [];

  // Check for required props
  if (!props.onPress && !props.onLongPress) {
    issues.push('Missing onPress or onLongPress handler');
  }

  // Check for conflicting props
  if (props.disabled) {
    warnings.push('Component is disabled');
  }

  if (props.pointerEvents === 'none') {
    issues.push('pointerEvents is set to "none" - component will not receive touch events');
  }

  // Check for style issues
  if (props.style) {
    const style = Array.isArray(props.style) ? Object.assign({}, ...props.style) : props.style;
    
    if (style.position === 'absolute' && (!style.zIndex || style.zIndex < 0)) {
      warnings.push('Absolute positioned element without proper zIndex');
    }

    if (style.opacity !== undefined && style.opacity < 0.1) {
      warnings.push('Very low opacity might make component hard to interact with');
    }
  }

  // Log results
  if (issues.length > 0) {
    console.error(`❌ [${componentName}] Touch issues:`, issues);
  }
  
  if (warnings.length > 0) {
    console.warn(`⚠️ [${componentName}] Touch warnings:`, warnings);
  }

  if (issues.length === 0 && warnings.length === 0) {
    console.log(`✅ [${componentName}] Touch configuration looks good`);
  }
};

/**
 * Test touch responsiveness
 */
export const testTouchResponsiveness = (componentName: string) => {
  if (!__DEV__) return;

  const startTime = Date.now();
  
  return {
    onPressIn: () => {
      debugTouchEvent(componentName, 'onPressIn', `Response time: ${Date.now() - startTime}ms`);
    },
    onPressOut: () => {
      debugTouchEvent(componentName, 'onPressOut');
    },
    onPress: () => {
      debugTouchEvent(componentName, 'onPress', `Total interaction time: ${Date.now() - startTime}ms`);
    },
  };
};

/**
 * Common fixes for touch issues
 */
export const getTouchFixProps = () => ({
  // Ensure proper touch target size
  style: {
    minHeight: 44,
    minWidth: 44,
  },
  // Improve touch responsiveness
  delayPressIn: 0,
  delayPressOut: 0,
  // Better accessibility
  accessible: true,
  accessibilityRole: 'button' as const,
});

/**
 * Platform-specific touch optimizations
 */
export const getPlatformTouchOptimizations = (colors: any) => {
  const baseProps = {
    activeOpacity: 0.7,
    delayLongPress: 500,
  };

  if (Platform.OS === 'android') {
    return {
      ...baseProps,
      android_ripple: {
        color: colors.primary + '20',
        borderless: false,
      },
    };
  }

  return baseProps;
};

/**
 * Check for common touch blocking issues
 */
export const checkForTouchBlockers = (componentName: string, parentProps?: any) => {
  if (!__DEV__) return;

  const blockers: string[] = [];

  // Check parent component issues
  if (parentProps) {
    if (parentProps.pointerEvents === 'none') {
      blockers.push('Parent has pointerEvents="none"');
    }

    if (parentProps.style) {
      const parentStyle = Array.isArray(parentProps.style) 
        ? Object.assign({}, ...parentProps.style) 
        : parentProps.style;

      if (parentStyle.zIndex && parentStyle.zIndex < 0) {
        blockers.push('Parent has negative zIndex');
      }
    }
  }

  // Check for keyboard interference
  if (Platform.OS === 'ios') {
    blockers.push('Check if keyboard is interfering with touch events on iOS');
  }

  if (blockers.length > 0) {
    console.warn(`⚠️ [${componentName}] Potential touch blockers:`, blockers);
  }
};

/**
 * Enhanced TouchableOpacity props with debugging
 */
export const getDebugTouchableProps = (componentName: string, colors: any) => ({
  ...getTouchFixProps(),
  ...getPlatformTouchOptimizations(colors),
  ...testTouchResponsiveness(componentName),
  onPress: createDebugOnPress(() => {
    console.log(`🎯 [${componentName}] Touch successful!`);
  }, componentName),
});
