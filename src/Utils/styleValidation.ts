/**
 * Style validation utilities to prevent common React Native style errors
 */

import {ViewStyle, TextStyle, ImageStyle} from 'react-native';

type StyleType = ViewStyle | TextStyle | ImageStyle;

/**
 * Validates that shadow properties are properly nested in style objects
 * @param props - Component props to validate
 * @param componentName - Name of the component for error reporting
 */
export const validateStyleProps = (props: any, componentName: string = 'Component') => {
  if (__DEV__) {
    const shadowProps = ['shadowOffset', 'shadowColor', 'shadowOpacity', 'shadowRadius'];
    
    shadowProps.forEach(prop => {
      if (props.hasOwnProperty(prop)) {
        console.warn(
          `⚠️ [${componentName}] Style property '${prop}' should be nested in a style object. ` +
          `Use: style={{${prop}: ...}} instead of ${prop}={...}`
        );
      }
    });
  }
};

/**
 * Safely merges shadow styles to prevent prop conflicts
 * @param shadowStyle - Shadow style object
 * @param additionalStyles - Additional styles to merge
 */
export const mergeShadowStyles = (shadowStyle: StyleType, ...additionalStyles: StyleType[]): StyleType => {
  return Object.assign({}, shadowStyle, ...additionalStyles);
};

/**
 * Creates a safe shadow style object
 * @param shadowConfig - Shadow configuration
 */
export const createShadowStyle = (shadowConfig: {
  shadowColor?: string;
  shadowOffset?: {width: number; height: number};
  shadowOpacity?: number;
  shadowRadius?: number;
  elevation?: number;
}): ViewStyle => {
  return {
    shadowColor: shadowConfig.shadowColor || '#000',
    shadowOffset: shadowConfig.shadowOffset || {width: 0, height: 2},
    shadowOpacity: shadowConfig.shadowOpacity || 0.1,
    shadowRadius: shadowConfig.shadowRadius || 4,
    elevation: shadowConfig.elevation || 4,
  };
};

/**
 * Validates and fixes common style issues
 * @param style - Style object to validate
 */
export const validateAndFixStyle = (style: any): StyleType => {
  if (!style) return {};
  
  // If style contains direct shadow props, move them to proper structure
  const fixedStyle = {...style};
  
  if (__DEV__) {
    const shadowProps = ['shadowOffset', 'shadowColor', 'shadowOpacity', 'shadowRadius'];
    const hasDirectShadowProps = shadowProps.some(prop => style.hasOwnProperty(prop));
    
    if (hasDirectShadowProps) {
      console.warn('⚠️ Direct shadow props detected and fixed automatically');
    }
  }
  
  return fixedStyle;
};
