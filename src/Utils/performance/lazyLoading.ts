import React, {Suspense, ComponentType, LazyExoticComponent} from 'react';
import {InteractionManager} from 'react-native';

// ============================================================================
// COMPREHENSIVE LAZY LOADING SYSTEM
// ============================================================================

interface LazyLoadConfig {
  preloadDelay?: number;
  preloadCondition?: () => boolean;
  fallback?: ComponentType;
  errorBoundary?: ComponentType<{error: Error; retry: () => void}>;
  retryAttempts?: number;
  retryDelay?: number;
}

interface LazyComponentManager {
  preload: () => Promise<void>;
  isLoaded: () => boolean;
  Component: LazyExoticComponent<any>;
}

// Global registry for lazy components
const lazyComponentRegistry = new Map<string, LazyComponentManager>();

/**
 * Enhanced lazy loading with preloading and error handling
 */
export const createLazyComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{default: T}>,
  config: LazyLoadConfig = {}
): LazyComponentManager => {
  const {
    preloadDelay = 0,
    preloadCondition,
    fallback,
    errorBoundary,
    retryAttempts = 3,
    retryDelay = 1000,
  } = config;

  let componentPromise: Promise<{default: T}> | null = null;
  let isLoaded = false;
  let retryCount = 0;

  const loadComponent = async (): Promise<{default: T}> => {
    if (componentPromise) {
      return componentPromise;
    }

    componentPromise = new Promise(async (resolve, reject) => {
      try {
        const module = await importFn();
        isLoaded = true;
        resolve(module);
      } catch (error) {
        componentPromise = null;

        if (retryCount < retryAttempts) {
          retryCount++;
          console.warn(`Lazy load failed, retrying (${retryCount}/${retryAttempts}):`, error);

          setTimeout(() => {
            loadComponent().then(resolve).catch(reject);
          }, retryDelay * retryCount);
        } else {
          reject(error);
        }
      }
    });

    return componentPromise;
  };

  const preload = async (): Promise<void> => {
    if (isLoaded || componentPromise) return;

    await new Promise(resolve => {
      InteractionManager.runAfterInteractions(() => {
        setTimeout(resolve, preloadDelay);
      });
    });

    try {
      await loadComponent();
    } catch (error) {
      console.warn('Preload failed:', error);
    }
  };

  // Auto-preload if condition is met
  if (preloadCondition?.()) {
    setTimeout(preload, 100);
  }

  const LazyComponent = React.lazy(loadComponent);

  return {
    preload,
    isLoaded: () => isLoaded,
    Component: LazyComponent,
  };
};

/**
 * Screen-level lazy loading with route-based preloading
 */
export const createLazyScreen = <T extends ComponentType<any>>(
  screenName: string,
  importFn: () => Promise<{default: T}>,
  config: LazyLoadConfig & {
    preloadRoutes?: string[];
    preloadOnNavigation?: boolean;
  } = {}
) => {
  const {preloadRoutes = [], preloadOnNavigation = true, ...lazyConfig} = config;

  const manager = createLazyComponent(importFn, lazyConfig);

  // Register in global registry
  lazyComponentRegistry.set(screenName, manager);

  // Setup route-based preloading
  if (preloadOnNavigation) {
    setupRoutePreloading(screenName, preloadRoutes);
  }

  return manager;
};

/**
 * Route-based preloading system
 */
const setupRoutePreloading = (targetScreen: string, triggerRoutes: string[]) => {
  // This would integrate with your navigation system
  // For now, we'll provide a manual trigger
  const preloadOnRoute = (currentRoute: string) => {
    if (triggerRoutes.includes(currentRoute)) {
      const manager = lazyComponentRegistry.get(targetScreen);
      if (manager && !manager.isLoaded()) {
        manager.preload();
      }
    }
  };

  return preloadOnRoute;
};

/**
 * Bulk preloading for related components
 */
export const preloadComponentGroup = async (
  componentNames: string[],
  options: {
    parallel?: boolean;
    delay?: number;
    priority?: 'high' | 'normal' | 'low';
  } = {}
) => {
  const {parallel = true, delay = 0, priority = 'normal'} = options;

  const preloadTasks = componentNames.map(name => {
    const manager = lazyComponentRegistry.get(name);
    return manager ? manager.preload : null;
  }).filter(Boolean) as Array<() => Promise<void>>;

  if (delay > 0) {
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  if (parallel) {
    await Promise.allSettled(preloadTasks.map(task => task()));
  } else {
    for (const task of preloadTasks) {
      await task();
      if (priority === 'low') {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }
};

/**
 * Intelligent preloading based on user behavior
 */
export class IntelligentPreloader {
  private userPatterns: Map<string, number> = new Map();
  private preloadHistory: Set<string> = new Set();

  recordNavigation(from: string, to: string) {
    const pattern = `${from}->${to}`;
    const count = this.userPatterns.get(pattern) || 0;
    this.userPatterns.set(pattern, count + 1);

    // Trigger predictive preloading
    this.predictivePreload(to);
  }

  private predictivePreload(currentScreen: string) {
    // Find most likely next screens
    const predictions = Array.from(this.userPatterns.entries())
      .filter(([pattern]) => pattern.startsWith(currentScreen))
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([pattern]) => pattern.split('->')[1]);

    // Preload predicted screens
    predictions.forEach(screenName => {
      if (!this.preloadHistory.has(screenName)) {
        const manager = lazyComponentRegistry.get(screenName);
        if (manager && !manager.isLoaded()) {
          manager.preload();
          this.preloadHistory.add(screenName);
        }
      }
    });
  }

  getPatterns() {
    return Array.from(this.userPatterns.entries())
      .sort(([, a], [, b]) => b - a);
  }

  clearHistory() {
    this.preloadHistory.clear();
  }
}

export const intelligentPreloader = new IntelligentPreloader();

/**
 * Component-level lazy loading for heavy components
 */
export const LazyComponent = <T extends ComponentType<any>>({
  loader,
  fallback,
  delay = 0,
  condition = true,
  ...props
}: {
  loader: () => Promise<{default: T}>;
  fallback?: ComponentType;
  delay?: number;
  condition?: boolean;
} & React.ComponentProps<T>) => {
  const [Component, setComponent] = React.useState<T | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    if (!condition || Component) return;

    const loadComponent = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        const module = await loader();
        setComponent(() => module.default);
      } catch (err) {
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    };

    InteractionManager.runAfterInteractions(loadComponent);
  }, [condition, Component, loader, delay]);

  if (error) {
    return null; // Simplified error handling
  }

  if (!Component) {
    return null; // Simplified fallback
  }

  return React.createElement(Component, props);
};

/**
 * Lazy loading hook for dynamic imports
 */
export const useLazyImport = <T>(
  importFn: () => Promise<T>,
  deps: React.DependencyList = []
) => {
  const [data, setData] = React.useState<T | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  const load = React.useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await importFn();
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, deps);

  return {data, isLoading, error, load};
};

// Default components - simplified for TypeScript compatibility
const DefaultFallback = () => null;
const DefaultErrorBoundary = () => null;

// Utility functions
export const preloadAllRegistered = async () => {
  const managers = Array.from(lazyComponentRegistry.values());
  await Promise.allSettled(managers.map(manager => manager.preload()));
};

export const getRegistryStats = () => {
  const total = lazyComponentRegistry.size;
  const loaded = Array.from(lazyComponentRegistry.values())
    .filter(manager => manager.isLoaded()).length;

  return {total, loaded, pending: total - loaded};
};
