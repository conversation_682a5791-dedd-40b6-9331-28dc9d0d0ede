import {addEventListener} from '@react-native-community/netinfo';
import {requestQueue, RequestPriority} from './advancedOptimizations';

// ============================================================================
// ADVANCED NETWORK OPTIMIZATION
// ============================================================================

interface NetworkState {
  isConnected: boolean;
  type: string;
  isInternetReachable: boolean;
  effectiveType?: string;
}

interface RequestConfig {
  priority?: RequestPriority;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  cacheStrategy?: 'cache-first' | 'network-first' | 'cache-only' | 'network-only';
  adaptToConnection?: boolean;
}

interface CacheEntry {
  data: any;
  timestamp: number;
  etag?: string;
  maxAge: number;
  staleWhileRevalidate?: boolean;
}

/**
 * Advanced network manager with intelligent caching and request optimization
 */
class NetworkManager {
  private cache = new Map<string, CacheEntry>();
  private pendingRequests = new Map<string, Promise<any>>();
  private networkState: NetworkState = {
    isConnected: true,
    type: 'unknown',
    isInternetReachable: true,
  };
  private requestStats = {
    total: 0,
    cached: 0,
    failed: 0,
    retries: 0,
  };

  constructor() {
    this.setupNetworkListener();
  }

  private setupNetworkListener() {
    addEventListener(state => {
      this.networkState = {
        isConnected: state.isConnected ?? false,
        type: state.type,
        isInternetReachable: state.isInternetReachable ?? false,
        effectiveType: state.details?.effectiveType,
      };
    });
  }

  /**
   * Optimized fetch with intelligent caching and request deduplication
   */
  async fetch<T>(url: string, options: RequestInit & RequestConfig = {}): Promise<T> {
    const {
      priority = RequestPriority.NORMAL,
      timeout = 10000,
      retries = 3,
      retryDelay = 1000,
      cacheStrategy = 'cache-first',
      adaptToConnection = true,
      ...fetchOptions
    } = options;

    const cacheKey = this.generateCacheKey(url, fetchOptions);

    // Handle cache strategies
    if (cacheStrategy === 'cache-only') {
      return this.getCachedData(cacheKey);
    }

    if (cacheStrategy === 'cache-first') {
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        // Optionally revalidate in background
        this.backgroundRevalidate(url, fetchOptions, cacheKey);
        return cached;
      }
    }

    // Check for pending request (deduplication)
    if (this.pendingRequests.has(cacheKey)) {
      return this.pendingRequests.get(cacheKey)!;
    }

    // Create request promise
    const requestPromise = this.executeRequest<T>(
      url,
      fetchOptions,
      {priority, timeout, retries, retryDelay, adaptToConnection},
      cacheKey,
    );

    this.pendingRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;
      this.requestStats.total++;
      return result;
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }

  private async executeRequest<T>(
    url: string,
    options: RequestInit,
    config: {
      priority: RequestPriority;
      timeout: number;
      retries: number;
      retryDelay: number;
      adaptToConnection: boolean;
    },
    cacheKey: string,
  ): Promise<T> {
    const {priority, timeout, retries, retryDelay, adaptToConnection} = config;

    // Adapt request based on connection
    const adaptedOptions = adaptToConnection ? this.adaptRequestToConnection(options) : options;

    return requestQueue.enqueue(async () => {
      let lastError: Error | null = null;

      for (let attempt = 0; attempt <= retries; attempt++) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeout);

          const response = await fetch(url, {
            ...adaptedOptions,
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();

          // Cache successful response
          this.cacheResponse(cacheKey, data, response);

          return data;
        } catch (error) {
          lastError = error as Error;

          if (attempt < retries) {
            this.requestStats.retries++;
            await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
          }
        }
      }

      this.requestStats.failed++;
      throw lastError;
    }, priority);
  }

  private adaptRequestToConnection(options: RequestInit): RequestInit {
    const {type, effectiveType} = this.networkState;

    // Reduce quality for slow connections
    if (effectiveType === '2g' || effectiveType === 'slow-2g') {
      return {
        ...options,
        headers: {
          ...options.headers,
          'Accept-Encoding': 'gzip',
          'Cache-Control': 'max-age=3600',
        },
      };
    }

    // Optimize for cellular connections
    if (type === 'cellular') {
      return {
        ...options,
        headers: {
          ...options.headers,
          'Accept-Encoding': 'gzip, deflate, br',
        },
      };
    }

    return options;
  }

  private generateCacheKey(url: string, options: RequestInit): string {
    const method = options.method || 'GET';
    const body = options.body ? JSON.stringify(options.body) : '';
    return `${method}:${url}:${body}`;
  }

  private getCachedData<T>(cacheKey: string): T | null {
    const entry = this.cache.get(cacheKey);
    if (!entry) {
      return null;
    }

    const now = Date.now();
    const age = now - entry.timestamp;

    // Check if expired
    if (age > entry.maxAge) {
      this.cache.delete(cacheKey);
      return null;
    }

    this.requestStats.cached++;
    return entry.data;
  }

  private cacheResponse(cacheKey: string, data: any, response: Response) {
    const cacheControl = response.headers.get('cache-control') || '';
    const maxAge = this.parseCacheControl(cacheControl);
    const etag = response.headers.get('etag') || undefined;

    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      etag,
      maxAge,
      staleWhileRevalidate: cacheControl.includes('stale-while-revalidate'),
    });

    // Limit cache size
    if (this.cache.size > 1000) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  private parseCacheControl(cacheControl: string): number {
    const maxAgeMatch = cacheControl.match(/max-age=(\d+)/);
    return maxAgeMatch ? parseInt(maxAgeMatch[1]) * 1000 : 300000; // Default 5 minutes
  }

  private async backgroundRevalidate(url: string, options: RequestInit, cacheKey: string) {
    try {
      await this.executeRequest(
        url,
        options,
        {
          priority: RequestPriority.BACKGROUND,
          timeout: 5000,
          retries: 1,
          retryDelay: 1000,
          adaptToConnection: true,
        },
        cacheKey,
      );
    } catch (error) {
      // Silent fail for background revalidation
      console.warn('Background revalidation failed:', error);
    }
  }

  /**
   * Batch multiple requests for efficiency
   */
  async batchRequests<T>(requests: Array<{url: string; options?: RequestInit & RequestConfig}>): Promise<T[]> {
    const promises = requests.map(({url, options}) => this.fetch<T>(url, options));

    return Promise.all(promises);
  }

  /**
   * Prefetch resources
   */
  async prefetch(urls: string[], priority: RequestPriority = RequestPriority.LOW) {
    const promises = urls.map(url => this.fetch(url, {priority, cacheStrategy: 'network-first'}));

    try {
      await Promise.allSettled(promises);
    } catch (error) {
      console.warn('Prefetch failed:', error);
    }
  }

  /**
   * Clear cache
   */
  clearCache(pattern?: string) {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const [key] of this.cache) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  /**
   * Get network and cache statistics
   */
  getStats() {
    return {
      ...this.requestStats,
      cacheSize: this.cache.size,
      networkState: this.networkState,
      hitRate: this.requestStats.total > 0 ? (this.requestStats.cached / this.requestStats.total) * 100 : 0,
    };
  }

  /**
   * Check if online
   */
  isOnline(): boolean {
    return this.networkState.isConnected && this.networkState.isInternetReachable;
  }

  /**
   * Get connection quality
   */
  getConnectionQuality(): 'excellent' | 'good' | 'poor' | 'offline' {
    if (!this.isOnline()) {
      return 'offline';
    }

    const {type, effectiveType} = this.networkState;

    if (type === 'wifi') {
      return 'excellent';
    }
    if (effectiveType === '4g') {
      return 'good';
    }
    if (effectiveType === '3g') {
      return 'good';
    }
    if (effectiveType === '2g' || effectiveType === 'slow-2g') {
      return 'poor';
    }

    return 'good';
  }
}

// Global network manager instance
export const networkManager = new NetworkManager();

/**
 * React hook for network-aware requests
 */
export const useNetworkRequest = <T>(
  url: string,
  options: RequestInit & RequestConfig = {},
  deps: React.DependencyList = [],
) => {
  const [data, setData] = React.useState<T | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  const execute = React.useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await networkManager.fetch<T>(url, options);
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [url, ...deps]);

  React.useEffect(() => {
    execute();
  }, [execute]);

  return {data, isLoading, error, refetch: execute};
};

/**
 * Offline-first data synchronization
 */
export class OfflineSync {
  private pendingOperations: Array<{
    id: string;
    operation: () => Promise<any>;
    timestamp: number;
  }> = [];

  addOperation(id: string, operation: () => Promise<any>) {
    this.pendingOperations.push({
      id,
      operation,
      timestamp: Date.now(),
    });

    // Try to sync immediately if online
    if (networkManager.isOnline()) {
      this.sync();
    }
  }

  async sync() {
    if (!networkManager.isOnline() || this.pendingOperations.length === 0) {
      return;
    }

    const operations = [...this.pendingOperations];
    this.pendingOperations = [];

    for (const {id, operation} of operations) {
      try {
        await operation();
      } catch (error) {
        console.warn(`Sync failed for operation ${id}:`, error);
        // Re-add failed operation
        this.pendingOperations.push({id, operation, timestamp: Date.now()});
      }
    }
  }

  getPendingCount(): number {
    return this.pendingOperations.length;
  }
}

export const offlineSync = new OfflineSync();

// Auto-sync when network becomes available
addEventListener(state => {
  if (state.isConnected && state.isInternetReachable) {
    offlineSync.sync();
  }
});
