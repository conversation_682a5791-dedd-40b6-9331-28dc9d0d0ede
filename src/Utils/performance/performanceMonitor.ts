import {QueryClient} from 'react-query';

// Enhanced performance metrics tracking
interface PerformanceMetrics {
  searchLatency: number[];
  cacheHitRate: number;
  requestCount: number;
  errorRate: number;
  prefetchSuccess: number;
  backgroundRefreshCount: number;

  // New metrics for comprehensive tracking
  eventFiltering: {
    totalFetched: number;
    filteredOut: number;
    duplicatesRemoved: number;
    averageFilterTime: number;
  };

  offlineMetrics: {
    offlineInteractions: number;
    syncFailures: number;
    backgroundSyncs: number;
    cacheSize: number; // in MB
  };

  userExperience: {
    averageLoadTime: number;
    fastLoads: number; // < 1s
    slowLoads: number; // > 3s
  };
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    searchLatency: [],
    cacheHitRate: 0,
    requestCount: 0,
    errorRate: 0,
    prefetchSuccess: 0,
    backgroundRefreshCount: 0,

    eventFiltering: {
      totalFetched: 0,
      filteredOut: 0,
      duplicatesRemoved: 0,
      averageFilterTime: 0,
    },

    offlineMetrics: {
      offlineInteractions: 0,
      syncFailures: 0,
      backgroundSyncs: 0,
      cacheSize: 0,
    },

    userExperience: {
      averageLoadTime: 0,
      fastLoads: 0,
      slowLoads: 0,
    },
  };

  private cacheHits = 0;
  private cacheMisses = 0;
  private errors = 0;
  private startTimes = new Map<string, number>();

  // Track search performance
  startSearch(searchId: string) {
    this.startTimes.set(searchId, Date.now());
  }

  endSearch(searchId: string, fromCache = false) {
    const startTime = this.startTimes.get(searchId);
    if (startTime) {
      const latency = Date.now() - startTime;
      this.metrics.searchLatency.push(latency);

      // Keep only last 100 measurements
      if (this.metrics.searchLatency.length > 100) {
        this.metrics.searchLatency.shift();
      }

      this.startTimes.delete(searchId);

      if (fromCache) {
        this.cacheHits++;
      } else {
        this.cacheMisses++;
      }

      this.updateCacheHitRate();
    }
  }

  // Track cache performance
  recordCacheHit() {
    this.cacheHits++;
    this.updateCacheHitRate();
  }

  recordCacheMiss() {
    this.cacheMisses++;
    this.updateCacheHitRate();
  }

  private updateCacheHitRate() {
    const total = this.cacheHits + this.cacheMisses;
    this.metrics.cacheHitRate = total > 0 ? (this.cacheHits / total) * 100 : 0;
  }

  // Track request counts
  recordRequest() {
    this.metrics.requestCount++;
  }

  recordError() {
    this.errors++;
    this.metrics.errorRate = (this.errors / this.metrics.requestCount) * 100;
  }

  recordPrefetchSuccess() {
    this.metrics.prefetchSuccess++;
  }

  recordBackgroundRefresh() {
    this.metrics.backgroundRefreshCount++;
  }

  // ============================================================================
  // NEW ENHANCED TRACKING METHODS
  // ============================================================================

  // Track event filtering performance
  recordEventFiltering(totalFetched: number, filteredOut: number, duplicatesRemoved: number, filterTime: number) {
    this.metrics.eventFiltering.totalFetched += totalFetched;
    this.metrics.eventFiltering.filteredOut += filteredOut;
    this.metrics.eventFiltering.duplicatesRemoved += duplicatesRemoved;

    // Update average filter time
    const currentAvg = this.metrics.eventFiltering.averageFilterTime;
    const totalOperations = this.metrics.eventFiltering.totalFetched / totalFetched; // Approximate operation count
    this.metrics.eventFiltering.averageFilterTime = (currentAvg * (totalOperations - 1) + filterTime) / totalOperations;

    console.log(
      `🎯 Event Filtering: ${totalFetched} → ${totalFetched - filteredOut - duplicatesRemoved} (${filterTime}ms)`,
    );
  }

  // Track offline interactions
  recordOfflineInteraction() {
    this.metrics.offlineMetrics.offlineInteractions++;
    console.log(`📱 Offline Interaction: ${this.metrics.offlineMetrics.offlineInteractions} total`);
  }

  recordSyncFailure() {
    this.metrics.offlineMetrics.syncFailures++;
    console.log(`❌ Sync Failure: ${this.metrics.offlineMetrics.syncFailures} total`);
  }

  recordBackgroundSync() {
    this.metrics.offlineMetrics.backgroundSyncs++;
    console.log(`🔄 Background Sync: ${this.metrics.offlineMetrics.backgroundSyncs} total`);
  }

  updateCacheSize(sizeInMB: number) {
    this.metrics.offlineMetrics.cacheSize = sizeInMB;

    if (sizeInMB > 40) {
      // Warn at 40MB
      console.warn(`⚠️ Cache size is large: ${sizeInMB}MB`);
    }
  }

  // Track user experience metrics
  recordLoadTime(loadTime: number) {
    const currentAvg = this.metrics.userExperience.averageLoadTime;
    const totalLoads = this.metrics.userExperience.fastLoads + this.metrics.userExperience.slowLoads + 1;

    this.metrics.userExperience.averageLoadTime = (currentAvg * (totalLoads - 1) + loadTime) / totalLoads;

    if (loadTime < 1000) {
      this.metrics.userExperience.fastLoads++;
    } else if (loadTime > 3000) {
      this.metrics.userExperience.slowLoads++;
    }

    console.log(`⏱️ Load Time: ${loadTime}ms (avg: ${this.metrics.userExperience.averageLoadTime.toFixed(0)}ms)`);
  }

  // Get performance score (0-100)
  getPerformanceScore(): number {
    let score = 100;

    // Cache hit rate impact (0-25 points)
    score += (this.metrics.cacheHitRate / 100) * 25;

    // Error rate impact (-30 points max)
    score -= this.metrics.errorRate * 0.3;

    // Load time impact (-20 points max)
    if (this.metrics.userExperience.averageLoadTime > 3000) {
      score -= 20;
    } else if (this.metrics.userExperience.averageLoadTime < 1000) {
      score += 10;
    }

    // Event filtering efficiency (0-15 points)
    const filteringEfficiency =
      this.metrics.eventFiltering.filteredOut / Math.max(1, this.metrics.eventFiltering.totalFetched);
    score += filteringEfficiency * 15; // Reward filtering out irrelevant events

    // Offline reliability (-10 points max)
    const syncReliability =
      this.metrics.offlineMetrics.backgroundSyncs /
      Math.max(1, this.metrics.offlineMetrics.backgroundSyncs + this.metrics.offlineMetrics.syncFailures);
    score -= (1 - syncReliability) * 10;

    return Math.max(0, Math.min(100, score));
  }

  // Get performance statistics
  getMetrics() {
    const latencies = this.metrics.searchLatency;
    return {
      ...this.metrics,
      averageSearchLatency: latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0,
      p95SearchLatency: latencies.length > 0 ? this.percentile(latencies, 95) : 0,
      p99SearchLatency: latencies.length > 0 ? this.percentile(latencies, 99) : 0,
      totalCacheOperations: this.cacheHits + this.cacheMisses,
    };
  }

  private percentile(arr: number[], p: number): number {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }

  // Reset metrics
  reset() {
    this.metrics = {
      searchLatency: [],
      cacheHitRate: 0,
      requestCount: 0,
      errorRate: 0,
      prefetchSuccess: 0,
      backgroundRefreshCount: 0,
    };
    this.cacheHits = 0;
    this.cacheMisses = 0;
    this.errors = 0;
    this.startTimes.clear();
  }

  // Enhanced log performance summary
  logSummary() {
    const metrics = this.getMetrics();
    const score = this.getPerformanceScore();

    console.log('🚀 COMPREHENSIVE PERFORMANCE SUMMARY');
    console.log('=====================================');
    console.log(`🏆 Performance Score: ${score.toFixed(1)}/100`);
    console.log('');
    console.log('📊 Core Metrics:');
    console.log(`  • Cache Hit Rate: ${metrics.cacheHitRate.toFixed(1)}%`);
    console.log(`  • Average Search Latency: ${metrics.averageSearchLatency.toFixed(2)}ms`);
    console.log(`  • P95 Search Latency: ${metrics.p95SearchLatency.toFixed(2)}ms`);
    console.log(`  • Total Requests: ${metrics.requestCount}`);
    console.log(`  • Error Rate: ${metrics.errorRate.toFixed(1)}%`);
    console.log('');
    console.log('🎯 Event Performance:');
    console.log(`  • Events Fetched: ${this.metrics.eventFiltering.totalFetched}`);
    console.log(
      `  • Filtered Out: ${this.metrics.eventFiltering.filteredOut} (${((this.metrics.eventFiltering.filteredOut / Math.max(1, this.metrics.eventFiltering.totalFetched)) * 100).toFixed(1)}%)`,
    );
    console.log(`  • Duplicates Removed: ${this.metrics.eventFiltering.duplicatesRemoved}`);
    console.log(`  • Avg Filter Time: ${this.metrics.eventFiltering.averageFilterTime.toFixed(2)}ms`);
    console.log('');
    console.log('📱 Offline Performance:');
    console.log(`  • Offline Interactions: ${this.metrics.offlineMetrics.offlineInteractions}`);
    console.log(`  • Background Syncs: ${this.metrics.offlineMetrics.backgroundSyncs}`);
    console.log(`  • Sync Failures: ${this.metrics.offlineMetrics.syncFailures}`);
    console.log(`  • Cache Size: ${this.metrics.offlineMetrics.cacheSize.toFixed(1)}MB`);
    console.log('');
    console.log('👤 User Experience:');
    console.log(`  • Average Load Time: ${this.metrics.userExperience.averageLoadTime.toFixed(0)}ms`);
    console.log(`  • Fast Loads (<1s): ${this.metrics.userExperience.fastLoads}`);
    console.log(`  • Slow Loads (>3s): ${this.metrics.userExperience.slowLoads}`);
    console.log(`  • Prefetch Success: ${metrics.prefetchSuccess}`);
    console.log(`  • Background Refreshes: ${metrics.backgroundRefreshCount}`);
    console.log('=====================================');
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// React Query performance plugin
export const createPerformancePlugin = () => {
  return {
    onSuccess: (data: any, query: any) => {
      performanceMonitor.recordRequest();

      // Check if data came from cache
      if (query.state.dataUpdatedAt === query.state.dataFetchedAt) {
        performanceMonitor.recordCacheHit();
      } else {
        performanceMonitor.recordCacheMiss();
      }
    },
    onError: (error: any, query: any) => {
      performanceMonitor.recordRequest();
      performanceMonitor.recordError();
    },
  };
};

// Hook to monitor query performance
export const usePerformanceTracking = (queryKey: string) => {
  const trackStart = () => {
    performanceMonitor.startSearch(queryKey);
  };

  const trackEnd = (fromCache = false) => {
    performanceMonitor.endSearch(queryKey, fromCache);
  };

  return {trackStart, trackEnd};
};

// Background task to log performance periodically
let performanceLogInterval: NodeJS.Timeout;

export const startPerformanceLogging = (intervalMs = 60000) => {
  if (performanceLogInterval) {
    clearInterval(performanceLogInterval);
  }

  performanceLogInterval = setInterval(() => {
    performanceMonitor.logSummary();
  }, intervalMs);
};

export const stopPerformanceLogging = () => {
  if (performanceLogInterval) {
    clearInterval(performanceLogInterval);
  }
};

// Memory usage monitoring
export const getMemoryUsage = () => {
  if (global.performance && global.performance.memory) {
    return {
      usedJSHeapSize: global.performance.memory.usedJSHeapSize,
      totalJSHeapSize: global.performance.memory.totalJSHeapSize,
      jsHeapSizeLimit: global.performance.memory.jsHeapSizeLimit,
    };
  }
  return null;
};

// Network performance tracking
export const trackNetworkRequest = async <T>(requestName: string, requestFn: () => Promise<T>): Promise<T> => {
  const startTime = Date.now();
  performanceMonitor.recordRequest();

  try {
    const result = await requestFn();
    const endTime = Date.now();

    console.log(`📡 ${requestName} completed in ${endTime - startTime}ms`);
    return result;
  } catch (error) {
    performanceMonitor.recordError();
    const endTime = Date.now();
    console.error(`❌ ${requestName} failed after ${endTime - startTime}ms:`, error);
    throw error;
  }
};
