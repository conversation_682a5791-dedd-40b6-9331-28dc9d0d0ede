import {Event} from '~types/api/event';
import {GetEventsParams} from '~types/events';

/**
 * Intelligent event filtering utilities to prevent unnecessary API calls
 * and optimize performance by excluding irrelevant events
 */

export interface EventFilterOptions {
  excludePastEvents?: boolean;
  excludeExpiredEvents?: boolean;
  maxEventAge?: number; // in days
  minEventAge?: number; // in days
  eventTypes?: string[];
  userLocation?: {lat: number; lng: number};
  maxDistance?: number; // in km
}

/**
 * Filter events based on various criteria to optimize performance
 */
export const filterEvents = (events: Event[], options: EventFilterOptions = {}): Event[] => {
  const {
    excludePastEvents = true,
    excludeExpiredEvents = true,
    maxEventAge,
    minEventAge,
    eventTypes,
    userLocation,
    maxDistance,
  } = options;

  const now = new Date();

  return events.filter(event => {
    // Filter past events
    if (excludePastEvents && event.end_date) {
      const endDate = new Date(event.end_date);
      if (endDate < now) {
        console.log(`🗑️ Filtering past event: ${event.title} (ended ${endDate.toISOString()})`);
        return false;
      }
    }

    // DISABLED: Client-side filtering should not be needed since API already filters
    // The API should return only valid, non-expired events
    // if (excludeExpiredEvents && event.start_date) {
    //   const startDate = new Date(event.start_date);
    //   if (startDate < now) {
    //     console.log(`🗑️ Filtering expired event: ${event.title} (started ${startDate.toISOString()})`);
    //     return false;
    //   }
    // }

    // Filter by maximum event age
    if (maxEventAge && event.start_date) {
      const startDate = new Date(event.start_date);
      const daysDiff = (startDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff > maxEventAge) {
        console.log(`🗑️ Filtering event too far in future: ${event.title} (${daysDiff} days away)`);
        return false;
      }
    }

    // Filter by minimum event age
    if (minEventAge && event.start_date) {
      const startDate = new Date(event.start_date);
      const daysDiff = (startDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff < minEventAge) {
        console.log(`🗑️ Filtering event too soon: ${event.title} (${daysDiff} days away)`);
        return false;
      }
    }

    // Filter by event types
    if (eventTypes && eventTypes.length > 0) {
      if (!eventTypes.includes(event.event_type)) {
        console.log(`🗑️ Filtering event type: ${event.title} (type: ${event.event_type})`);
        return false;
      }
    }

    // Filter by distance from user location
    if (userLocation && maxDistance && event.coords) {
      const distance = calculateDistance(userLocation.lat, userLocation.lng, event.coords.lat, event.coords.long);
      if (distance > maxDistance) {
        console.log(`🗑️ Filtering distant event: ${event.title} (${distance.toFixed(1)}km away)`);
        return false;
      }
    }

    return true;
  });
};

/**
 * Optimize API parameters to prevent fetching unnecessary data
 */
export const optimizeEventParams = (params: GetEventsParams): GetEventsParams => {
  const optimizedParams = {...params};

  // Always exclude past events by default
  if (!optimizedParams.timeframe || optimizedParams.timeframe === 'all_time') {
    optimizedParams.timeframe = 'future';
  }

  // Map 'upcoming' to 'future' for API compatibility
  if (optimizedParams.timeframe === 'upcoming') {
    optimizedParams.timeframe = 'future';
  }

  // Set default timeframe to 'future' if not specified
  if (!optimizedParams.timeframe) {
    optimizedParams.timeframe = 'future';
  }

  // Limit distance for better performance - increased cap for radius expansion
  if (optimizedParams.distance_km && optimizedParams.distance_km > 2000) {
    optimizedParams.distance_km = 2000; // Cap at 2000km to allow radius expansion
  }

  // Ensure distance_km is always an integer for API compatibility
  if (optimizedParams.distance_km) {
    optimizedParams.distance_km = Math.round(optimizedParams.distance_km);
  }

  // Set default limit to 100 events for better coverage
  if (!optimizedParams.limit) {
    optimizedParams.limit = 100; // Default to 100 events
  }

  // Cap at maximum limit if needed
  if (optimizedParams.limit > 100) {
    optimizedParams.limit = 100; // Maximum 100 events per request
  }

  // Add backend date filtering for better performance
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const futureDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000); // 1 year from now

  // Always filter out past events by setting start_date_after to today (YYYY-MM-DD format)
  if (optimizedParams.timeframe === 'future' || optimizedParams.timeframe === 'upcoming') {
    if (!optimizedParams.start_date_after) {
      optimizedParams.start_date_after = today.toISOString().split('T')[0]; // YYYY-MM-DD format
    }
  }

  // Add start_date_before parameter to limit future events (optional)
  if (!optimizedParams.start_date_before && optimizedParams.timeframe === 'future') {
    optimizedParams.start_date_before = futureDate.toISOString().split('T')[0]; // YYYY-MM-DD format
  }

  return optimizedParams;
};

/**
 * Calculate distance between two coordinates using Haversine formula
 */
export const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLng = toRadians(lng2 - lng1);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLng / 2) * Math.sin(dLng / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

/**
 * Convert degrees to radians
 */
const toRadians = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

/**
 * Check if event is relevant based on user preferences and location
 */
export const isEventRelevant = (
  event: Event,
  userPreferences?: {
    categories?: number[];
    maxDistance?: number;
    location?: {lat: number; lng: number};
    eventTypes?: string[];
  },
): boolean => {
  if (!userPreferences) return true;

  const {categories, maxDistance, location, eventTypes} = userPreferences;

  // Check event type preference
  if (eventTypes && eventTypes.length > 0) {
    if (!eventTypes.includes(event.event_type)) {
      return false;
    }
  }

  // Check category preference
  if (categories && categories.length > 0 && event.subcategory_id) {
    if (!categories.includes(event.subcategory_id)) {
      return false;
    }
  }

  // Check distance preference
  if (maxDistance && location && event.coords) {
    const distance = calculateDistance(location.lat, location.lng, event.coords.lat, event.coords.long);
    if (distance > maxDistance) {
      return false;
    }
  }

  return true;
};

/**
 * Sort events by relevance score
 */
export const sortEventsByRelevance = (
  events: Event[],
  userPreferences?: {
    location?: {lat: number; lng: number};
    categories?: number[];
    eventTypes?: string[];
  },
): Event[] => {
  if (!userPreferences) return events;

  return events.sort((a, b) => {
    let scoreA = 0;
    let scoreB = 0;

    // Distance score (closer is better)
    if (userPreferences.location && a.coords && b.coords) {
      const distanceA = calculateDistance(
        userPreferences.location.lat,
        userPreferences.location.lng,
        a.coords.lat,
        a.coords.long,
      );
      const distanceB = calculateDistance(
        userPreferences.location.lat,
        userPreferences.location.lng,
        b.coords.lat,
        b.coords.long,
      );

      // Inverse distance score (closer = higher score)
      scoreA += Math.max(0, 100 - distanceA);
      scoreB += Math.max(0, 100 - distanceB);
    }

    // Category preference score
    if (userPreferences.categories && userPreferences.categories.length > 0) {
      if (a.subcategory_id && userPreferences.categories.includes(a.subcategory_id)) {
        scoreA += 50;
      }
      if (b.subcategory_id && userPreferences.categories.includes(b.subcategory_id)) {
        scoreB += 50;
      }
    }

    // Event type preference score
    if (userPreferences.eventTypes && userPreferences.eventTypes.length > 0) {
      if (userPreferences.eventTypes.includes(a.event_type)) {
        scoreA += 30;
      }
      if (userPreferences.eventTypes.includes(b.event_type)) {
        scoreB += 30;
      }
    }

    // Popularity score (likes count)
    scoreA += (a.likes_count || 0) * 0.1;
    scoreB += (b.likes_count || 0) * 0.1;

    // Time relevance score (sooner events get higher score, but not too soon)
    const now = new Date().getTime();
    if (a.start_date && b.start_date) {
      const timeA = new Date(a.start_date).getTime();
      const timeB = new Date(b.start_date).getTime();

      const daysA = (timeA - now) / (1000 * 60 * 60 * 24);
      const daysB = (timeB - now) / (1000 * 60 * 60 * 24);

      // Optimal range is 1-30 days
      const optimalA = Math.max(0, 30 - Math.abs(daysA - 15));
      const optimalB = Math.max(0, 30 - Math.abs(daysB - 15));

      scoreA += optimalA;
      scoreB += optimalB;
    }

    return scoreB - scoreA; // Higher score first
  });
};

/**
 * Deduplicate events based on ID and title similarity
 */
export const deduplicateEvents = (events: Event[]): Event[] => {
  const seen = new Set<number>();
  const titleSeen = new Set<string>();

  return events.filter(event => {
    // Check by ID first
    if (seen.has(event.event_id)) {
      return false;
    }
    seen.add(event.event_id);

    // Check by title/name similarity (basic deduplication)
    const eventTitle = event.name || '';
    const normalizedTitle = eventTitle.toLowerCase().trim();
    if (normalizedTitle && titleSeen.has(normalizedTitle)) {
      return false;
    }
    if (normalizedTitle) {
      titleSeen.add(normalizedTitle);
    }

    return true;
  });
};
