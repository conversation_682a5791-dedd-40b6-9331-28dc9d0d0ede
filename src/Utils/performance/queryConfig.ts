import {QueryClient} from 'react-query';

// Enhanced React Query configuration for optimal performance
export const createOptimizedQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Enable caching with smart defaults
        cacheTime: 1000 * 60 * 30, // 30 minutes cache time
        staleTime: 1000 * 60 * 5, // 5 minutes stale time

        // Background refetching for fresh data
        refetchOnWindowFocus: true,
        refetchOnReconnect: true,
        refetchOnMount: true,

        // Retry configuration
        retry: (failureCount, error: any) => {
          // Don't retry on 4xx errors (client errors)
          if (error?.response?.status >= 400 && error?.response?.status < 500) {
            return false;
          }
          // Retry up to 3 times for other errors
          return failureCount < 3;
        },
        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),

        // Performance optimizations
        keepPreviousData: true, // Keep previous data while fetching new data
        notifyOnChangeProps: 'tracked', // Only re-render when tracked properties change
      },
      mutations: {
        // Retry mutations once on network errors
        retry: (failureCount, error: any) => {
          if (error?.response?.status >= 400 && error?.response?.status < 500) {
            return false;
          }
          return failureCount < 1;
        },
      },
    },
  });
};

// Query key factories for consistent cache management
export const queryKeys = {
  events: {
    all: ['events'] as const,
    lists: () => [...queryKeys.events.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.events.lists(), filters] as const,
    details: () => [...queryKeys.events.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.events.details(), id] as const,
    search: (query: string) => [...queryKeys.events.all, 'search', query] as const,
    nearby: (lat: number, lng: number, radius: number) =>
      [...queryKeys.events.all, 'nearby', {lat, lng, radius}] as const,
  },
  locations: {
    all: ['locations'] as const,
    search: (query: string) => [...queryKeys.locations.all, 'search', query] as const,
  },
  subcategories: {
    all: ['subcategories'] as const,
  },
  user: {
    all: ['user'] as const,
    profile: () => [...queryKeys.user.all, 'profile'] as const,
    preferences: () => [...queryKeys.user.all, 'preferences'] as const,
  },
} as const;

// Cache invalidation helpers
export const invalidateQueries = {
  events: (queryClient: QueryClient) => {
    queryClient.invalidateQueries(queryKeys.events.all);
  },
  eventsList: (queryClient: QueryClient) => {
    queryClient.invalidateQueries(queryKeys.events.lists());
  },
  eventsSearch: (queryClient: QueryClient) => {
    queryClient.invalidateQueries([...queryKeys.events.all, 'search']);
  },
};

// Prefetch helpers
export const prefetchHelpers = {
  nearbyEvents: async (
    queryClient: QueryClient,
    lat: number,
    lng: number,
    radius: number,
    fetchFn: () => Promise<any>,
  ) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.events.nearby(lat, lng, radius),
      queryFn: fetchFn,
      staleTime: 1000 * 60 * 2, // 2 minutes for location-based data
    });
  },

  popularEvents: async (queryClient: QueryClient, fetchFn: () => Promise<any>) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.events.list({tab: 'popular', limit: 20}),
      queryFn: fetchFn,
      staleTime: 1000 * 60 * 10, // 10 minutes for popular events
    });
  },
};
