import moment from 'moment';
import {ChatMessageType, ConciergeChatMessageType} from '~types/chat';
import OfflineChatStorage from '~services/OfflineChatStorage/OfflineChatStorage';

type Message = ChatMessageType | ConciergeChatMessageType;
interface Timestamped {
  timestamp: string;
}

type DateHeaderType = {
  timestamp: string;
  message: string;
  type: 'date';
};

type ChatOrDateHeaderType<T> = T | DateHeaderType;
export const getLastMessage = <T extends Message>(messages: T[]): T | null => {
  if (!Array.isArray(messages) || messages.length === 0) {
    return null;
  }
  const lastKey = messages.length - 1;
  return messages[lastKey];
};

/**
 * Get the last message including unsent messages from offline storage
 * This function combines Firestore messages with unsent messages to find the true last message
 */
export const getLastMessageWithUnsent = async <T extends Message>(
  messages: T[],
  chatId: string,
): Promise<T | any | null> => {
  try {
    // Get the last Firestore message
    const lastFirestoreMessage = getLastMessage(messages);

    // Get unsent messages for this chat
    const unsentMessages = await OfflineChatStorage.getUnsentMessages();
    const chatUnsentMessages = unsentMessages.filter(msg => msg.chatId === chatId);

    if (chatUnsentMessages.length === 0) {
      // No unsent messages, return the last Firestore message
      return lastFirestoreMessage;
    }

    // Find the most recent unsent message
    const lastUnsentMessage = chatUnsentMessages.reduce((latest, current) => {
      return new Date(current.timestamp) > new Date(latest.timestamp) ? current : latest;
    });

    // Compare timestamps to determine which is more recent
    if (!lastFirestoreMessage) {
      // No Firestore messages, return the unsent message formatted as a chat message
      return {
        message: lastUnsentMessage.text,
        text: lastUnsentMessage.text,
        timestamp: lastUnsentMessage.timestamp,
        sender: lastUnsentMessage.userName,
        sender_id: lastUnsentMessage.userId,
        senderId: lastUnsentMessage.userId,
        status: lastUnsentMessage.status,
        messageStatus: lastUnsentMessage.status,
        isPending: true,
      };
    }

    const firestoreTime = new Date(lastFirestoreMessage.timestamp).getTime();
    const unsentTime = new Date(lastUnsentMessage.timestamp).getTime();

    if (unsentTime > firestoreTime) {
      // Unsent message is more recent
      return {
        message: lastUnsentMessage.text,
        text: lastUnsentMessage.text,
        timestamp: lastUnsentMessage.timestamp,
        sender: lastUnsentMessage.userName,
        sender_id: lastUnsentMessage.userId,
        senderId: lastUnsentMessage.userId,
        status: lastUnsentMessage.status,
        messageStatus: lastUnsentMessage.status,
        isPending: true,
      };
    } else {
      // Firestore message is more recent
      return lastFirestoreMessage;
    }
  } catch (error) {
    console.error('Error getting last message with unsent:', error);
    // Fallback to regular last message
    return getLastMessage(messages);
  }
};

export const addDateHeaders = <T extends Timestamped>(messagesArr: T[]): ChatOrDateHeaderType<T>[] => {
  const newMessages = [];
  const dayFormat = 'YYYY-MM-DD';

  for (let i = 0; i < messagesArr.length; i++) {
    const currentMessage = messagesArr[i];
    const currentMessageDate = currentMessage?.timestamp;
    const formattedDate = moment(currentMessageDate).format(dayFormat);

    if (i === 0 || formattedDate !== moment(messagesArr[i - 1]?.timestamp).format(dayFormat)) {
      let displayDate = '';
      if (moment().isSame(formattedDate, 'day')) {
        displayDate = 'Today';
      } else if (moment().subtract(1, 'days').isSame(formattedDate, 'day')) {
        displayDate = 'Yesterday';
      } else {
        displayDate = moment(currentMessageDate).format('DD MMMM YYYY');
      }

      newMessages.push({
        timestamp: currentMessage.timestamp,
        message: displayDate,
        type: 'date',
      });
    }

    newMessages.push(currentMessage);
  }

  return newMessages;
};
