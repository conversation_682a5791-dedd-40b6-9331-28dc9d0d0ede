import {useEffect, createContext, useContext, useRef, useCallback} from 'react';
import FirebaseAuth from '~services/FirebaseAuthService';
import {
  ACTIVE_CHAT_REQUEST_ACTIONS,
  ChatContextType,
  ChatProviderProps,
  WEBSOCKET_MESSAGE_ACTION,
  WEBSOCKET_TYPE,
  WebsocketCreateMessageResponse,
  WebsocketDeleteMessageResponse,
  WebsocketErrorMessageResponse,
} from '~types/chat';
import {useChatStore} from './zustand';
import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';
import {sendMessage} from './helpers/sendMessage';
import {deleteMessage} from './helpers/deleteMessage';
import {activeMessage} from './helpers/activeMessage';
import {inAppNotification} from './helpers/inAppNotification';
import {useQueryClient} from 'react-query';
import {AppState} from 'react-native';

const ChatContext = createContext<ChatContextType>({
  sendMessage: () => {},
  deleteMessage: () => {},
  enterChat: () => {},
  leaveChat: () => {},
});

const ChatProvider = (props: ChatProviderProps) => {
  const socketRef = useRef<WebSocket | null>(null);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const activeChatId = useRef<number | null>(null);
  const appState = useRef<string | null>(null);
  const {addMessage, removeMessage, updateChatsListWithNewMessage, markAsRead, increaseUnreadCount} = useChatStore();
  const {navigate} = useNavigation<NavigationProps>();

  const clearSubscriptions = () => {
    intervalRef.current && clearInterval(intervalRef.current);
    socketRef.current?.close();
    console.log('closed', WebSocket.OPEN);
  };

  const enterChat = useCallback(
    (chatId: number) => {
      activeChatId.current = chatId;
      markAsRead({userId: auth().currentUser?.uid || '', chatId});
      activeMessage(socketRef)({chatId, action: ACTIVE_CHAT_REQUEST_ACTIONS.JOIN});
    },
    [markAsRead],
  );

  const leaveChat = (chatId: number) => {
    activeChatId.current = null;
    activeMessage(socketRef)({chatId, action: ACTIVE_CHAT_REQUEST_ACTIONS.LEAVE});
  };

  const asyncSocket = useCallback(async () => {
    const token = await FirebaseAuth.getAuthToken();
    if (!token) {
      return;
    }

    const socket = new WebSocket('wss://pyxi-image-qa-dn66uttsza-nw.a.run.app/chats/ws', null, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    const interval = setInterval(() => {
      if (socket.readyState === WebSocket.OPEN) {
        socket.send('Ping');
        console.log('Ping');
      } else {
        console.log('WebSocket is not open. Cannot send Ping.');
      }
    }, 15000);

    socketRef.current = socket;
    intervalRef.current = interval;

    socket.onopen = () => {
      console.log('WebSocket opened');
    };

    socket.onerror = e => {
      console.log('WebSocket error', e);
    };

    socket.onmessage = event => {
      if (event.data === 'Pong') {
        console.log('Pong');
        return;
      }

      const receivedMessage = JSON.parse(event.data);

      if (receivedMessage?.type === WEBSOCKET_TYPE.ERROR) {
        console.error(receivedMessage as WebsocketErrorMessageResponse);
        return;
      }

      if (receivedMessage?.action === WEBSOCKET_MESSAGE_ACTION.DELETE) {
        if (!receivedMessage?.data?.chat_id) {
          return;
        }

        removeMessage({messageId: (receivedMessage as WebsocketDeleteMessageResponse)?.data.message_id});
        return;
      }

      if ((receivedMessage as WebsocketCreateMessageResponse).data.chat_id !== activeChatId.current) {
        const message = receivedMessage as WebsocketCreateMessageResponse;

        increaseUnreadCount({chatId: message?.data.chat_id, userId: auth().currentUser?.uid || ''});
        inAppNotification({
          title: `${message.data.user.first_name} ${message.data.user.last_name ? message.data.user?.last_name : ''}`,
          description: message.data.text,
          imageSource: message.data.user.photo ? {uri: message.data.user.photo} : undefined,
          onPress: () => navigate(SCREENS.CHAT_STACK, {key: message.data.chat_id}),
        });
      }

      addMessage(receivedMessage.data);
      updateChatsListWithNewMessage(receivedMessage as WebsocketCreateMessageResponse);
    };

    socket.onclose = () => console.log('WebSocket closed');
  }, [addMessage, updateChatsListWithNewMessage, removeMessage, increaseUnreadCount, navigate]);

  useEffect(() => {
    asyncSocket();

    return clearSubscriptions;
  }, [asyncSocket]);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', async nextAppState => {
      if (appState.current?.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App has come to the foreground!');
        await asyncSocket();
      } else {
        clearSubscriptions();
      }

      appState.current = nextAppState;
      console.log('AppState', appState.current);
    });

    return () => {
      subscription.remove();
      clearSubscriptions();
    };
  }, [asyncSocket]);

  return (
    <ChatContext.Provider
      value={{
        sendMessage: sendMessage(socketRef),
        deleteMessage: deleteMessage(socketRef),
        enterChat,
        leaveChat,
      }}
      {...props}
    />
  );
};

export default ChatProvider;
export const useChatContext = (): ChatContextType => useContext(ChatContext);
