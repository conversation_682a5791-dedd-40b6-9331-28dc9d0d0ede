import React, {useState, useEffect} from 'react';
import {View, Text, Alert, ActivityIndicator} from 'react-native';
import {Formik} from 'formik';
import * as yup from 'yup';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius, shadows} from '~constants/design';
import {ModernTextInput} from '~components/ModernTextInput';
import {ModernButton} from '~components/ModernButton';
import {ModernCard} from '~components/ModernCard';
import FirebaseAuth from '~services/FirebaseAuthService';
import {useOfflineUserData} from '~hooks/offline/useOfflineUserData';
import {useTranslation} from 'react-i18next';
import Animated, {FadeIn, FadeInDown} from 'react-native-reanimated';

const validationSchema = yup.object().shape({
  email: yup
    .string()
    .email('Enter a valid email')
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Enter a valid email')
    .required('Email is required'),
  password: yup.string().required('Password is required'),
});

interface EnhancedLoginFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  showOfflineStatus?: boolean;
}

const EnhancedLoginForm: React.FC<EnhancedLoginFormProps> = ({
  onSuccess,
  onError,
  showOfflineStatus = true,
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  
  // Offline user data management
  const {
    isOffline,
    hasOfflineData,
    syncStatus,
    lastSync,
    userData,
  } = useOfflineUserData();

  // Clear error when user starts typing
  const clearError = () => {
    if (loginError) {
      setLoginError(null);
    }
  };

  const handleLogin = async (values: {email: string; password: string}) => {
    try {
      setIsLoading(true);
      setLoginError(null);

      const result = await FirebaseAuth.signInEmailPassword(values.email, values.password, setIsLoading);
      
      if (result?.error) {
        setLoginError(result.error);
        onError?.(result.error);
      } else if (result?.user) {
        console.log('✅ Login successful');
        onSuccess?.();
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Login failed. Please try again.';
      setLoginError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const styles = {
    container: {
      flex: 1,
    },
    formCard: {
      marginBottom: spacing.lg,
      ...shadows.none,
    },
    formTitle: {
      fontSize: typography.fontSize['2xl'],
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
      textAlign: 'center' as const,
      marginBottom: spacing.sm,
    },
    formSubtitle: {
      fontSize: typography.fontSize.base,
      color: colors.textSecondary,
      textAlign: 'center' as const,
      marginBottom: spacing.xl,
      lineHeight: typography.fontSize.base * 1.5,
    },
    inputContainer: {
      marginBottom: spacing.lg,
    },
    errorContainer: {
      backgroundColor: colors.errorLight || colors.error + '10',
      borderRadius: borderRadius.md,
      padding: spacing.md,
      marginBottom: spacing.lg,
      borderLeftWidth: 4,
      borderLeftColor: colors.error,
    },
    errorText: {
      fontSize: typography.fontSize.sm,
      color: colors.error,
      fontWeight: typography.fontWeight.medium,
    },
    offlineStatusContainer: {
      backgroundColor: isOffline ? colors.warning + '10' : colors.success + '10',
      borderRadius: borderRadius.md,
      padding: spacing.sm,
      marginBottom: spacing.md,
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
    },
    offlineStatusText: {
      fontSize: typography.fontSize.xs,
      color: isOffline ? colors.warning : colors.success,
      fontWeight: typography.fontWeight.medium,
      marginLeft: spacing.xs,
    },
    offlineDataContainer: {
      backgroundColor: colors.surface,
      borderRadius: borderRadius.md,
      padding: spacing.md,
      marginBottom: spacing.lg,
      borderWidth: 1,
      borderColor: colors.border,
    },
    offlineDataTitle: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textPrimary,
      marginBottom: spacing.xs,
    },
    offlineDataText: {
      fontSize: typography.fontSize.xs,
      color: colors.textSecondary,
      lineHeight: typography.fontSize.xs * 1.4,
    },
    syncStatusContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginTop: spacing.xs,
    },
    syncStatusText: {
      fontSize: typography.fontSize.xs,
      color: colors.textSecondary,
      marginLeft: spacing.xs,
    },
    loadingOverlay: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: colors.background + 'E6',
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      zIndex: 1000,
    },
    loadingContainer: {
      backgroundColor: colors.surface,
      borderRadius: borderRadius.xl,
      padding: spacing.xl,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      ...shadows.lg,
      minWidth: 160,
    },
    loadingText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      color: colors.textPrimary,
      marginTop: spacing.md,
      textAlign: 'center' as const,
    },
  };

  const getSyncStatusIcon = () => {
    switch (syncStatus) {
      case 'syncing':
        return '🔄';
      case 'synced':
        return '✅';
      case 'failed':
        return '❌';
      default:
        return '⏸️';
    }
  };

  const formatLastSync = (timestamp: string | null) => {
    if (!timestamp) return 'Never';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  };

  return (
    <View style={styles.container}>
      {/* Offline Status Indicator */}
      {showOfflineStatus && (
        <Animated.View entering={FadeInDown.delay(100).duration(300)}>
          <View style={styles.offlineStatusContainer}>
            <Text>{isOffline ? '📱' : '🌐'}</Text>
            <Text style={styles.offlineStatusText}>
              {isOffline ? 'Offline Mode' : 'Online'}
            </Text>
          </View>
        </Animated.View>
      )}

      {/* Offline Data Status */}
      {hasOfflineData && (
        <Animated.View entering={FadeInDown.delay(200).duration(300)}>
          <View style={styles.offlineDataContainer}>
            <Text style={styles.offlineDataTitle}>Offline Data Available</Text>
            <Text style={styles.offlineDataText}>
              {userData ? `Cached profile for ${userData.first_name}` : 'User data cached locally'}
            </Text>
            <View style={styles.syncStatusContainer}>
              <Text>{getSyncStatusIcon()}</Text>
              <Text style={styles.syncStatusText}>
                Last sync: {formatLastSync(lastSync)}
              </Text>
            </View>
          </View>
        </Animated.View>
      )}

      {/* Login Form */}
      <FadeIn delay={300} duration={400}>
        <ModernCard variant="default" padding="xl" style={styles.formCard}>
          <Text style={styles.formTitle}>{t('signin.sign_in_title')}</Text>
          <Text style={styles.formSubtitle}>{t('signin.continue_event_journey')}</Text>

          {/* Error Display */}
          {loginError && (
            <Animated.View entering={FadeInDown.duration(300)}>
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{loginError}</Text>
              </View>
            </Animated.View>
          )}

          <Formik
            initialValues={{email: '', password: ''}}
            validationSchema={validationSchema}
            onSubmit={handleLogin}>
            {(formikProps) => (
              <>
                <View style={styles.inputContainer}>
                  <ModernTextInput
                    label={t('signin.email')}
                    placeholder="Enter your email"
                    value={formikProps.values.email}
                    onChangeText={(value) => {
                      clearError();
                      formikProps.handleChange('email')(value);
                    }}
                    errorText={formikProps.touched.email ? formikProps.errors.email : undefined}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    variant="outlined"
                    size="lg"
                  />
                </View>

                <View style={styles.inputContainer}>
                  <ModernTextInput
                    label={t('signin.password')}
                    placeholder="Enter your password"
                    value={formikProps.values.password}
                    onChangeText={(value) => {
                      clearError();
                      formikProps.handleChange('password')(value);
                    }}
                    errorText={formikProps.touched.password ? formikProps.errors.password : undefined}
                    isPassword
                    variant="outlined"
                    size="lg"
                  />
                </View>

                <ModernButton
                  onPress={() => formikProps.handleSubmit()}
                  title={t('signin.login')}
                  variant="primary"
                  size="lg"
                  fullWidth
                  loading={isLoading}
                  hapticFeedback
                  disabled={!formikProps.isValid || isLoading}
                />
              </>
            )}
          </Formik>
        </ModernCard>
      </FadeIn>

      {/* Loading Overlay */}
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Signing you in...</Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default EnhancedLoginForm;
