import React from 'react';
import {Modal, View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {ModernCloseIcon} from '~assets/icons';

interface IssueModalProps {
  visible: boolean;
  onClose: () => void;
  onTechnicalIssueClick: () => void;
  onEventIssueClick: () => void;
}

const IssueModal: React.FC<IssueModalProps> = ({visible, onClose, onTechnicalIssueClick, onEventIssueClick}) => {
  return (
    <Modal transparent visible={visible} animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Modern Close Icon in Top-Right Corner */}
          <TouchableOpacity onPress={onClose} style={styles.closeIcon}>
            <ModernCloseIcon size={20} variant="minimal" />
          </TouchableOpacity>

          <Text style={styles.title}>Need Assistance?</Text>
          <Text style={styles.message}>Is your question related to a technical issue or an event-related concern?</Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.button} onPress={onTechnicalIssueClick}>
              <Text style={styles.buttonText}>Technical Issue</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={onEventIssueClick}>
              <Text style={styles.buttonText}>Event Issue</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 30,
    margin: 20,
    maxWidth: 350,
    width: '90%',
    position: 'relative',
  },
  closeIcon: {
    position: 'absolute',
    top: 15,
    right: 15,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
    color: '#333',
    marginTop: 10,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 25,
    color: '#666',
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 12,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
  },
});

export default IssueModal;
