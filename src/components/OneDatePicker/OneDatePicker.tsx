import DatePicker from 'react-native-date-picker';
import moment from 'moment-timezone';
import styles from './styles';
import {Platform} from 'react-native';
import AndroidDatePicker from '~components/DatePicker/AndroidDatePicker';
import AndroidTimePicker from '../DateTimeModal/AndroidTimePicker';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  handleChooseDate: (value?: Date) => void;
  date?: Date;
  mixAvailableDate?: Date;
  maxAvailableDate?: Date;
  withoutMaxAvailableDate?: boolean;
  mode?: 'date' | 'time' | 'datetime';
}

const isAndroid = Platform.OS === 'android';

const OneDatePicker = ({
  handleChooseDate,
  date,
  withoutMaxAvailableDate = false,
  mixAvailableDate,
  maxAvailableDate,
  mode = 'date',
}: IProps) => {
  const {colors} = useTheme();

  return (
    <>
      {false ? (
        <>
          {mode === 'date' ? (
            <AndroidDatePicker
              date={date || moment().toDate()}
              onDateChange={handleChooseDate}
              minimumDate={mixAvailableDate}
              maximumDate={withoutMaxAvailableDate ? undefined : maxAvailableDate || moment().toDate()}
            />
          ) : (
            <AndroidTimePicker time={date || moment().toDate()} onTimeChange={handleChooseDate} />
          )}
        </>
      ) : (
        <DatePicker
          style={styles.container}
          locale="en-US"
          testID="dateTimeGPicker"
          textColor={colors.black}
          date={date || moment().toDate()}
          onDateChange={handleChooseDate}
          minimumDate={mixAvailableDate}
          maximumDate={withoutMaxAvailableDate ? undefined : maxAvailableDate || moment().toDate()}
          mode={mode}
        />
      )}
    </>
  );
};

export default OneDatePicker;
