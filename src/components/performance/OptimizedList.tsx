import React, {useCallback, useMemo, useRef, useState, useEffect} from 'react';
import {View, ViewToken} from 'react-native';
import {FlashList, FlashListProps} from '@shopify/flash-list';
import {
  createDynamicSizeCalculator,
  deferHeavyOperation,
  processInChunks,
} from '~Utils/performance/advancedOptimizations';
import {performanceMonitor} from '~Utils/performance/performanceMonitor';

interface OptimizedListProps<T> extends Omit<FlashListProps<T>, 'renderItem'> {
  data: T[];
  renderItem: (info: {item: T; index: number}) => React.ReactElement;
  keyExtractor?: (item: T, index: number) => string;

  // Performance optimizations
  enableDynamicSizing?: boolean;
  enableViewabilityTracking?: boolean;
  enablePrefetching?: boolean;
  prefetchDistance?: number;
  chunkSize?: number;

  // Advanced features
  onItemVisible?: (item: T, index: number) => void;
  onItemHidden?: (item: T, index: number) => void;
  onPrefetch?: (items: T[], startIndex: number) => void;

  // Memory management
  maxCachedItems?: number;
  recycleItems?: boolean;
}

const OptimizedList = <T extends any>({
  data,
  renderItem,
  keyExtractor,
  enableDynamicSizing = true,
  enableViewabilityTracking = true,
  enablePrefetching = true,
  prefetchDistance = 10,
  chunkSize = 50,
  onItemVisible,
  onItemHidden,
  onPrefetch,
  maxCachedItems = 100,
  recycleItems = true,
  ...props
}: OptimizedListProps<T>) => {
  const listRef = useRef<FlashList<T>>(null);
  const sizeCalculator = useMemo(() => createDynamicSizeCalculator(), []);
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const [prefetchedRanges, setPrefetchedRanges] = useState<Set<string>>(new Set());

  // Item cache for recycling
  const itemCache = useRef<Map<string, React.ReactElement>>(new Map());
  const renderCount = useRef(0);

  // Generate stable key
  const getItemKey = useCallback(
    (item: T, index: number): string => {
      if (keyExtractor) {
        return keyExtractor(item, index);
      }
      return `item-${index}`;
    },
    [keyExtractor],
  );

  // Optimized render item with caching
  const optimizedRenderItem = useCallback(
    ({item, index}: {item: T; index: number}) => {
      const key = getItemKey(item, index);

      // Use cached item if available and recycling is enabled
      if (recycleItems && itemCache.current.has(key)) {
        return itemCache.current.get(key)!;
      }

      // Track render performance
      const renderStart = Date.now();

      const element = renderItem({item, index});

      const renderTime = Date.now() - renderStart;
      if (renderTime > 16) {
        // More than one frame
        console.warn(`Slow render detected: ${renderTime}ms for item ${index}`);
      }

      // Cache the rendered element
      if (recycleItems) {
        // Limit cache size
        if (itemCache.current.size >= maxCachedItems) {
          const firstKey = itemCache.current.keys().next().value;
          itemCache.current.delete(firstKey);
        }
        itemCache.current.set(key, element);
      }

      renderCount.current++;
      return element;
    },
    [renderItem, getItemKey, recycleItems, maxCachedItems],
  );

  // Dynamic size estimation
  const getItemLayout = useCallback(
    (data: T[] | null | undefined, index: number) => {
      if (!enableDynamicSizing || !data) {
        return undefined;
      }

      const key = getItemKey(data[index], index);
      const estimatedSize = sizeCalculator.getEstimatedSize(key);

      return {
        length: estimatedSize,
        offset: estimatedSize * index,
        index,
      };
    },
    [enableDynamicSizing, getItemKey, sizeCalculator],
  );

  // Viewability tracking
  const onViewableItemsChanged = useCallback(
    ({viewableItems, changed}: {viewableItems: ViewToken[]; changed: ViewToken[]}) => {
      if (!enableViewabilityTracking) {
        return;
      }

      const newVisibleItems = new Set(viewableItems.map(item => item.index!));
      setVisibleItems(newVisibleItems);

      // Track visibility changes
      changed.forEach(({item, index, isViewable}) => {
        if (index !== null) {
          if (isViewable) {
            onItemVisible?.(item, index);
          } else {
            onItemHidden?.(item, index);
          }
        }
      });

      // Prefetch nearby items
      if (enablePrefetching && viewableItems.length > 0) {
        deferHeavyOperation(() => {
          prefetchNearbyItems(viewableItems);
        }, 'low');
      }
    },
    [enableViewabilityTracking, enablePrefetching, onItemVisible, onItemHidden],
  );

  // Prefetch nearby items
  const prefetchNearbyItems = useCallback(
    async (viewableItems: ViewToken[]) => {
      if (!enablePrefetching || !onPrefetch) {
        return;
      }

      const visibleIndices = viewableItems.map(item => item.index!).filter(i => i !== null);
      const minIndex = Math.min(...visibleIndices);
      const maxIndex = Math.max(...visibleIndices);

      // Calculate prefetch range
      const prefetchStart = Math.max(0, minIndex - prefetchDistance);
      const prefetchEnd = Math.min(data.length - 1, maxIndex + prefetchDistance);

      const rangeKey = `${prefetchStart}-${prefetchEnd}`;

      // Skip if already prefetched
      if (prefetchedRanges.has(rangeKey)) {
        return;
      }

      const itemsToPrefetch = data.slice(prefetchStart, prefetchEnd + 1);

      try {
        await processInChunks(
          itemsToPrefetch,
          async chunk => {
            onPrefetch(chunk, prefetchStart);
            return chunk;
          },
          chunkSize,
          5, // 5ms delay between chunks
        );

        setPrefetchedRanges(prev => new Set([...prev, rangeKey]));
      } catch (error) {
        console.warn('Prefetch failed:', error);
      }
    },
    [enablePrefetching, onPrefetch, prefetchDistance, data, chunkSize, prefetchedRanges],
  );

  // Performance monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      if (renderCount.current > 0) {
        performanceMonitor.recordRequest();
        renderCount.current = 0;
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Memory cleanup
  useEffect(() => {
    return () => {
      itemCache.current.clear();
    };
  }, []);

  // Optimized estimated item size
  const estimatedItemSize = useMemo(() => {
    if (enableDynamicSizing) {
      return sizeCalculator.getAverageSize();
    }
    return props.estimatedItemSize || 100;
  }, [enableDynamicSizing, sizeCalculator, props.estimatedItemSize]);

  // Viewability config
  const viewabilityConfig = useMemo(
    () => ({
      itemVisiblePercentThreshold: 50,
      minimumViewTime: 100,
    }),
    [],
  );

  return (
    <FlashList
      ref={listRef}
      data={data}
      renderItem={optimizedRenderItem}
      keyExtractor={getItemKey}
      estimatedItemSize={estimatedItemSize}
      // Performance optimizations
      removeClippedSubviews={true}
      maxToRenderPerBatch={chunkSize}
      windowSize={10}
      initialNumToRender={Math.min(20, data.length)}
      // Viewability tracking
      onViewableItemsChanged={enableViewabilityTracking ? onViewableItemsChanged : undefined}
      viewabilityConfig={viewabilityConfig}
      // Memory optimizations
      getItemLayout={enableDynamicSizing ? undefined : getItemLayout}
      {...props}
    />
  );
};

// Hook for list performance monitoring
export const useListPerformance = () => {
  const [metrics, setMetrics] = useState({
    renderCount: 0,
    averageRenderTime: 0,
    slowRenders: 0,
  });

  const trackRender = useCallback((renderTime: number) => {
    setMetrics(prev => ({
      renderCount: prev.renderCount + 1,
      averageRenderTime: (prev.averageRenderTime * prev.renderCount + renderTime) / (prev.renderCount + 1),
      slowRenders: prev.slowRenders + (renderTime > 16 ? 1 : 0),
    }));
  }, []);

  const resetMetrics = useCallback(() => {
    setMetrics({
      renderCount: 0,
      averageRenderTime: 0,
      slowRenders: 0,
    });
  }, []);

  return {metrics, trackRender, resetMetrics};
};

// Optimized list item wrapper
export const OptimizedListItem = React.memo<{
  children: React.ReactNode;
  onLayout?: (height: number) => void;
}>(({children, onLayout}) => {
  const handleLayout = useCallback(
    (event: any) => {
      const {height} = event.nativeEvent.layout;
      onLayout?.(height);
    },
    [onLayout],
  );

  return <View onLayout={handleLayout}>{children}</View>;
});

export default OptimizedList;
