import React, {useState, useEffect, useCallback} from 'react';
import {View, Text, ScrollView, StyleSheet, TouchableOpacity, Modal, Dimensions} from 'react-native';
import {performanceMonitor} from '~Utils/performance/performanceMonitor';
import {networkManager} from '~Utils/performance/networkOptimization';
import {bundleAnalyzer} from '~Utils/performance/bundleOptimization';
import {getRegistryStats} from '~Utils/performance/lazyLoading';

interface PerformanceDashboardProps {
  visible: boolean;
  onClose: () => void;
}

const {width, height} = Dimensions.get('window');

const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({visible, onClose}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'network' | 'bundle' | 'lazy'>('overview');
  const [metrics, setMetrics] = useState<any>({});
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Refresh metrics
  const refreshMetrics = useCallback(() => {
    const performanceMetrics = performanceMonitor.getMetrics();
    const networkStats = networkManager.getStats();
    const bundleStats = bundleAnalyzer.getStats();
    const lazyStats = getRegistryStats();

    setMetrics({
      performance: performanceMetrics,
      network: networkStats,
      bundle: bundleStats,
      lazy: lazyStats,
      memory: getMemoryUsage(),
    });
  }, []);

  // Auto-refresh when visible
  useEffect(() => {
    if (visible) {
      refreshMetrics();
      const interval = setInterval(refreshMetrics, 2000);
      setRefreshInterval(interval);
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [visible, refreshMetrics]);

  const renderOverview = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Performance Overview</Text>

        <MetricCard
          title="Search Latency"
          value={`${Math.round(metrics.performance?.averageSearchLatency || 0)}ms`}
          subtitle={`P95: ${Math.round(metrics.performance?.p95SearchLatency || 0)}ms`}
          status={getLatencyStatus(metrics.performance?.averageSearchLatency)}
        />

        <MetricCard
          title="Cache Hit Rate"
          value={`${Math.round(metrics.performance?.cacheHitRate || 0)}%`}
          subtitle={`${metrics.performance?.totalCacheOperations || 0} operations`}
          status={getCacheStatus(metrics.performance?.cacheHitRate)}
        />

        <MetricCard
          title="Network Requests"
          value={`${metrics.network?.total || 0}`}
          subtitle={`${Math.round(metrics.network?.hitRate || 0)}% cached`}
          status={getNetworkStatus(metrics.network?.hitRate)}
        />

        <MetricCard
          title="Bundle Size"
          value={formatBytes(metrics.bundle?.totalSize || 0)}
          subtitle={`${metrics.bundle?.moduleCount || 0} modules`}
          status="neutral"
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>System Health</Text>

        <MetricCard
          title="Memory Usage"
          value={formatBytes(metrics.memory?.usedJSHeapSize || 0)}
          subtitle={`${Math.round(((metrics.memory?.usedJSHeapSize || 0) / (metrics.memory?.totalJSHeapSize || 1)) * 100)}% used`}
          status={getMemoryStatus(metrics.memory)}
        />

        <MetricCard
          title="Error Rate"
          value={`${Math.round(metrics.performance?.errorRate || 0)}%`}
          subtitle={`${metrics.network?.failed || 0} failed requests`}
          status={getErrorStatus(metrics.performance?.errorRate)}
        />
      </View>
    </ScrollView>
  );

  const renderNetwork = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Network Performance</Text>

        <MetricCard
          title="Connection Quality"
          value={networkManager.getConnectionQuality()}
          subtitle={`${metrics.network?.networkState?.type || 'unknown'}`}
          status="neutral"
        />

        <MetricCard
          title="Request Queue"
          value={`${metrics.network?.pendingRequests || 0}`}
          subtitle="Pending requests"
          status="neutral"
        />

        <MetricCard
          title="Cache Size"
          value={`${metrics.network?.cacheSize || 0}`}
          subtitle="Cached responses"
          status="neutral"
        />

        <MetricCard
          title="Retry Rate"
          value={`${Math.round(((metrics.network?.retries || 0) / (metrics.network?.total || 1)) * 100)}%`}
          subtitle={`${metrics.network?.retries || 0} retries`}
          status={getRetryStatus(metrics.network?.retries, metrics.network?.total)}
        />
      </View>
    </ScrollView>
  );

  const renderBundle = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Bundle Analysis</Text>

        <MetricCard
          title="Total Bundle Size"
          value={formatBytes(metrics.bundle?.totalSize || 0)}
          subtitle={`${metrics.bundle?.moduleCount || 0} modules loaded`}
          status="neutral"
        />

        <MetricCard
          title="Average Load Time"
          value={`${Math.round(metrics.bundle?.averageLoadTime || 0)}ms`}
          subtitle="Per module"
          status={getLoadTimeStatus(metrics.bundle?.averageLoadTime)}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Largest Modules</Text>
        {metrics.bundle?.largestModules?.slice(0, 5).map((module: any, index: number) => (
          <View key={index} style={styles.moduleItem}>
            <Text style={styles.moduleName}>{module.name}</Text>
            <Text style={styles.moduleSize}>{formatBytes(module.size)}</Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );

  const renderLazy = () => (
    <ScrollView style={styles.tabContent}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Lazy Loading</Text>

        <MetricCard
          title="Registered Components"
          value={`${metrics.lazy?.total || 0}`}
          subtitle="Total lazy components"
          status="neutral"
        />

        <MetricCard
          title="Loaded Components"
          value={`${metrics.lazy?.loaded || 0}`}
          subtitle={`${Math.round(((metrics.lazy?.loaded || 0) / (metrics.lazy?.total || 1)) * 100)}% loaded`}
          status="neutral"
        />

        <MetricCard
          title="Pending Components"
          value={`${metrics.lazy?.pending || 0}`}
          subtitle="Not yet loaded"
          status="neutral"
        />
      </View>
    </ScrollView>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Performance Dashboard</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeText}>Close</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.tabs}>
          {['overview', 'network', 'bundle', 'lazy'].map(tab => (
            <TouchableOpacity
              key={tab}
              style={[styles.tab, activeTab === tab && styles.activeTab]}
              onPress={() => setActiveTab(tab as any)}>
              <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'network' && renderNetwork()}
        {activeTab === 'bundle' && renderBundle()}
        {activeTab === 'lazy' && renderLazy()}
      </View>
    </Modal>
  );
};

// Helper components
const MetricCard: React.FC<{
  title: string;
  value: string;
  subtitle: string;
  status: 'good' | 'warning' | 'error' | 'neutral';
}> = ({title, value, subtitle, status}) => (
  <View style={[styles.metricCard, styles[`${status}Card`]]}>
    <Text style={styles.metricTitle}>{title}</Text>
    <Text style={styles.metricValue}>{value}</Text>
    <Text style={styles.metricSubtitle}>{subtitle}</Text>
  </View>
);

// Helper functions
function getLatencyStatus(latency: number): 'good' | 'warning' | 'error' | 'neutral' {
  if (latency < 100) {
    return 'good';
  }
  if (latency < 300) {
    return 'warning';
  }
  return 'error';
}

function getCacheStatus(hitRate: number): 'good' | 'warning' | 'error' | 'neutral' {
  if (hitRate > 80) {
    return 'good';
  }
  if (hitRate > 50) {
    return 'warning';
  }
  return 'error';
}

function getNetworkStatus(hitRate: number): 'good' | 'warning' | 'error' | 'neutral' {
  if (hitRate > 70) {
    return 'good';
  }
  if (hitRate > 40) {
    return 'warning';
  }
  return 'error';
}

function getMemoryStatus(memory: any): 'good' | 'warning' | 'error' | 'neutral' {
  if (!memory) {
    return 'neutral';
  }
  const usage = memory.usedJSHeapSize / memory.totalJSHeapSize;
  if (usage < 0.7) {
    return 'good';
  }
  if (usage < 0.9) {
    return 'warning';
  }
  return 'error';
}

function getErrorStatus(errorRate: number): 'good' | 'warning' | 'error' | 'neutral' {
  if (errorRate < 1) {
    return 'good';
  }
  if (errorRate < 5) {
    return 'warning';
  }
  return 'error';
}

function getRetryStatus(retries: number, total: number): 'good' | 'warning' | 'error' | 'neutral' {
  const rate = (retries / total) * 100;
  if (rate < 5) {
    return 'good';
  }
  if (rate < 15) {
    return 'warning';
  }
  return 'error';
}

function getLoadTimeStatus(loadTime: number): 'good' | 'warning' | 'error' | 'neutral' {
  if (loadTime < 100) {
    return 'good';
  }
  if (loadTime < 500) {
    return 'warning';
  }
  return 'error';
}

function formatBytes(bytes: number): string {
  if (bytes === 0) {
    return '0 B';
  }
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getMemoryUsage() {
  if (global.performance && global.performance.memory) {
    return global.performance.memory;
  }
  return null;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 8,
  },
  closeText: {
    color: '#007AFF',
    fontSize: 16,
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  metricCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  goodCard: {
    borderLeftColor: '#4CAF50',
  },
  warningCard: {
    borderLeftColor: '#FF9800',
  },
  errorCard: {
    borderLeftColor: '#F44336',
  },
  neutralCard: {
    borderLeftColor: '#9E9E9E',
  },
  metricTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  metricSubtitle: {
    fontSize: 12,
    color: '#999',
  },
  moduleItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'white',
    borderRadius: 6,
    marginBottom: 8,
  },
  moduleName: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  moduleSize: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
});

export default PerformanceDashboard;
