import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {View, StyleSheet, ViewStyle, ImageStyle} from 'react-native';
import FastImage, {FastImageProps, Priority, ResizeMode} from 'react-native-fast-image';
import {createProgressiveImageLoader} from '~Utils/performance/advancedOptimizations';
import {memoryManager} from '~Utils/performance/advancedOptimizations';

interface OptimizedImageProps extends Omit<FastImageProps, 'source'> {
  source: {
    uri: string;
    lowQualityUri?: string;
    mediumQualityUri?: string;
    placeholder?: string;
  };
  style?: ImageStyle | ViewStyle;
  progressive?: boolean;
  lazy?: boolean;
  preload?: boolean;
  fallbackSource?: any;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: any) => void;
  cacheKey?: string;
}

const imageLoader = createProgressiveImageLoader();

// Global image cache with LRU eviction
class ImageCache {
  private cache = new Map<string, {data: string; lastAccessed: number; size: number}>();
  private maxSize = 50 * 1024 * 1024; // 50MB
  private currentSize = 0;

  set(key: string, data: string, size: number = 1024) {
    // Remove old entry if exists
    if (this.cache.has(key)) {
      this.currentSize -= this.cache.get(key)!.size;
    }

    // Evict if necessary
    while (this.currentSize + size > this.maxSize && this.cache.size > 0) {
      this.evictLRU();
    }

    this.cache.set(key, {
      data,
      lastAccessed: Date.now(),
      size,
    });
    this.currentSize += size;
  }

  get(key: string): string | null {
    const entry = this.cache.get(key);
    if (entry) {
      entry.lastAccessed = Date.now();
      return entry.data;
    }
    return null;
  }

  private evictLRU() {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      const entry = this.cache.get(oldestKey)!;
      this.currentSize -= entry.size;
      this.cache.delete(oldestKey);
    }
  }

  clear() {
    this.cache.clear();
    this.currentSize = 0;
  }

  getStats() {
    return {
      size: this.cache.size,
      currentSize: this.currentSize,
      maxSize: this.maxSize,
    };
  }
}

const globalImageCache = new ImageCache();

// Register cleanup with memory manager
memoryManager.addCleanupTask(() => {
  globalImageCache.clear();
});

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  source,
  style,
  progressive = true,
  lazy = false,
  preload = false,
  fallbackSource,
  onLoadStart,
  onLoadEnd,
  onError,
  cacheKey,
  ...props
}) => {
  const [currentSource, setCurrentSource] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isVisible, setIsVisible] = useState(!lazy);

  // Generate cache key
  const finalCacheKey = useMemo(() => {
    return cacheKey || `${source.uri}-${JSON.stringify(style)}`;
  }, [cacheKey, source.uri, style]);

  // Progressive loading logic
  const loadProgressively = useCallback(async () => {
    if (!progressive || !source.lowQualityUri) {
      setCurrentSource(source.uri);
      return;
    }

    try {
      // Check cache first
      const cached = globalImageCache.get(finalCacheKey);
      if (cached) {
        setCurrentSource(cached);
        setIsLoading(false);
        return;
      }

      // Load placeholder first
      if (source.placeholder) {
        setCurrentSource(source.placeholder);
      }

      // Load low quality
      if (source.lowQualityUri) {
        await imageLoader.preloadImage(source.lowQualityUri);
        setCurrentSource(source.lowQualityUri);
      }

      // Load medium quality
      if (source.mediumQualityUri) {
        await imageLoader.preloadImage(source.mediumQualityUri);
        setCurrentSource(source.mediumQualityUri);
      }

      // Load high quality
      await imageLoader.preloadImage(source.uri);
      setCurrentSource(source.uri);

      // Cache the final result
      globalImageCache.set(finalCacheKey, source.uri);
    } catch (error) {
      console.warn('Progressive image loading failed:', error);
      setHasError(true);
      onError?.(error);
    } finally {
      setIsLoading(false);
      onLoadEnd?.();
    }
  }, [source, progressive, finalCacheKey, onError, onLoadEnd]);

  // Preload effect
  useEffect(() => {
    if (preload && !lazy) {
      imageLoader.preloadImage(source.uri);
    }
  }, [preload, lazy, source.uri]);

  // Load image when visible
  useEffect(() => {
    if (isVisible && !currentSource && !hasError) {
      onLoadStart?.();
      setIsLoading(true);
      loadProgressively();
    }
  }, [isVisible, currentSource, hasError, loadProgressively, onLoadStart]);

  // Intersection observer for lazy loading
  const handleLayout = useCallback(() => {
    if (lazy && !isVisible) {
      setIsVisible(true);
    }
  }, [lazy, isVisible]);

  // Determine priority based on props
  const priority: Priority = useMemo(() => {
    if (preload) {
      return 'high';
    }
    if (lazy) {
      return 'low';
    }
    return 'normal';
  }, [preload, lazy]);

  // Determine resize mode
  const resizeMode: ResizeMode = useMemo(() => {
    return props.resizeMode || 'cover';
  }, [props.resizeMode]);

  if (hasError && fallbackSource) {
    return <FastImage source={fallbackSource} style={style} resizeMode={resizeMode} {...props} />;
  }

  if (!isVisible) {
    return <View style={style} onLayout={handleLayout} />;
  }

  if (!currentSource) {
    return (
      <View style={[style, styles.placeholder]} onLayout={handleLayout}>
        {/* Placeholder content */}
      </View>
    );
  }

  return (
    <FastImage
      source={{
        uri: currentSource,
        priority,
        cache: 'immutable',
      }}
      style={style}
      resizeMode={resizeMode}
      onLoadStart={() => {
        setIsLoading(true);
        onLoadStart?.();
      }}
      onLoadEnd={() => {
        setIsLoading(false);
        onLoadEnd?.();
      }}
      onError={error => {
        setHasError(true);
        setIsLoading(false);
        onError?.(error);
      }}
      {...props}
    />
  );
};

// Optimized image preloader hook
export const useImagePreloader = () => {
  const preloadImages = useCallback(async (uris: string[]) => {
    const promises = uris.map(uri => imageLoader.preloadImage(uri));
    try {
      await Promise.all(promises);
    } catch (error) {
      console.warn('Some images failed to preload:', error);
    }
  }, []);

  const preloadImageSet = useCallback(
    async (
      configs: Array<{
        lowQuality: string;
        mediumQuality: string;
        highQuality: string;
        placeholder?: string;
      }>,
    ) => {
      const promises = configs.map(config => imageLoader.preloadImageSet(config));
      try {
        await Promise.all(promises);
      } catch (error) {
        console.warn('Some image sets failed to preload:', error);
      }
    },
    [],
  );

  return {preloadImages, preloadImageSet};
};

// Image cache management hook
export const useImageCache = () => {
  const clearCache = useCallback(() => {
    globalImageCache.clear();
    FastImage.clearMemoryCache();
    FastImage.clearDiskCache();
  }, []);

  const getCacheStats = useCallback(() => {
    return globalImageCache.getStats();
  }, []);

  return {clearCache, getCacheStats};
};

const styles = StyleSheet.create({
  placeholder: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default OptimizedImage;
