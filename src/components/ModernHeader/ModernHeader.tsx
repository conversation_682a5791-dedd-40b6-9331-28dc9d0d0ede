import React from 'react';
import {View, Text, TouchableOpacity, Platform} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius, shadows} from '~constants/design';
import {haptics} from '~Utils/haptics';
import {ChevronIcon, ModernCloseIcon} from '~assets/icons';
import {NavigationProps} from '~types/navigation/navigation.type';
import Animated, {FadeInDown} from 'react-native-reanimated';

interface ModernHeaderProps {
  title?: string;
  subtitle?: string;
  onBackPress?: () => void;
  onClosePress?: () => void;
  rightComponent?: React.ReactNode;
  variant?: 'default' | 'minimal' | 'elevated';
  showBackButton?: boolean;
  showCloseButton?: boolean;
  backgroundColor?: string;
  textColor?: string;
}

const ModernHeader: React.FC<ModernHeaderProps> = ({
  title,
  subtitle,
  onBackPress,
  onClosePress,
  rightComponent,
  variant = 'default',
  showBackButton = true,
  showCloseButton = false,
  backgroundColor,
  textColor,
}) => {
  const {colors} = useTheme();
  const {top} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const {t} = useTranslation();

  const handleBackPress = () => {
    haptics.light();
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  const handleClosePress = () => {
    haptics.light();
    if (onClosePress) {
      onClosePress();
    } else {
      navigation.goBack();
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'minimal':
        return {
          backgroundColor: 'transparent',
          borderBottomWidth: 0,
          elevation: 0,
          shadowOpacity: 0,
        };
      case 'elevated':
        return {
          backgroundColor: backgroundColor || colors.surface,
          borderBottomWidth: 0,
          ...shadows.md,
        };
      default:
        return {
          backgroundColor: backgroundColor || colors.surface, // Use surface for consistency
          borderBottomWidth: 1,
          borderBottomColor: colors.border + '30',
        };
    }
  };

  const variantStyles = getVariantStyles();

  return (
    <Animated.View
      entering={FadeInDown.duration(400)}
      style={[
        {
          paddingTop: spacing.md,
          paddingBottom: spacing.md,
          paddingHorizontal: spacing.lg,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          minHeight: 56,
        },
        variantStyles,
      ]}>
      {/* Left Section - Back/Close Button */}
      <View style={{flexDirection: 'row', alignItems: 'center', flex: 1}}>
        {showBackButton && (
          <TouchableOpacity
            onPress={handleBackPress}
            style={{
              width: 40,
              height: 40,
              borderRadius: borderRadius.full,
              backgroundColor: variant === 'minimal' ? colors.surface + '80' : 'transparent',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: spacing.sm,
            }}
            activeOpacity={0.7}>
            <ChevronIcon color={textColor || colors.primary} />
          </TouchableOpacity>
        )}

        {showCloseButton && (
          <TouchableOpacity
            onPress={handleClosePress}
            style={{
              width: 40,
              height: 40,
              borderRadius: borderRadius.full,
              backgroundColor: variant === 'minimal' ? colors.surface + '80' : 'transparent',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: spacing.sm,
            }}
            activeOpacity={0.7}>
            <ModernCloseIcon size={20} color={textColor || colors.textPrimary} variant="minimal" />
          </TouchableOpacity>
        )}

        {/* Title Section */}
        {title && (
          <View style={{flex: 1}}>
            <Text
              style={{
                fontSize: typography.fontSize.lg,
                fontWeight: typography.fontWeight.bold,
                color: textColor || colors.textPrimary,
                marginBottom: subtitle ? spacing.xs : 0,
              }}
              numberOfLines={1}>
              {title}
            </Text>
            {subtitle && (
              <Text
                style={{
                  fontSize: typography.fontSize.sm,
                  fontWeight: typography.fontWeight.normal,
                  color: textColor || colors.textSecondary,
                }}
                numberOfLines={1}>
                {subtitle}
              </Text>
            )}
          </View>
        )}
      </View>

      {/* Right Section */}
      {rightComponent && <View style={{marginLeft: spacing.sm}}>{rightComponent}</View>}
    </Animated.View>
  );
};

export default ModernHeader;
