import React from 'react';
import {View, Text, TouchableOpacity, Platform} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius} from '~constants/design';
import {haptics} from '~Utils/haptics';
import {ChevronIcon, ModernCloseIcon} from '~assets/icons';
import Animated, {FadeInDown} from 'react-native-reanimated';

interface ModernOnboardingHeaderProps {
  title: string;
  subtitle?: string;
  step?: number;
  totalSteps?: number;
  onBackPress?: () => void;
  onSkipPress?: () => void;
  showProgress?: boolean;
  showSkip?: boolean;
}

const ModernOnboardingHeader: React.FC<ModernOnboardingHeaderProps> = ({
  title,
  subtitle,
  step,
  totalSteps,
  onBackPress,
  onSkipPress,
  showProgress = true,
  showSkip = false,
}) => {
  const {colors} = useTheme();
  const {top} = useSafeAreaInsets();

  const handleBackPress = () => {
    if (onBackPress) {
      haptics.light();
      onBackPress();
    }
  };

  const handleSkipPress = () => {
    if (onSkipPress) {
      haptics.light();
      onSkipPress();
    }
  };

  const progressPercentage = step && totalSteps ? (step / totalSteps) * 100 : 0;

  return (
    <Animated.View
      entering={FadeInDown.duration(400)}
      style={{
        backgroundColor: colors.background,
        paddingTop: Platform.OS === 'ios' ? top : top + spacing.md,
        paddingBottom: spacing.lg,
        paddingHorizontal: spacing.lg,
      }}>
      {/* Header Row */}
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: spacing.md,
        }}>
        {/* Back Button */}
        {onBackPress ? (
          <TouchableOpacity
            onPress={handleBackPress}
            style={{
              width: 40,
              height: 40,
              borderRadius: borderRadius.full,
              backgroundColor: colors.surface,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <ChevronIcon color={colors.textPrimary} />
          </TouchableOpacity>
        ) : (
          <View style={{width: 40}} />
        )}

        {/* Step Indicator */}
        {showProgress && step && totalSteps && (
          <View
            style={{
              backgroundColor: colors.surface,
              paddingHorizontal: spacing.md,
              paddingVertical: spacing.xs,
              borderRadius: borderRadius.full,
            }}>
            <Text
              style={{
                fontSize: typography.fontSize.sm,
                fontWeight: typography.fontWeight.medium,
                color: colors.textSecondary,
              }}>
              {step} of {totalSteps}
            </Text>
          </View>
        )}

        {/* Skip Button */}
        {showSkip && onSkipPress ? (
          <TouchableOpacity
            onPress={handleSkipPress}
            style={{
              paddingHorizontal: spacing.md,
              paddingVertical: spacing.xs,
            }}>
            <Text
              style={{
                fontSize: typography.fontSize.sm,
                fontWeight: typography.fontWeight.medium,
                color: colors.textSecondary,
              }}>
              Skip
            </Text>
          </TouchableOpacity>
        ) : (
          <View style={{width: 40}} />
        )}
      </View>

      {/* Progress Bar */}
      {showProgress && step && totalSteps && (
        <View
          style={{
            height: 4,
            backgroundColor: colors.border,
            borderRadius: borderRadius.full,
            marginBottom: spacing.lg,
            overflow: 'hidden',
          }}>
          <Animated.View
            style={{
              height: '100%',
              backgroundColor: colors.primary,
              borderRadius: borderRadius.full,
              width: `${progressPercentage}%`,
            }}
          />
        </View>
      )}

      {/* Title and Subtitle */}
      <View style={{alignItems: 'center'}}>
        <Text
          style={{
            fontSize: typography.fontSize['3xl'],
            fontWeight: typography.fontWeight.bold,
            color: colors.textPrimary,
            textAlign: 'center',
            marginBottom: subtitle ? spacing.xs : 0,
          }}>
          {title}
        </Text>
        {subtitle && (
          <Text
            style={{
              fontSize: typography.fontSize.base,
              fontWeight: typography.fontWeight.normal,
              color: colors.textSecondary,
              textAlign: 'center',
              lineHeight: typography.fontSize.base * typography.lineHeight.relaxed,
            }}>
            {subtitle}
          </Text>
        )}
      </View>
    </Animated.View>
  );
};

export default ModernOnboardingHeader;
