import React, {Component, ReactNode} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Alert} from 'react-native';
import {error as logError} from '~Utils/debugLogger';
import {lightTheme, darkTheme} from '~constants/colors';
import {useTheme} from '~contexts/ThemeContext';
import {useTranslation} from 'react-i18next';
import {isViewHierarchyError, logViewHierarchyError} from '~Utils/globalErrorHandler';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

interface ErrorFallbackProps {
  error?: Error;
  onRetry: () => void;
  onReport: () => void;
}

// Functional component that can use hooks for the error UI
const ErrorFallback: React.FC<ErrorFallbackProps> = ({error, onRetry, onReport}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.errorContainer}>
        <Text style={styles.title}>{t('generic.oops_something_went_wrong') || 'Oops! Something went wrong'}</Text>
        <Text style={styles.message}>
          {t('generic.unexpected_error') || 'We encountered an unexpected error. Please try again.'}
        </Text>

        {__DEV__ && error && (
          <View style={styles.debugContainer}>
            <Text style={styles.debugTitle}>Debug Info:</Text>
            <Text style={styles.debugText}>{error.message}</Text>
          </View>
        )}

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
            <Text style={styles.retryButtonText}>{t('generic.try_again') || 'Try Again'}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.reportButton} onPress={onReport}>
            <Text style={styles.reportButtonText}>{t('generic.report_issue') || 'Report Issue'}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {hasError: false};
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {hasError: true, error};
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Check if this is a view hierarchy error using our utility
    if (isViewHierarchyError(error)) {
      logError(`🔴 View Hierarchy Error: ${error.message}`);
      logViewHierarchyError(error);
      console.warn('🔴 View Hierarchy Error caught by ErrorBoundary:', {
        error: error.message,
        stack: error.stack?.split('\n').slice(0, 5).join('\n'),
        componentStack: errorInfo.componentStack?.split('\n').slice(0, 5).join('\n'),
        timestamp: new Date().toISOString(),
      });
    } else {
      logError(`ErrorBoundary caught error: ${error.message}`);
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({hasError: false, error: undefined});
  };

  handleReportError = () => {
    const {error} = this.state;
    if (error) {
      Alert.alert('Error Report', `An error occurred: ${error.message}\n\nWould you like to report this issue?`, [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Report',
          onPress: () => {
            // Here you could integrate with your error reporting service
            console.log('Error reported:', error);
          },
        },
      ]);
    }
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Use the functional component that can access theme context
      return <ErrorFallback error={this.state.error} onRetry={this.handleRetry} onReport={this.handleReportError} />;
    }

    return this.props.children;
  }
}

const createStyles = (colors: typeof lightTheme | typeof darkTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.gray100,
      padding: 20,
    },
    errorContainer: {
      backgroundColor: colors.white,
      borderRadius: 12,
      padding: 24,
      alignItems: 'center',
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      maxWidth: 350,
    },
    title: {
      fontSize: 20,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: 12,
      textAlign: 'center',
    },
    message: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
      marginBottom: 24,
    },
    debugContainer: {
      backgroundColor: colors.gray100,
      borderRadius: 8,
      padding: 12,
      marginBottom: 20,
      width: '100%',
    },
    debugTitle: {
      fontSize: 14,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: 8,
    },
    debugText: {
      fontSize: 12,
      color: colors.textSecondary,
      fontFamily: 'monospace',
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: 12,
    },
    retryButton: {
      backgroundColor: colors.statusBlue,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
    },
    retryButtonText: {
      color: colors.white,
      fontSize: 16,
      fontWeight: '600',
    },
    reportButton: {
      backgroundColor: colors.error,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
    },
    reportButtonText: {
      color: colors.white,
      fontSize: 16,
      fontWeight: '600',
    },
  });

export default ErrorBoundary;
