import React from 'react';
import {View, Text, TouchableOpacity, ActivityIndicator} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, shadows, typography} from '~constants/design';

import {FadeIn} from '~components/MicroInteractions/MicroInteractions';
import moment from 'moment';

interface ModernChatBubbleProps {
  message: string;
  timestamp: string;
  isOwn: boolean;
  senderName?: string;
  showSender?: boolean;
  messageStatus?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed' | 'pending';
  onPress?: () => void;
  onLongPress?: () => void;
  index?: number;
  maxWidth?: number;
}

const ModernChatBubble: React.FC<ModernChatBubbleProps> = ({
  message,
  timestamp,
  isOwn,
  senderName,
  showSender = false,
  messageStatus = 'sent',
  onPress,
  onLongPress,
  index = 0,
  maxWidth = 280,
}) => {
  const {colors} = useTheme();
  const pressScale = useSharedValue(1);

  const handlePressIn = () => {
    pressScale.value = withSpring(0.98, {damping: 15});
  };

  const handlePressOut = () => {
    pressScale.value = withSpring(1, {damping: 15});
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    }
  };

  const handleLongPress = () => {
    if (onLongPress) {
      onLongPress();
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{scale: pressScale.value}],
  }));

  const getBubbleColors = () => {
    if (isOwn) {
      return {
        backgroundColor: colors.primary,
        textColor: colors.white,
        borderColor: colors.primaryDark,
      };
    }
    return {
      backgroundColor: colors.surface,
      textColor: colors.textPrimary,
      borderColor: colors.border,
    };
  };

  const getMessageStatusIcon = () => {
    if (!isOwn) {
      return null;
    }

    switch (messageStatus) {
      case 'sending':
        return <ActivityIndicator size="small" color={colors.white} style={{width: 12, height: 12}} />;
      case 'pending':
        return (
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <ActivityIndicator size="small" color={colors.warning} style={{width: 10, height: 10, marginRight: 2}} />
            <Text style={{fontSize: 10, color: colors.warning, fontWeight: 'bold'}}>⏳</Text>
          </View>
        );
      case 'sent':
        return <Text style={{fontSize: 12, color: colors.white, fontWeight: 'bold'}}>✓</Text>;
      case 'delivered':
        return <Text style={{fontSize: 12, color: colors.white, fontWeight: 'bold'}}>✓✓</Text>;
      case 'read':
        return <Text style={{fontSize: 12, color: colors.success, fontWeight: 'bold'}}>✓✓</Text>;
      case 'failed':
        return <Text style={{fontSize: 12, color: colors.error, fontWeight: 'bold'}}>⚠</Text>;
      default:
        return null;
    }
  };

  const bubbleColors = getBubbleColors();
  const statusIcon = getMessageStatusIcon();

  return (
    <FadeIn delay={index * 50} duration={300}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: isOwn ? 'flex-end' : 'flex-start',
          marginVertical: spacing.xs,
          paddingHorizontal: spacing.md,
        }}>
        <Animated.View style={[animatedStyle, {maxWidth}]}>
          <TouchableOpacity
            onPress={handlePress}
            onLongPress={handleLongPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={1}
            style={{
              backgroundColor: bubbleColors.backgroundColor,
              borderRadius: borderRadius.xl,
              borderWidth: 1,
              borderColor: bubbleColors.borderColor,
              paddingHorizontal: spacing.md,
              paddingVertical: spacing.sm,
              ...shadows.sm,
              // Tail effect
              ...(isOwn
                ? {
                    borderBottomRightRadius: borderRadius.sm,
                    marginLeft: spacing.xl,
                  }
                : {
                    borderBottomLeftRadius: borderRadius.sm,
                    marginRight: spacing.xl,
                  }),
            }}>
            {/* Sender name for group chats */}
            {showSender && senderName && !isOwn && (
              <Text
                selectable={true}
                style={{
                  fontSize: typography.fontSize.xs,
                  fontWeight: typography.fontWeight.semibold,
                  color: colors.primary,
                  marginBottom: spacing.xs,
                }}>
                {senderName}
              </Text>
            )}

            {/* Message text */}
            <Text
              selectable={true}
              style={{
                fontSize: typography.fontSize.base,
                lineHeight: typography.fontSize.base * typography.lineHeight.normal,
                color: bubbleColors.textColor,
                fontWeight: typography.fontWeight.normal,
              }}>
              {message}
            </Text>

            {/* Timestamp and status */}
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'flex-end',
                alignItems: 'center',
                marginTop: spacing.xs,
                gap: spacing.xs,
              }}>
              <Text
                style={{
                  fontSize: typography.fontSize.xs,
                  color: isOwn ? colors.white : colors.textSecondary,
                  opacity: 0.8,
                }}>
                {moment(timestamp).calendar(null, {
                  sameDay: 'HH:mm',
                  lastDay: '[Yesterday] HH:mm',
                  lastWeek: 'ddd HH:mm',
                  sameElse: 'DD/MM/YY HH:mm',
                })}
              </Text>

              {statusIcon && (
                <View style={{justifyContent: 'center', alignItems: 'center', minWidth: 16}}>{statusIcon}</View>
              )}
            </View>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </FadeIn>
  );
};

export default ModernChatBubble;
