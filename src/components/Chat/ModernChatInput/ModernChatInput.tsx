import React, {useState, useRef, useEffect, useCallback} from 'react';
import {Pressable, TextInput, View, Text, Platform} from 'react-native';
import {SendArrow} from '~assets/icons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Animated, {
  Extrapolation,
  SharedValue,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import {useTheme} from '~contexts/ThemeContext';
import {haptics} from '~Utils/haptics';
import {spacing, borderRadius, shadows} from '~constants/design';
// import {useMessageStatus} from '~hooks/chat/useMessageStatus'; // Temporarily commented out
import {MESSAGE_STATUS} from '~types/chat/index';

const AnimatedTextInput = Animated.createAnimatedComponent(TextInput);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

interface ModernChatInputProps {
  submit: (text: string, tempMessageId?: string) => Promise<string | void>;
  h: SharedValue<number>;
  progress: SharedValue<number>;
  placeholder?: string;
  maxLength?: number;
  showCharacterCount?: boolean;
  disabled?: boolean;
  onMessageStatusChange?: (tempId: string, status: MESSAGE_STATUS, realMessageId?: string) => void;
  onTypingStart?: () => void;
  onTypingEnd?: () => void;
}

export default function ModernChatInput({
  submit,
  h,
  progress,
  placeholder = 'Type a message...',
  maxLength = 1000,
  showCharacterCount = false,
  disabled = false,
  onMessageStatusChange,
  onTypingStart,
  onTypingEnd,
}: ModernChatInputProps) {
  const {colors} = useTheme();
  const [textInput, setTextInput] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [inputHeight, setInputHeight] = useState(44);
  const {bottom} = useSafeAreaInsets();
  const textInputRef = useRef<TextInput>(null);
  // const {addPendingMessage, updateMessageStatus} = useMessageStatus(); // Temporarily commented out
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Animation values
  const sendButtonScale = useSharedValue(0.8);
  const sendButtonOpacity = useSharedValue(0.6);
  const inputBorderWidth = useSharedValue(1);
  const inputShadowOpacity = useSharedValue(0);

  // Update send button animation based on text input
  useEffect(() => {
    const hasText = textInput.trim().length > 0;
    sendButtonScale.value = withSpring(hasText ? 1 : 0.8, {damping: 15});
    sendButtonOpacity.value = withTiming(hasText ? 1 : 0.6, {duration: 200});
  }, [textInput, sendButtonScale, sendButtonOpacity]);

  // Update input styling based on focus
  useEffect(() => {
    inputBorderWidth.value = withTiming(isFocused ? 2 : 1, {duration: 200});
    inputShadowOpacity.value = withTiming(isFocused ? 0.1 : 0, {duration: 200});
  }, [isFocused, inputBorderWidth, inputShadowOpacity]);

  // Handle typing indicators
  const handleTyping = useCallback(() => {
    if (onTypingStart) {
      onTypingStart();
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      if (onTypingEnd) {
        onTypingEnd();
      }
    }, 2000);
  }, [onTypingStart, onTypingEnd]);

  // Container style with keyboard animation
  const containerStyle = useAnimatedStyle(
    () => ({
      flexDirection: 'row',
      alignItems: 'flex-end',
      paddingHorizontal: spacing.lg,
      paddingTop: spacing.md,
      backgroundColor: colors.surface,
      paddingBottom: interpolate(progress.value, [0, 1], [bottom || spacing.lg, spacing.md], Extrapolation.CLAMP),
      width: '100%',
      transform: [{translateY: -h.value}],
      borderTopWidth: 1,
      borderTopColor: colors.border,
    }),
    [colors, bottom],
  );

  // Input container style
  const inputContainerStyle = useAnimatedStyle(() => ({
    flex: 1,
    minHeight: Math.max(44, inputHeight),
    maxHeight: 120,
    borderRadius: borderRadius['2xl'],
    backgroundColor: colors.inputBackground,
    borderWidth: inputBorderWidth.value,
    borderColor: isFocused ? colors.primary : colors.inputBorder,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    marginRight: spacing.sm,
    justifyContent: 'center',
  }));

  // Send button style
  const sendButtonStyle = useAnimatedStyle(() => ({
    width: 44,
    height: 44,
    borderRadius: borderRadius.full,
    backgroundColor: textInput.trim() ? colors.primary : colors.gray300,
    justifyContent: 'center',
    alignItems: 'center',
    transform: [{scale: sendButtonScale.value}],
    opacity: sendButtonOpacity.value,
  }));

  // Character count style
  const characterCountStyle = useAnimatedStyle(() => ({
    opacity: withTiming(showCharacterCount && textInput.length > maxLength * 0.8 ? 1 : 0, {duration: 200}),
  }));

  // Handle message submission
  const handleSubmit = useCallback(async () => {
    const text = textInput.trim();
    if (!text || isSubmitting || disabled) {
      return;
    }

    // Generate a temporary message ID
    const tempMessageId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // Haptic feedback for send action
    haptics.medium();

    setIsSubmitting(true);

    // Add pending message with "sending" status
    // addPendingMessage(tempMessageId, text); // Temporarily commented out
    onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.SENDING);

    // Animate send button
    sendButtonScale.value = withSpring(0.9, {damping: 15}, () => {
      sendButtonScale.value = withSpring(1, {damping: 15});
    });

    // Clear typing indicator
    if (onTypingEnd) {
      onTypingEnd();
    }

    try {
      console.log('ModernChatInput: Attempting to send message:', text);
      const realMessageId = await submit(text, tempMessageId);
      console.log('ModernChatInput: Message sent successfully');

      // Update status to "sent"
      // updateMessageStatus(tempMessageId, MESSAGE_STATUS.SENT, realMessageId as string); // Temporarily commented out
      onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.SENT, realMessageId as string);

      // Clear the text input after successful submission
      setTextInput('');
      setInputHeight(44); // Reset input height
    } catch (error) {
      console.log('ModernChatInput: Failed to send message:', error);

      // Update status to "failed"
      // updateMessageStatus(tempMessageId, MESSAGE_STATUS.FAILED); // Temporarily commented out
      onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.FAILED);

      // Keep the text in the input on error
      haptics.error();
    } finally {
      setIsSubmitting(false);
    }
  }, [
    textInput,
    isSubmitting,
    disabled,
    // addPendingMessage, // Temporarily commented out
    onMessageStatusChange,
    sendButtonScale,
    onTypingEnd,
    submit,
    // updateMessageStatus, // Temporarily commented out
  ]);

  // Handle text changes
  const handleTextChange = useCallback(
    (text: string) => {
      if (text.length <= maxLength) {
        setTextInput(text);
        handleTyping();
      } else {
        haptics.warning();
      }
    },
    [maxLength, handleTyping],
  );

  // Handle content size change for multiline input
  const handleContentSizeChange = useCallback((event: any) => {
    const newHeight = Math.min(Math.max(44, event.nativeEvent.contentSize.height + 16), 120);
    setInputHeight(newHeight);
  }, []);

  // Handle focus events
  const handleFocus = useCallback(() => {
    setIsFocused(true);
    haptics.light();
  }, []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    // Stop typing indicator when input loses focus
    if (onTypingEnd) {
      onTypingEnd();
    }
  }, [onTypingEnd]);

  // Handle key press for send on enter
  const handleKeyPress = useCallback(
    (e: any) => {
      if (Platform.OS === 'web' && e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
        e.preventDefault();
        handleSubmit();
      }
    },
    [handleSubmit],
  );

  const isOverLimit = textInput.length > maxLength * 0.9;
  const characterCount = textInput.length;

  return (
    <Animated.View style={[containerStyle, shadows.sm]}>
      <Animated.View style={[inputContainerStyle, shadows.sm]}>
        <AnimatedTextInput
          ref={textInputRef}
          value={textInput}
          onChangeText={handleTextChange}
          onContentSizeChange={handleContentSizeChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          placeholderTextColor={colors.placeholderText}
          style={{
            flex: 1,
            fontSize: 16,
            lineHeight: 22,
            color: colors.inputText,
            textAlignVertical: 'top',
            paddingVertical: spacing.sm,
            minHeight: 20,
            maxHeight: 100,
          }}
          multiline={true}
          maxLength={maxLength}
          editable={!disabled && !isSubmitting}
          returnKeyType="send"
          onSubmitEditing={handleSubmit}
          scrollEnabled={true}
          enablesReturnKeyAutomatically={true}
        />

        {showCharacterCount && (
          <Animated.View style={[characterCountStyle, {position: 'absolute', right: spacing.sm, bottom: 2}]}>
            <Text
              style={{
                fontSize: 12,
                color: isOverLimit ? colors.error : colors.textSecondary,
                fontWeight: isOverLimit ? '600' : '400',
              }}>
              {characterCount}/{maxLength}
            </Text>
          </Animated.View>
        )}
      </Animated.View>

      <AnimatedPressable
        style={[sendButtonStyle, shadows.sm]}
        onPress={handleSubmit}
        disabled={!textInput.trim() || isSubmitting || disabled}
        android_ripple={{
          color: colors.white + '30',
          borderless: true,
        }}>
        <SendArrow isActive={!!textInput.trim() && !isSubmitting} />
      </AnimatedPressable>
    </Animated.View>
  );
}
