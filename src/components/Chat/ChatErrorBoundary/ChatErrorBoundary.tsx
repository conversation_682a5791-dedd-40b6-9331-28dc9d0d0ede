import React, {Component, ReactNode} from 'react';
import {View, Text, TouchableOpacity, ActivityIndicator} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {useTranslation} from 'react-i18next';
import {spacing, typography, borderRadius} from '~constants/design';
import {haptics} from '~Utils/haptics';
import {AlertTriangleIcon, RefreshIcon, WifiOffIcon} from '~assets/icons';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  onRetry?: () => Promise<void>;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorType: 'network' | 'general' | 'unknown';
}

interface ChatErrorFallbackProps {
  error?: Error;
  errorType: 'network' | 'general' | 'unknown';
  onRetry: () => void;
  onReport: () => void;
}

// Functional component for error UI that can use hooks
const ChatErrorFallback: React.FC<ChatErrorFallbackProps> = ({
  error,
  errorType,
  onRetry,
  onReport,
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const [isRetrying, setIsRetrying] = React.useState(false);

  const handleRetry = async () => {
    setIsRetrying(true);
    haptics.light();
    
    try {
      await onRetry();
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      haptics.error();
    } finally {
      setIsRetrying(false);
    }
  };

  const getErrorContent = () => {
    switch (errorType) {
      case 'network':
        return {
          icon: <WifiOffIcon width={48} height={48} color={colors.textSecondary} />,
          title: t('chat.network_error_title') || 'Connection Error',
          message: t('chat.network_error_message') || 'Unable to connect to chat servers. Please check your internet connection and try again.',
        };
      case 'general':
        return {
          icon: <AlertTriangleIcon width={48} height={48} color={colors.textSecondary} />,
          title: t('chat.error_title') || 'Chat Error',
          message: t('chat.error_message') || 'Something went wrong with the chat. Please try again.',
        };
      default:
        return {
          icon: <AlertTriangleIcon width={48} height={48} color={colors.textSecondary} />,
          title: t('generic.oops_something_went_wrong') || 'Oops! Something went wrong',
          message: t('generic.unexpected_error') || 'We encountered an unexpected error. Please try again.',
        };
    }
  };

  const {icon, title, message} = getErrorContent();

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: spacing.xl,
    }}>
      <View style={{
        backgroundColor: colors.surface,
        borderRadius: borderRadius.lg,
        padding: spacing.xl,
        alignItems: 'center',
        maxWidth: 320,
        shadowColor: colors.shadow,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
      }}>
        <View style={{marginBottom: spacing.md}}>
          {icon}
        </View>
        
        <Text style={{
          fontSize: typography.fontSize.lg,
          fontWeight: typography.fontWeight.semibold,
          color: colors.text,
          textAlign: 'center',
          marginBottom: spacing.sm,
        }}>
          {title}
        </Text>
        
        <Text style={{
          fontSize: typography.fontSize.base,
          color: colors.textSecondary,
          textAlign: 'center',
          lineHeight: 22,
          marginBottom: spacing.lg,
        }}>
          {message}
        </Text>

        <View style={{
          flexDirection: 'row',
          gap: spacing.md,
        }}>
          <TouchableOpacity
            onPress={handleRetry}
            disabled={isRetrying}
            style={{
              backgroundColor: colors.primary,
              paddingHorizontal: spacing.lg,
              paddingVertical: spacing.md,
              borderRadius: borderRadius.md,
              flexDirection: 'row',
              alignItems: 'center',
              opacity: isRetrying ? 0.7 : 1,
              flex: 1,
            }}
          >
            {isRetrying ? (
              <ActivityIndicator 
                size="small" 
                color={colors.white} 
                style={{marginRight: spacing.sm}}
              />
            ) : (
              <RefreshIcon 
                width={16} 
                height={16} 
                color={colors.white} 
                style={{marginRight: spacing.sm}}
              />
            )}
            <Text style={{
              color: colors.white,
              fontSize: typography.fontSize.base,
              fontWeight: typography.fontWeight.medium,
              textAlign: 'center',
              flex: 1,
            }}>
              {isRetrying 
                ? (t('chat.retrying') || 'Retrying...') 
                : (t('chat.try_again') || 'Try Again')
              }
            </Text>
          </TouchableOpacity>
        </View>

        {__DEV__ && error && (
          <View style={{
            marginTop: spacing.lg,
            padding: spacing.md,
            backgroundColor: colors.backgroundSecondary,
            borderRadius: borderRadius.sm,
            width: '100%',
          }}>
            <Text style={{
              fontSize: typography.fontSize.sm,
              color: colors.textSecondary,
              fontWeight: typography.fontWeight.medium,
              marginBottom: spacing.xs,
            }}>
              Debug Info:
            </Text>
            <Text style={{
              fontSize: typography.fontSize.xs,
              color: colors.textSecondary,
              fontFamily: 'monospace',
            }}>
              {error.message}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

class ChatErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      errorType: 'unknown',
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Determine error type based on error message
    const errorMessage = error.message.toLowerCase();
    let errorType: 'network' | 'general' | 'unknown' = 'unknown';

    if (
      errorMessage.includes('network') ||
      errorMessage.includes('fetch') ||
      errorMessage.includes('axios') ||
      errorMessage.includes('connection') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('internet') ||
      errorMessage.includes('offline')
    ) {
      errorType = 'network';
    } else if (
      errorMessage.includes('chat') ||
      errorMessage.includes('message') ||
      errorMessage.includes('firebase')
    ) {
      errorType = 'general';
    }

    return {
      hasError: true,
      error,
      errorType,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ChatErrorBoundary caught an error:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = async () => {
    this.setState({hasError: false, error: undefined});
    
    if (this.props.onRetry) {
      try {
        await this.props.onRetry();
      } catch (error) {
        console.error('Retry failed in ChatErrorBoundary:', error);
        // Error will be caught again by componentDidCatch if it's a render error
      }
    }
  };

  handleReportError = () => {
    haptics.light();
    // Could implement error reporting here
    console.log('Error reported by user');
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ChatErrorFallback
          error={this.state.error}
          errorType={this.state.errorType}
          onRetry={this.handleRetry}
          onReport={this.handleReportError}
        />
      );
    }

    return this.props.children;
  }
}

export default ChatErrorBoundary;
