import auth from '@react-native-firebase/auth';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment-timezone';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { SCREENS } from '~constants';
import { msgType } from '~constants/chat';
import { BUTTON_STATUS_ENUM, CHAT_TYPE_ENUM } from '~types/chat';
import { NavigationProps } from '~types/navigation/navigation.type';
import { useTheme } from '~contexts/ThemeContext';
import ModernChatBubble from '../ModernChatBubble/ModernChatBubble';
import { spacing, borderRadius, shadows, typography } from '~constants/design';
import { FadeIn } from '~components/MicroInteractions/MicroInteractions';

interface ChatItemProps {
  item: any;
  chatType?: any;
  index?: number;
}

export default function ChatItem({ item, chatType, index = 0 }: ChatItemProps) {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const uid = auth().currentUser?.uid;
  const navigation = useNavigation<NavigationProps>();

  const senderImage = item.sender_image;
  const senderName = item.senderName || item.sender || '';
  const senderId = item.senderId || item.sender_id;
  const isOwn = senderId === uid;
  const isImage = senderId !== uid && senderImage;

  const isSenderVisible = senderId !== uid && chatType !== CHAT_TYPE_ENUM.PRIVATE;

  const onEventClick = () => {
    const eventId = item.event.event_id;

    navigation.navigate(SCREENS.HOME_EVENT, { eventId: Number(eventId) });
  };

  const handleMessagePress = () => {
    // Handle message press (e.g., show message details)
  };

  const handleMessageLongPress = () => {
    // Handle long press (e.g., show message options)
  };

  return (
    <View style={{ flex: 1 }}>
      {item.type === msgType.info ? (
        <View style={{ width: '100%' }}>
          <Text style={{ textAlign: 'center', color: colors.gray400 }}>
            <Text style={{ fontWeight: '600', textTransform: 'capitalize' }}>{item.names}</Text>
            {item.message}
          </Text>
        </View>
      ) : item.type === 'date' ? (
        <FadeIn delay={index * 50} duration={300}>
          <View style={{ width: '100%', marginVertical: spacing.md }}>
            <View
              style={{
                backgroundColor: colors.surface,
                paddingHorizontal: spacing.md,
                paddingVertical: spacing.xs,
                borderRadius: borderRadius.full,
                alignSelf: 'center',
                ...shadows.sm,
              }}>
              <Text
                style={{
                  textAlign: 'center',
                  color: colors.textSecondary,
                  fontSize: 12,
                  fontWeight: '500',
                }}>
                {item.message}
              </Text>
            </View>
          </View>
        </FadeIn>
      ) : item.type === 'event' ? (
        <View
          style={{
            flexDirection: senderId !== uid ? 'row' : 'row-reverse',
            padding: 4,
            // marginLeft: Theme.wp('2%'),
          }}>
          {isImage && (
            <TouchableOpacity
              onPress={() => {
                // @ts-ignore
                navigation.navigate(SCREENS.PERSONAL_INFO, {
                  user: {
                    tag: senderId + 'image',
                    source: senderImage,
                    description: senderName || '',
                    name: senderName,
                    user_id: senderId,
                  },
                });
              }}>
              <FastImage
                source={{ uri: senderImage, priority: 'high' }}
                style={{ height: 40, width: 40, borderRadius: 8, alignSelf: 'flex-end' }}
              />
            </TouchableOpacity>
          )}
          {/* Hide shape icon for event messages since modern event card doesn't need bubble tail */}

          <TouchableOpacity
            activeOpacity={0.7}
            onPress={onEventClick}
            style={{
              left: isImage ? 6 : 0,
              width: '85%', // Fixed width for consistent event tile sizing
            }}>
            {isSenderVisible && senderName && (
              <Text style={{ color: colors.primary, fontSize: 11, fontWeight: '600' }}>{senderName}</Text>
            )}

            {/* Modern Event Card */}
            <View style={styles.modernEventCard}>
              {/* Image Section */}
              <View style={styles.eventImageContainer}>
                <FastImage
                  style={styles.eventImage}
                  source={{
                    uri: item.event.image_url,
                    priority: 'high',
                  }}
                  resizeMode="cover"
                />

                {/* Dark overlay for better text readability */}
                <View style={styles.eventImageOverlay} />

                {/* Event Title Overlay */}
                <View style={styles.eventTitleOverlay}>
                  <Text style={styles.modernEventTitle} numberOfLines={2}>
                    {item.event.name}
                  </Text>
                </View>

                {/* Date Badge */}
                <View style={styles.modernDateBadge}>
                  <Text style={styles.modernDateText}>
                    {`${moment.utc(item.event.start_date).format('MMM DD')} - ${moment.utc(item.event.end_date).format('MMM DD')}`}
                  </Text>
                </View>
              </View>

              {/* Content Section */}
              <View style={styles.eventContentSection}>
                {/* Location */}
                {(item.event.coord_address || item.event.address_name) && (
                  <Text style={styles.modernLocationText} numberOfLines={1}>
                    📍 {item.event.coord_address || item.event.address_name}
                  </Text>
                )}

                {/* Price and Type */}
                <View style={styles.eventBottomRow}>
                  <Text style={styles.modernPriceText}>{item.event.is_paid ? '$' : 'Free'}</Text>
                  <View style={styles.eventTypeBadge}>
                    <Text style={styles.eventTypeText}>Event</Text>
                  </View>
                </View>
              </View>
            </View>

            <Text
              style={{
                fontSize: 10,
                color: colors.textSecondary,
                textAlign: senderId !== uid ? 'left' : 'right',
                marginTop: spacing.xs,
                marginLeft: isImage ? 6 : 0,
              }}>
              {moment(item.timestamp).calendar(null, {
                sameDay: 'HH:mm',
                lastDay: '[Yesterday] HH:mm',
                lastWeek: 'ddd HH:mm',
                sameElse: 'DD/MM/YY HH:mm',
              })}
            </Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ModernChatBubble
          message={item.message}
          timestamp={item.timestamp}
          isOwn={isOwn}
          senderName={senderName}
          showSender={isSenderVisible}
          messageStatus={isOwn ? item.status || item.messageStatus || 'sent' : undefined}
          onPress={handleMessagePress}
          onLongPress={handleMessageLongPress}
          index={index}
        />
      )}
      {item.requestorId && item.status === BUTTON_STATUS_ENUM.SENT && (
        <View
          style={{
            width: '100%',
            marginVertical: 16,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <View style={{ flex: 1, marginLeft: 14, marginRight: 4 }}>
            <TouchableOpacity
              // onPress={disableUserRequest}
              style={{
                borderRadius: 6,
                borderWidth: 1.25,
                borderColor: colors.statusPurple,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 11,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontWeight: '500',
                  color: colors.textPrimary,
                }}>
                Disable
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={{
              flex: 1,
              marginRight: 14,
              marginLeft: 4,
            }}>
            <TouchableOpacity
              // onPress={approveUserRequest}
              style={{
                borderRadius: 6,
                borderWidth: 1.25,
                borderColor: colors.white,
                backgroundColor: colors.statusPurple,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 11,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontWeight: '500',
                  color: colors.white,
                }}>
                Accept
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    // Legacy styles (keeping for compatibility)
    calloutDescription: {
      fontSize: 11,
      color: colors.warning,
      position: 'absolute',
      bottom: 7,
      left: 10,
    },
    imageEvent: {
      width: '100%',
      height: '100%',
      borderRadius: 10,
    },
    dateRangeContainer: {
      zIndex: 1,
      position: 'absolute',
      top: 10,
      left: 10,
      backgroundColor: colors.overlayBackground,
      borderRadius: 10,
      padding: 6,
    },
    eventTitle: {
      fontWeight: 'bold',
      color: colors.white,
      fontSize: 12,
      position: 'absolute',
      bottom: 20,
      left: 10,
    },
    eventCard: {
      height: 100,
      width: 300,
      alignSelf: 'center',
      borderRadius: 10,
    },

    // Modern Event Card Styles
    modernEventCard: {
      backgroundColor: colors.surface,
      borderRadius: borderRadius.lg,
      overflow: 'hidden',
      marginTop: spacing.xs,
      borderWidth: 1,
      borderColor: colors.border + '30',
      width: '100%', // Ensure consistent width across all event tiles
      ...shadows.sm,
    },
    eventImageContainer: {
      height: 120,
      position: 'relative',
    },
    eventImage: {
      width: '100%',
      height: '100%',
    },
    eventImageOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    eventTitleOverlay: {
      position: 'absolute',
      bottom: spacing.sm,
      left: spacing.sm,
      right: spacing.sm,
    },
    modernEventTitle: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.bold,
      color: colors.white,
      textShadowColor: 'rgba(0, 0, 0, 0.5)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    modernDateBadge: {
      position: 'absolute',
      top: spacing.sm,
      left: spacing.sm,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.md,
    },
    modernDateText: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.semibold,
      color: colors.white,
    },
    eventContentSection: {
      padding: spacing.sm,
      gap: spacing.xs,
    },
    modernLocationText: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      fontWeight: typography.fontWeight.medium,
    },
    eventBottomRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: spacing.xs,
    },
    modernPriceText: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.semibold,
      color: colors.primary,
    },
    eventTypeBadge: {
      backgroundColor: colors.primary + '20',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.md,
    },
    eventTypeText: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.medium,
      color: colors.primary,
    },
  });
