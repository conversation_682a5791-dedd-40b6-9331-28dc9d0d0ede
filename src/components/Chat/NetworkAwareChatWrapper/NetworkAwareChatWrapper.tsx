import React, {useState, useEffect, useCallback} from 'react';
import {View, Text, TouchableOpacity, ActivityIndicator} from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import {useTheme} from '~contexts/ThemeContext';
import {useTranslation} from 'react-i18next';
import {spacing, typography, borderRadius} from '~constants/design';
import {haptics} from '~Utils/haptics';
import {WifiOffIcon, RefreshIcon} from '~assets/icons';

interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string;
}

interface NetworkAwareChatWrapperProps {
  children: React.ReactNode;
  onRetry?: () => Promise<void>;
  showOfflineMessage?: boolean;
}

const NetworkAwareChatWrapper: React.FC<NetworkAwareChatWrapperProps> = ({
  children,
  onRetry,
  showOfflineMessage = true,
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const [networkState, setNetworkState] = useState<NetworkState>({
    isConnected: true,
    isInternetReachable: true,
    type: 'unknown',
  });
  const [isRetrying, setIsRetrying] = useState(false);
  const [hasInitialConnection, setHasInitialConnection] = useState(false);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const newNetworkState = {
        isConnected: state.isConnected ?? false,
        isInternetReachable: state.isInternetReachable ?? false,
        type: state.type,
      };

      setNetworkState(newNetworkState);

      // Track if we've had an initial connection
      if (newNetworkState.isConnected && newNetworkState.isInternetReachable) {
        setHasInitialConnection(true);
      }
    });

    return unsubscribe;
  }, []);

  const handleRetry = useCallback(async () => {
    if (!onRetry) return;

    setIsRetrying(true);
    haptics.light();

    try {
      await onRetry();
    } catch (error) {
      console.error('Retry failed:', error);
      haptics.error();
    } finally {
      setIsRetrying(false);
    }
  }, [onRetry]);

  const isOffline = !networkState.isConnected || !networkState.isInternetReachable;

  // Don't show offline message if we haven't had an initial connection yet
  // This prevents showing offline message during app startup
  const shouldShowOfflineMessage = showOfflineMessage && isOffline && hasInitialConnection;

  if (shouldShowOfflineMessage) {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: colors.background,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: spacing.xl,
        }}>
        <View
          style={{
            backgroundColor: colors.surface,
            borderRadius: borderRadius.lg,
            padding: spacing.xl,
            alignItems: 'center',
            maxWidth: 320,
            shadowColor: colors.shadow,
            shadowOffset: {width: 0, height: 2},
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}>
          <WifiOffIcon width={48} height={48} color={colors.textSecondary} style={{marginBottom: spacing.md}} />

          <Text
            style={{
              fontSize: typography.fontSize.lg,
              fontWeight: typography.fontWeight.semibold,
              color: colors.text,
              textAlign: 'center',
              marginBottom: spacing.sm,
            }}>
            {t('chat.no_internet_title') || 'No Internet Connection'}
          </Text>

          <Text
            style={{
              fontSize: typography.fontSize.base,
              color: colors.textSecondary,
              textAlign: 'center',
              lineHeight: 22,
              marginBottom: spacing.lg,
            }}>
            {t('chat.no_internet_message') ||
              'Please check your internet connection and try again. Chat requires an active internet connection.'}
          </Text>

          {onRetry && (
            <TouchableOpacity
              onPress={handleRetry}
              disabled={isRetrying}
              style={{
                backgroundColor: colors.primary,
                paddingHorizontal: spacing.lg,
                paddingVertical: spacing.md,
                borderRadius: borderRadius.md,
                flexDirection: 'row',
                alignItems: 'center',
                opacity: isRetrying ? 0.7 : 1,
              }}>
              {isRetrying ? (
                <ActivityIndicator size="small" color={colors.white} style={{marginRight: spacing.sm}} />
              ) : (
                <RefreshIcon width={16} height={16} color={colors.white} style={{marginRight: spacing.sm}} />
              )}
              <Text
                style={{
                  color: colors.white,
                  fontSize: typography.fontSize.base,
                  fontWeight: typography.fontWeight.medium,
                }}>
                {isRetrying ? t('chat.retrying') || 'Retrying...' : t('chat.try_again') || 'Try Again'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }

  return <>{children}</>;
};

export default NetworkAwareChatWrapper;
