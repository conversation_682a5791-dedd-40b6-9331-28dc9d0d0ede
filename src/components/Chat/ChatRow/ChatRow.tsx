import React, {memo, useCallback, useMemo} from 'react';
import {Text, TouchableOpacity, View, StyleSheet} from 'react-native';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import {useNavigation} from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import {formatDateTime} from '~Utils/Time';
import {getLastMessage} from '~Utils/chat';
import {LogoIcon} from '~assets/icons';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import FirebaseChatsService from '~services/FirebaseChats';
import {CHAT_TYPE_ENUM, ChatType, ChatTypeWithKey} from '~types/chat';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import getStyles from './styles';
import {useTheme} from '~contexts/ThemeContext';
import {AnimatedPressable} from '~components/MicroInteractions/MicroInteractions';
import {spacing, borderRadius, shadows} from '~constants/design';
import {haptics} from '~Utils/haptics';
import {debugTouchEvent, validateTouchableComponent} from '~Utils/debugTouchIssues';

interface ChatRowProps {
  chat: ChatTypeWithKey;
}

const ChatRow: React.FC<ChatRowProps> = memo(({chat}) => {
  const {colors} = useTheme();
  const styles = getStyles(colors);
  const uid = auth().currentUser?.uid;

  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();

  const {data: userAccount} = useGetUserAccount(uid);

  const currentUserName = userAccount?.last_name
    ? `${userAccount.first_name} ${userAccount.last_name}`
    : userAccount?.first_name;

  const unreadMessagesCount = useMemo(() => {
    return (
      chat?.history?.filter((message: any) => message?.readUserIds && !message.readUserIds.includes(uid))?.length || 0
    );
  }, [chat?.history, uid]);

  const lastMessage = useMemo(() => getLastMessage(chat?.history || []), [chat?.history]);
  const interlocutorId = chat?.userIds?.find(userId => userId !== uid);
  const interlocutorMessage = chat?.history?.find(message => message.sender_id === interlocutorId);
  const interlocutorName = interlocutorMessage?.sender || chat?.users?.find(user => user !== lastMessage?.sender);
  const interlocutorImage = interlocutorMessage?.sender_image;

  const topText = chat?.eventName || interlocutorName;
  const middleText = chat?.eventId ? lastMessage?.sender : null;
  const chatStatus = chat?.status || 'closed';
  const isOpenIssue = chatStatus == 'open';
  const bottomText = lastMessage?.message;
  const image = chat?.eventImage || interlocutorImage;
  const userId = chat?.userIds?.find(id => id !== uid);
  const userIndex = chat?.userIds?.findIndex(user => user.toLowerCase() !== (uid || '').toLowerCase()) || 0;
  const userName = chat?.users?.[userIndex];

  const userImage = useMemo(() => {
    if (!image) {
      return (
        <View style={[styles.image, {backgroundColor: colors.black, alignItems: 'center', justifyContent: 'center'}]}>
          <LogoIcon color={colors.white} />
        </View>
      );
    }
    return <FastImage source={{uri: image, priority: 'high'}} style={styles.image} />;
  }, [image]);

  const selectChat = useCallback(async () => {
    try {
      debugTouchEvent('ChatRow', 'selectChat called', {chatKey: chat?.key, userName});

      // Haptic feedback for chat selection
      haptics.light();

      const chatRef = firestore().collection('chats').doc(chat?.key || '');
      const doc = await chatRef.get();
      if (doc.exists) {
        const chatData = doc.data() as ChatType;
        const updatedMessages =
          chatData?.history?.map((message: any) => {
            if (!message.readUserIds?.includes(uid)) {
              return {...message, readUserIds: [...(message.readUserIds || []), uid]};
            }
            return message;
          }) || [];

        await chatRef.update({history: updatedMessages});
      }
      setIsTabBarDisabled(true);
      if (chat?.type == CHAT_TYPE_ENUM.ORGANISATION) {
        debugTouchEvent('ChatRow', 'Navigating to organisation chat');
        navigation.navigate(SCREENS.USER_CHAT, {chatId: chat?.key, image: image, userName: userName});
      } else if (chat?.type == 'contact-pyxi') {
        debugTouchEvent('ChatRow', 'Navigating to support chat');
        navigation.navigate(SCREENS.USER_CHAT, {chatId: chat?.key, image: image, userName: userName});
      } else {
        debugTouchEvent('ChatRow', 'Creating private chat');
        const chatId = await FirebaseChatsService.createPrivateChat({
          user_id1: uid!,
          user_id2: userId!,
          user_name1: topText || '',
          user_name2: userName || '',
          user_image: image || '',
        });
        navigation.navigate(SCREENS.USER_CHAT, {chatId: chatId, image: image, userName: userName});
      }
      debugTouchEvent('ChatRow', 'selectChat completed successfully');
    } catch (error) {
      console.error('ChatRow selectChat error:', error);
      debugTouchEvent('ChatRow', 'selectChat failed', error);
      haptics.error();
    }
  }, [chat.key, uid, userId, userName, topText, image, setIsTabBarDisabled, navigation]);

  // Validate touch configuration in development
  React.useEffect(() => {
    if (__DEV__) {
      validateTouchableComponent('ChatRow', {
        onPress: selectChat,
        style: styles.container,
        disabled: false,
      });
    }
  }, [selectChat]);

  return (
    <TouchableOpacity
      onPress={selectChat}
      style={[styles.container /* {opacity: isOpenIssue ? 1 : 0.5} */]}
      activeOpacity={0.7}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={`Chat with ${userName}`}
      accessibilityHint="Tap to open chat conversation"
      // Ensure touch events are properly handled
      delayPressIn={0}
      delayPressOut={0}
      // Add debug handlers
      onPressIn={() => debugTouchEvent('ChatRow', 'onPressIn')}
      onPressOut={() => debugTouchEvent('ChatRow', 'onPressOut')}
      // Prevent touch event conflicts
    >
      <View style={styles.rightContentContainer} pointerEvents="none">
        {userImage}
      </View>
      <View style={styles.middleContentContainer} pointerEvents="none">
        <Text style={styles.topText} numberOfLines={1}>
          {userName} {chat.type == 'contact-pyxi' ? ' (Support Chat)' : ''}
        </Text>
        {!!middleText && (
          <Text style={styles.middleText} numberOfLines={1}>
            {middleText}
          </Text>
        )}
        <Text style={styles.bottomText} numberOfLines={1}>
          {bottomText}
        </Text>
      </View>
      <View style={styles.rightContentContainer} pointerEvents="none">
        <View style={styles.flexWrapper}>
          <Text style={styles.dateText}>{formatDateTime(lastMessage?.timestamp || '')}</Text>
        </View>
        {!isOpenIssue && false && <Text style={styles.closedText}>{'Closed'}</Text>}
        <View style={[styles.flexWrapper, {marginTop: 5}]}>
          {unreadMessagesCount ? (
            <View style={styles.quantityUnreadMessagesContainer}>
              <Text style={styles.quantityUnreadMessagesText}>{unreadMessagesCount}</Text>
            </View>
          ) : null}
        </View>
        <View style={styles.flexWrapper} />
      </View>
    </TouchableOpacity>
  );
});

export default ChatRow;
