import React, {useEffect, useState} from 'react';
import {View, Text, Animated} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {useTranslation} from 'react-i18next';
import {spacing, borderRadius, typography} from '~constants/design';
// Using text icons for simplicity and reliability

interface OfflineMessageNotificationProps {
  visible: boolean;
  successCount: number;
  failureCount: number;
  onHide: () => void;
  duration?: number;
}

const OfflineMessageNotification: React.FC<OfflineMessageNotificationProps> = ({
  visible,
  successCount,
  failureCount,
  onHide,
  duration = 4000,
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(-100));

  useEffect(() => {
    if (visible) {
      // Slide down and fade in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto hide after duration
      const timer = setTimeout(() => {
        hideNotification();
      }, duration);

      return () => clearTimeout(timer);
    } else {
      hideNotification();
    }
  }, [visible, duration]);

  const hideNotification = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onHide();
    });
  };

  if (!visible && fadeAnim._value === 0) {
    return null;
  }

  const getMessage = () => {
    if (successCount > 0 && failureCount === 0) {
      return successCount === 1
        ? t('chat.offline_message_sent') || '1 offline message sent successfully!'
        : t('chat.offline_messages_sent', {count: successCount}) ||
            `${successCount} offline messages sent successfully!`;
    } else if (successCount > 0 && failureCount > 0) {
      return (
        t('chat.offline_messages_partial', {success: successCount, failed: failureCount}) ||
        `${successCount} messages sent, ${failureCount} failed`
      );
    } else {
      return (
        t('chat.offline_messages_failed', {count: failureCount}) || `${failureCount} offline messages failed to send`
      );
    }
  };

  const getIcon = () => {
    if (successCount > 0 && failureCount === 0) {
      return <Text style={{fontSize: 18, color: colors.white, fontWeight: 'bold'}}>✅</Text>;
    } else if (successCount > 0 && failureCount > 0) {
      return <Text style={{fontSize: 18, color: colors.white, fontWeight: 'bold'}}>⚠️</Text>;
    } else {
      return <Text style={{fontSize: 18, color: colors.white, fontWeight: 'bold'}}>❌</Text>;
    }
  };

  const getBackgroundColor = () => {
    if (successCount > 0 && failureCount === 0) {
      return colors.success || colors.primary;
    } else if (successCount > 0 && failureCount > 0) {
      return colors.warning || colors.orange;
    } else {
      return colors.error || colors.red;
    }
  };

  return (
    <Animated.View
      style={{
        position: 'absolute',
        top: 0, // Position at very top
        left: 0,
        right: 0,
        zIndex: 99999,
        opacity: fadeAnim,
        transform: [{translateY: slideAnim}],
        paddingTop: 50, // Add padding for status bar
        paddingHorizontal: spacing.md,
      }}>
      <View
        style={{
          backgroundColor: getBackgroundColor(),
          borderRadius: borderRadius.lg,
          paddingHorizontal: spacing.lg,
          paddingVertical: spacing.md,
          flexDirection: 'row',
          alignItems: 'center',
          shadowColor: colors.black,
          shadowOffset: {width: 0, height: 2},
          shadowOpacity: 0.25,
          shadowRadius: 4,
          elevation: 5,
        }}>
        {getIcon()}
        <Text
          style={{
            marginLeft: spacing.sm,
            fontSize: typography.fontSize.sm,
            fontWeight: typography.fontWeight.medium,
            color: colors.white,
            flex: 1,
          }}>
          {getMessage()}
        </Text>
      </View>
    </Animated.View>
  );
};

export default OfflineMessageNotification;
