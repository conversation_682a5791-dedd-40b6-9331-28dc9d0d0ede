import React, {memo, useCallback, useMemo, useState, useEffect} from 'react';
import {Text, Pressable, View} from 'react-native';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import {useNavigation} from '@react-navigation/native';
import NetInfo from '@react-native-community/netinfo';

import {formatDateTime} from '~Utils/Time';
import {getLastMessage, getLastMessageWithUnsent} from '~Utils/chat';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import FirebaseChatsService from '~services/FirebaseChats';
import {CHAT_TYPE_ENUM, ChatType, ChatTypeWithKey} from '~types/chat';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useTheme} from '~contexts/ThemeContext';
import CircularImage from '~components/CircularImage/CircularImage';
import {spacing, borderRadius, shadows, typography} from '~constants/design';
import {haptics} from '~Utils/haptics';

interface ModernChatRowProps {
  chat: ChatTypeWithKey;
  index?: number;
}

const ModernChatRow: React.FC<ModernChatRowProps> = memo(({chat, index = 0}) => {
  const {colors} = useTheme();
  const uid = auth().currentUser?.uid;
  const navigation = useNavigation<NavigationProps>();
  const {setIsTabBarDisabled} = useTabBar();
  const {data: userAccount} = useGetUserAccount(uid);

  // Network state tracking
  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected ?? false);
    });

    return unsubscribe;
  }, []);

  // All hooks must be called before any early returns
  const unreadMessagesCount = useMemo(() => {
    if (!chat?.history) return 0;
    return (
      chat.history.filter((message: any) => message?.readUserIds && !message.readUserIds.includes(uid))?.length || 0
    );
  }, [chat?.history, uid]);

  // State for the last message including unsent messages
  const [lastMessageWithUnsent, setLastMessageWithUnsent] = useState<any>(null);
  const [isLoadingLastMessage, setIsLoadingLastMessage] = useState(false);

  const lastMessage = useMemo(() => {
    if (!chat?.history) return null;
    return getLastMessage(chat.history);
  }, [chat?.history]);

  // Load last message including unsent messages
  useEffect(() => {
    const loadLastMessageWithUnsent = async () => {
      if (!chat?.key) return;

      setIsLoadingLastMessage(true);
      try {
        const lastMsg = await getLastMessageWithUnsent(chat.history || [], chat.key);
        setLastMessageWithUnsent(lastMsg);
      } catch (error) {
        console.error('Error loading last message with unsent:', error);
        setLastMessageWithUnsent(lastMessage);
      } finally {
        setIsLoadingLastMessage(false);
      }
    };

    loadLastMessageWithUnsent();
  }, [chat?.history, chat?.key, lastMessage]);

  // Modern profile picture logic with better fallbacks
  const profileImageData = useMemo(() => {
    // For event chats, use event image
    if (chat?.eventImage) {
      return {
        uri: chat.eventImage,
        fallbackText: chat?.eventName?.charAt(0)?.toUpperCase() || 'E',
      };
    }

    // For user chats, find the other user's image
    const otherUserId = chat?.userIds?.find(userId => userId !== uid);
    const otherUserMessage = chat?.history?.find(message => message.sender_id === otherUserId);
    const otherUserImage = otherUserMessage?.sender_image;
    const otherUserName = otherUserMessage?.sender || chat?.users?.find(user => user !== lastMessage?.sender);

    return {
      uri: otherUserImage,
      fallbackText: otherUserName?.charAt(0)?.toUpperCase() || 'U',
    };
  }, [chat?.eventImage, chat?.eventName, chat?.userIds, chat?.history, chat?.users, uid, lastMessage?.sender]);

  const renderUserImage = useCallback(() => {
    if (profileImageData.uri) {
      return (
        <CircularImage
          source={{uri: profileImageData.uri}}
          size={56}
          showShadow={true}
          style={{
            marginRight: 0, // We handle margin in parent
          }}
        />
      );
    }

    // Fallback to text avatar
    return (
      <View
        style={{
          width: 56,
          height: 56,
          borderRadius: 28,
          backgroundColor: colors.primary,
          alignItems: 'center',
          justifyContent: 'center',
          elevation: 3,
          shadowColor: '#000',
          shadowOffset: {width: 0, height: 2},
          shadowOpacity: 0.1,
          shadowRadius: 4,
        }}>
        <Text
          style={{
            color: colors.white,
            fontSize: 20,
            fontWeight: '600',
          }}>
          {profileImageData.fallbackText}
        </Text>
      </View>
    );
  }, [profileImageData, colors]);

  const selectChat = useCallback(async () => {
    if (!chat) {
      return;
    }

    const interlocutorId = chat?.userIds?.find(userId => userId !== uid);
    const interlocutorMessage = chat?.history?.find(message => message.sender_id === interlocutorId);
    const interlocutorName = interlocutorMessage?.sender || chat?.users?.find(user => user !== lastMessage?.sender);
    const interlocutorImage = interlocutorMessage?.sender_image;

    const topText = chat?.eventName || interlocutorName;
    const image = chat?.eventImage || interlocutorImage;
    const userId = chat?.userIds?.find(id => id !== uid);
    const userIndex = chat?.userIds?.findIndex(user => user.toLowerCase() !== (uid || '').toLowerCase()) || 0;
    const userName = chat?.users?.[userIndex];

    setIsTabBarDisabled(true);

    console.log('🔥 ModernChatRow: Checking chat type', {
      chatType: chat?.type,
      isOrganisation: chat?.type === CHAT_TYPE_ENUM.ORGANISATION,
      isContactPyxi: chat?.type === 'contact-pyxi',
      isConnected,
      ORGANISATION_ENUM: CHAT_TYPE_ENUM.ORGANISATION,
    });

    // Handle existing chats (organisation, support, or existing private chats)
    if (chat?.type === CHAT_TYPE_ENUM.ORGANISATION || chat?.type === 'contact-pyxi' || chat?.key) {
      console.log('🔥 ModernChatRow: Navigating to existing chat', {
        chatId: chat?.key,
        type: chat?.type,
        isOffline: !isConnected,
      });

      // For existing chats, we can navigate even when offline
      navigation.navigate(SCREENS.USER_CHAT, {chatId: chat?.key, image: image, userName: userName});

      // Only try to mark messages as read when online
      if (isConnected) {
        try {
          const chatRef = firestore().collection('chats').doc(chat?.key || '');
          const doc = await chatRef.get();
          if (doc.exists) {
            const chatData = doc.data() as ChatType;
            const updatedMessages = chatData.history.map((message: any) => {
              if (!message.readUserIds?.includes(uid)) {
                return {...message, readUserIds: [...(message.readUserIds || []), uid]};
              }
              return message;
            });

            await chatRef.update({history: updatedMessages});
          }
        } catch (error) {
          console.error('❌ ModernChatRow: Failed to mark messages as read (offline)', error);
        }
      }
    } else {
      // For new private chats, we need to be online
      if (!isConnected) {
        console.log('🔥 ModernChatRow: Cannot create new chat while offline');
        // Could show a toast or alert here
        return;
      }

      console.log('🔥 ModernChatRow: Creating private chat for user chat', {
        chatType: chat?.type,
        user_id1: uid,
        user_id2: userId,
        userName,
      });

      try {
        // Get current user's name from userAccount
        const currentUserName = userAccount
          ? `${userAccount.first_name || ''} ${userAccount.last_name || ''}`.trim()
          : '';

        const chatId = await FirebaseChatsService.createPrivateChat({
          user_id1: uid!,
          user_id2: userId!,
          user_name1: currentUserName || 'Unknown User',
          user_name2: userName || '',
          user_image: userAccount?.photo || '',
        });
        console.log('🔥 ModernChatRow: Private chat created successfully', {chatId});
        navigation.navigate(SCREENS.USER_CHAT, {chatId: chatId, image: image, userName: userName});
      } catch (createChatError) {
        console.error('❌ ModernChatRow: Failed to create private chat', createChatError);
      }
    }
  }, [chat, uid, lastMessage, setIsTabBarDisabled, navigation, userAccount, isConnected]);

  // Safety check for chat prop - now after all hooks
  if (!chat) {
    return null;
  }

  // These variables are now handled in profileImageData useMemo above

  // Use lastMessageWithUnsent for display, fallback to lastMessage
  const displayMessage = lastMessageWithUnsent || lastMessage;
  const middleText = chat?.eventId ? displayMessage?.sender : null;
  const bottomText = displayMessage?.message;
  const userIndex = chat?.userIds?.findIndex(user => user.toLowerCase() !== (uid || '').toLowerCase()) || 0;
  const userName = chat?.users?.[userIndex];

  const getLastMessageTime = () => {
    if (!displayMessage?.timestamp) {
      return '';
    }
    return formatDateTime(displayMessage.timestamp);
  };

  const isUnread = unreadMessagesCount > 0;

  const handlePress = useCallback(() => {
    try {
      haptics.light();
    } catch (error) {
      console.error('Haptic feedback failed:', error);
    }
    selectChat();
  }, [selectChat]);

  return (
    // Temporarily remove FadeIn animation to fix touch issues
    // <FadeIn delay={index * 50} duration={300}>
    <Pressable
      style={({pressed}) => [
        {
          backgroundColor: colors.surface,
          borderRadius: borderRadius.xl,
          marginHorizontal: spacing.md,
          marginVertical: spacing.xs,
          padding: spacing.md,
          flexDirection: 'row',
          alignItems: 'center',
          borderWidth: isUnread ? 1 : 0,
          borderColor: isUnread ? colors.primary : 'transparent',
          opacity: pressed ? 0.7 : 1,
          // Ensure proper touch target
          minHeight: 80,
        },
        // Separate shadow style to avoid React Native shadowOffset warning
        shadows.sm,
      ]}
      onPress={handlePress}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={`Chat with ${userName || 'Unknown User'}`}
      accessibilityHint="Tap to open chat conversation"
      // Ensure proper touch handling
      delayLongPress={500}
      // Enhanced touch handling for React Native upgrade compatibility
      hitSlop={{top: 5, bottom: 5, left: 5, right: 5}}
      android_ripple={{
        color: colors.primary + '20',
        borderless: false,
      }}>
      {/* User Avatar */}
      <View style={{marginRight: spacing.md}}>{renderUserImage()}</View>

      {/* Chat Content */}
      <View style={{flex: 1, marginRight: spacing.sm}}>
        <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: spacing.xs}}>
          <Text
            style={{
              fontSize: typography.fontSize.base,
              fontWeight: isUnread ? typography.fontWeight.semibold : typography.fontWeight.medium,
              color: colors.textPrimary,
              flex: 1,
            }}
            numberOfLines={1}>
            {userName || 'Unknown User'} {chat?.type === 'contact-pyxi' ? '(Support)' : ''}
          </Text>
          <Text
            style={{
              fontSize: typography.fontSize.xs,
              color: colors.textSecondary,
              fontWeight: typography.fontWeight.medium,
            }}>
            {getLastMessageTime()}
          </Text>
        </View>

        {middleText && (
          <Text
            style={{
              fontSize: typography.fontSize.sm,
              color: colors.primary,
              fontWeight: typography.fontWeight.medium,
              marginBottom: spacing.xs,
            }}
            numberOfLines={1}>
            {middleText}
          </Text>
        )}

        <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
          <View style={{flexDirection: 'row', alignItems: 'center', flex: 1}}>
            <Text
              style={{
                fontSize: typography.fontSize.sm,
                color: colors.textSecondary,
                flex: 1,
                fontWeight: isUnread ? typography.fontWeight.medium : typography.fontWeight.normal,
              }}
              numberOfLines={1}>
              {bottomText}
            </Text>
            {displayMessage?.isPending && (
              <Text
                style={{
                  fontSize: typography.fontSize.xs,
                  color: colors.warning || colors.orange,
                  fontWeight: typography.fontWeight.medium,
                  marginLeft: spacing.xs,
                }}>
                ⏳
              </Text>
            )}
          </View>

          {isUnread && (
            <View
              style={{
                backgroundColor: colors.primary,
                borderRadius: borderRadius.full,
                minWidth: 20,
                height: 20,
                paddingHorizontal: spacing.xs,
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: spacing.sm,
              }}>
              <Text
                style={{
                  fontSize: typography.fontSize.xs,
                  color: colors.white,
                  fontWeight: typography.fontWeight.semibold,
                }}>
                {unreadMessagesCount > 99 ? '99+' : unreadMessagesCount}
              </Text>
            </View>
          )}
        </View>
      </View>
    </Pressable>
    // </FadeIn>
  );
});

export default ModernChatRow;
