import React, {useMemo} from 'react';
import {View, Text, TouchableOpacity, Image, Platform} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, shadows, typography} from '~constants/design';
import {haptics} from '~Utils/haptics';
import {ChevronIcon, LogoIcon, LogoIconBar, SettingsIcon} from '~assets/icons';

interface ModernChatHeaderProps {
  userName: string;
  userImage?: string;
  isOnline?: boolean;
  lastSeen?: string;
  issueNumber?: string;
  onBackPress?: () => void;
  onUserPress?: () => void;
  onCallPress?: () => void;
  onVideoPress?: () => void;
  onMorePress?: () => void;
  showCallButtons?: boolean;
  showMoreButton?: boolean;
}

const ModernChatHeader: React.FC<ModernChatHeaderProps> = ({
  userName,
  userImage,
  isOnline = false,
  lastSeen,
  issueNumber,
  onBackPress,
  onUserPress,
  onCallPress,
  onVideoPress,
  onMorePress,
  showCallButtons = false,
  showMoreButton = false,
}) => {
  const {colors} = useTheme();
  const {top} = useSafeAreaInsets();

  const handleBackPress = () => {
    if (onBackPress) {
      haptics.medium();
      onBackPress();
    }
  };

  const handleUserPress = () => {
    if (onUserPress) {
      haptics.medium();
      onUserPress();
    }
  };

  const renderUserAvatar = useMemo(() => {
    if (!userImage) {
      return (
        <View
          style={{
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <LogoIcon color={colors.primaryDark} />
        </View>
      );
    }

    return (
      <View style={{position: 'relative'}}>
        <Image
          source={{uri: userImage}}
          style={{
            width: 40,
            height: 40,
            borderRadius: borderRadius.full,
            ...shadows.sm,
          }}
          resizeMode="cover"
        />
        {isOnline && (
          <View
            style={{
              position: 'absolute',
              bottom: 0,
              right: 0,
              width: 12,
              height: 12,
              borderRadius: borderRadius.full,
              backgroundColor: colors.success,
              borderWidth: 2,
              borderColor: colors.surface,
            }}
          />
        )}
      </View>
    );
  }, [userImage, isOnline, colors]);

  const getStatusText = () => {
    if (isOnline) {
      return 'Online';
    }
    if (lastSeen) {
      return `Last seen ${lastSeen}`;
    }
    return '';
  };

  const renderActionButton = (icon: React.ReactNode, onPress?: () => void, disabled = false) => (
    <TouchableOpacity
      style={{
        width: 40,
        height: 40,
        borderRadius: borderRadius.full,
        backgroundColor: colors.gray100,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: spacing.sm,
        opacity: disabled ? 0.5 : 1,
      }}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}>
      {icon}
    </TouchableOpacity>
  );

  return (
    <View
      style={{
        backgroundColor: colors.surface,
        paddingTop: Platform.OS === 'ios' ? top : top + spacing.md,
        paddingBottom: spacing.md,
        paddingHorizontal: spacing.lg,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
        ...shadows.sm,
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {/* Left Section - Back Button (if provided) & User Info */}
        <View style={{flexDirection: 'row', alignItems: 'center', flex: 1}}>
          {onBackPress && (
            <TouchableOpacity
              style={{
                width: 40,
                height: 40,
                borderRadius: borderRadius.full,
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: spacing.sm,
              }}
              onPress={handleBackPress}
              activeOpacity={0.7}>
              <ChevronIcon color={colors.primary} />
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              flex: 1,
              paddingLeft: onBackPress ? 0 : spacing.lg, // Add padding when no back button
            }}
            onPress={handleUserPress}
            disabled={!onUserPress}
            activeOpacity={0.7}>
            {renderUserAvatar}

            <View style={{marginLeft: spacing.md, flex: 1}}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Text
                  style={{
                    fontSize: typography.fontSize.lg,
                    fontWeight: typography.fontWeight.semibold,
                    color: colors.textPrimary,
                    flex: 1,
                  }}
                  numberOfLines={1}>
                  {userName}
                </Text>
                {issueNumber && (
                  <Text
                    style={{
                      fontSize: typography.fontSize.sm,
                      color: colors.textSecondary,
                      fontWeight: typography.fontWeight.medium,
                      marginLeft: spacing.xs,
                    }}>
                    #{issueNumber}
                  </Text>
                )}
              </View>

              {getStatusText() && (
                <Text
                  style={{
                    fontSize: typography.fontSize.sm,
                    color: isOnline ? colors.success : colors.textSecondary,
                    fontWeight: typography.fontWeight.medium,
                    marginTop: 2,
                  }}>
                  {getStatusText()}
                </Text>
              )}
            </View>
          </TouchableOpacity>
        </View>

        {/* Right Section - Action Buttons */}
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {showCallButtons && (
            <>
              {renderActionButton(<Text style={{fontSize: 16}}>📞</Text>, onCallPress, !onCallPress)}
              {renderActionButton(<Text style={{fontSize: 16}}>📹</Text>, onVideoPress, !onVideoPress)}
            </>
          )}

          {showMoreButton && renderActionButton(<SettingsIcon color={colors.textPrimary} />, onMorePress, !onMorePress)}
        </View>
      </View>
    </View>
  );
};

export default ModernChatHeader;
