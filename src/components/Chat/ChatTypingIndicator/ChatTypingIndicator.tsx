import React, {useEffect} from 'react';
import {View, Text} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, typography} from '~constants/design';

interface ChatTypingIndicatorProps {
  isVisible: boolean;
  userName?: string;
  style?: any;
}

const ChatTypingIndicator: React.FC<ChatTypingIndicatorProps> = ({isVisible, userName = 'Someone', style}) => {
  const {colors} = useTheme();

  // Animation values for the dots
  const dot1 = useSharedValue(0);
  const dot2 = useSharedValue(0);
  const dot3 = useSharedValue(0);
  const containerOpacity = useSharedValue(0);

  useEffect(() => {
    if (isVisible) {
      // Fade in the container
      containerOpacity.value = withTiming(1, {duration: 300});

      // Start the dot animations with staggered delays
      const animateDot = (dotValue: Animated.SharedValue<number>, delay: number) => {
        dotValue.value = withDelay(
          delay,
          withRepeat(
            withTiming(1, {
              duration: 600,
              easing: Easing.inOut(Easing.ease),
            }),
            -1,
            true,
          ),
        );
      };

      animateDot(dot1, 0);
      animateDot(dot2, 200);
      animateDot(dot3, 400);
    } else {
      // Fade out
      containerOpacity.value = withTiming(0, {duration: 200});
      dot1.value = 0;
      dot2.value = 0;
      dot3.value = 0;
    }
  }, [isVisible, dot1, dot2, dot3, containerOpacity]);

  const containerStyle = useAnimatedStyle(() => ({
    opacity: containerOpacity.value,
    transform: [
      {
        translateY: interpolate(containerOpacity.value, [0, 1], [10, 0]),
      },
    ],
  }));

  const useDotStyle = (dotValue: Animated.SharedValue<number>) =>
    useAnimatedStyle(() => ({
      opacity: interpolate(dotValue.value, [0, 1], [0.3, 1]),
      transform: [
        {
          scale: interpolate(dotValue.value, [0, 1], [0.8, 1.2]),
        },
        {
          translateY: interpolate(dotValue.value, [0, 1], [0, -3]),
        },
      ],
    }));

  const dot1Style = useDotStyle(dot1);
  const dot2Style = useDotStyle(dot2);
  const dot3Style = useDotStyle(dot3);

  if (!isVisible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        containerStyle,
        {
          flexDirection: 'row',
          justifyContent: 'flex-start',
          marginVertical: spacing.xs,
          paddingHorizontal: spacing.md,
        },
        style,
      ]}>
      <View
        style={[
          {
            backgroundColor: colors.surface,
            borderRadius: borderRadius.xl,
            borderBottomLeftRadius: borderRadius.sm,
            borderWidth: 1,
            borderColor: colors.border,
            paddingHorizontal: spacing.md,
            paddingVertical: spacing.sm,
            marginRight: spacing.xl,
          },
          // Separate shadow style to avoid React Native warning
          {
            shadowColor: colors.black,
            shadowOffset: {width: 0, height: 1},
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
          },
        ]}>
        {/* Typing text */}
        <Text
          style={{
            fontSize: typography.fontSize.xs,
            color: colors.textSecondary,
            marginBottom: spacing.xs,
            fontStyle: 'italic',
          }}>
          {userName} is typing...
        </Text>

        {/* Animated dots */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: spacing.xs,
          }}>
          <Animated.View
            style={[
              dot1Style,
              {
                width: 6,
                height: 6,
                borderRadius: 3,
                backgroundColor: colors.primary,
              },
            ]}
          />
          <Animated.View
            style={[
              dot2Style,
              {
                width: 6,
                height: 6,
                borderRadius: 3,
                backgroundColor: colors.primary,
              },
            ]}
          />
          <Animated.View
            style={[
              dot3Style,
              {
                width: 6,
                height: 6,
                borderRadius: 3,
                backgroundColor: colors.primary,
              },
            ]}
          />
        </View>
      </View>
    </Animated.View>
  );
};

export default ChatTypingIndicator;
