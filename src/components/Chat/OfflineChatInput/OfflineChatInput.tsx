import React, {useState, useRef, useEffect} from 'react';
import {Pressable, TextInput, View, Text, ActivityIndicator} from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import {SendArrow} from '~assets/icons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Animated, {
  Extrapolation,
  SharedValue,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import {useTheme} from '~contexts/ThemeContext';
import {useTranslation} from 'react-i18next';
import {haptics} from '~Utils/haptics';
import {spacing, borderRadius, shadows} from '~constants/design';
import OfflineChatStorage, {UnsentMessage} from '~services/OfflineChatStorage/OfflineChatStorage';
import MessageRetryService from '~services/MessageRetryService';
import {MESSAGE_STATUS, MessageStatus} from '~constants/messageStatus';

const AnimatedTextInput = Animated.createAnimatedComponent(TextInput);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

interface OfflineChatInputProps {
  submit: (text: string, tempMessageId?: string) => Promise<string | void>;
  h: SharedValue<number>;
  progress: SharedValue<number>;
  chatId: string;
  userId: string;
  userName: string;
  userImage?: string;
  placeholder?: string;
  maxLength?: number;
  showCharacterCount?: boolean;
  disabled?: boolean;
  onUnsentMessageAdded?: (message: UnsentMessage) => void;
  onMessageStatusChange?: (tempId: string, status: MessageStatus, realMessageId?: string) => void;
  onOfflineMessagesSent?: (successCount: number, failureCount: number) => void;
}

export default function OfflineChatInput({
  submit,
  h,
  progress,
  chatId,
  userId,
  userName,
  userImage,
  placeholder = 'Type a message...',
  maxLength = 1000,
  showCharacterCount = false,
  disabled = false,
  onUnsentMessageAdded,
  onMessageStatusChange,
  onOfflineMessagesSent,
}: OfflineChatInputProps) {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const [textInput, setTextInput] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [inputHeight, setInputHeight] = useState(44);
  const [isOnline, setIsOnline] = useState(true);
  const {bottom} = useSafeAreaInsets();
  const textInputRef = useRef<TextInput>(null);

  // Network state monitoring
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const isNowOnline = !!(state.isConnected && state.isInternetReachable);
      setIsOnline(isNowOnline);
    });
    return unsubscribe;
  }, []);

  // Initialize retry service
  useEffect(() => {
    const retryService = MessageRetryService.getInstance();
    retryService.initialize();

    return () => {
      retryService.cleanup();
    };
  }, []);

  // Constants for height calculation
  const MIN_HEIGHT = 44;
  const MAX_HEIGHT = 44 + 22 * 4; // 5 lines total
  const LINE_HEIGHT = 22;

  // Animation values
  const sendButtonScale = useSharedValue(0.8);
  const sendButtonOpacity = useSharedValue(0.6);
  const inputBorderWidth = useSharedValue(1);
  const inputShadowOpacity = useSharedValue(0);
  const animatedInputHeight = useSharedValue(MIN_HEIGHT);

  // Update send button animation based on text input
  useEffect(() => {
    const hasText = textInput.trim().length > 0;
    sendButtonScale.value = withSpring(hasText ? 1 : 0.8, {damping: 15});
    sendButtonOpacity.value = withTiming(hasText ? 1 : 0.6, {duration: 200});
  }, [textInput, sendButtonScale, sendButtonOpacity]);

  // Update input styling based on focus
  useEffect(() => {
    inputBorderWidth.value = withTiming(isFocused ? 2 : 1, {duration: 200});
    inputShadowOpacity.value = withTiming(isFocused ? 0.1 : 0, {duration: 200});
  }, [isFocused, inputBorderWidth, inputShadowOpacity]);

  // Handle content size change for dynamic height
  const handleContentSizeChange = (event: any) => {
    const {height} = event.nativeEvent.contentSize;
    const newHeight = Math.max(MIN_HEIGHT, Math.min(height + 20, MAX_HEIGHT));
    setInputHeight(newHeight);
    animatedInputHeight.value = withSpring(newHeight, {damping: 15, stiffness: 150});
  };

  const containerStyle = useAnimatedStyle(
    () => ({
      flexDirection: 'row',
      alignItems: 'flex-end',
      paddingHorizontal: spacing.lg,
      paddingTop: spacing.md,
      backgroundColor: colors.surface,
      paddingBottom: interpolate(progress.value, [0, 1], [bottom || spacing.lg, spacing.md], Extrapolation.CLAMP),
      width: '100%',
      transform: [{translateY: -h.value}],
      borderTopWidth: 1,
      borderTopColor: colors.border,
    }),
    [colors],
  );

  const inputContainerStyle = useAnimatedStyle(() => ({
    flex: 1,
    height: animatedInputHeight.value,
    minHeight: MIN_HEIGHT,
    maxHeight: MAX_HEIGHT,
    borderRadius: borderRadius['2xl'],
    backgroundColor: colors.inputBackground,
    borderWidth: inputBorderWidth.value,
    borderColor: isFocused ? colors.primary : colors.inputBorder,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    marginRight: spacing.sm,
    justifyContent: 'center',
  }));

  const inputShadowStyle = {
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
    elevation: 2,
  };

  const inputShadowAnimatedStyle = useAnimatedStyle(() => ({
    shadowOpacity: inputShadowOpacity.value,
    elevation: inputShadowOpacity.value * 5,
  }));

  const sendButtonStyle = useAnimatedStyle(() => {
    const backgroundColor = textInput.trim() ? colors.primary : colors.gray300;

    return {
      width: 44,
      height: 44,
      borderRadius: borderRadius.full,
      backgroundColor,
      justifyContent: 'center',
      alignItems: 'center',
      transform: [{scale: sendButtonScale.value}],
      opacity: sendButtonOpacity.value,
    };
  });

  const handleSubmit = async () => {
    const text = textInput.trim();
    if (!text || disabled) {
      return;
    }

    // Don't block if already submitting - allow rapid message sending
    if (isSubmitting && isOnline) {
      return; // Only block online submissions to prevent duplicate sends
    }

    const tempMessageId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    haptics.medium();

    // Clear input immediately for better UX
    setTextInput('');
    setInputHeight(MIN_HEIGHT);
    animatedInputHeight.value = withSpring(MIN_HEIGHT, {damping: 15, stiffness: 150});

    if (isOnline) {
      // Online: Try to send immediately
      setIsSubmitting(true);
      try {
        onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.SENDING);

        const realMessageId = await submit(text, tempMessageId);
        onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.SENT, realMessageId as string);
      } catch (error) {
        console.error('Failed to send message:', error);
        onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.FAILED);
        haptics.error();
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Offline: Store message locally with pending status (non-blocking)
      try {
        await OfflineChatStorage.storeUnsentMessage({
          chatId,
          text,
          timestamp: new Date().toISOString(),
          tempMessageId,
          userId,
          userName,
          userImage,
        });

        onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.PENDING);

        // Get the stored message for callback
        const storedMessages = await OfflineChatStorage.getUnsentMessages();
        const storedMessage = storedMessages.find(m => m.tempMessageId === tempMessageId);
        if (storedMessage && onUnsentMessageAdded) {
          onUnsentMessageAdded(storedMessage);
        }

        console.log('✅ Offline message stored successfully');
      } catch (error) {
        console.error('Failed to store offline message:', error);
        onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.FAILED);
        haptics.error();
      }
      // Note: No setIsSubmitting for offline - allows rapid message sending
    }
  };

  const handleTextChange = (text: string) => {
    if (text.length <= maxLength) {
      setTextInput(text);
      if (text.trim() === '') {
        setInputHeight(MIN_HEIGHT);
        animatedInputHeight.value = withSpring(MIN_HEIGHT, {damping: 15, stiffness: 150});
      }
    } else {
      haptics.warning();
    }
  };

  const isOverLimit = textInput.length > maxLength * 0.9;
  const characterCount = textInput.length;

  return (
    <Animated.View style={[containerStyle, shadows.sm]}>
      {/* Offline indicator */}
      {!isOnline && (
        <View
          style={{
            position: 'absolute',
            top: -30,
            left: spacing.lg,
            right: spacing.lg,
            backgroundColor: colors.warning,
            paddingHorizontal: spacing.md,
            paddingVertical: spacing.xs,
            borderRadius: borderRadius.md,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <ActivityIndicator size="small" color={colors.white} style={{marginRight: spacing.xs}} />
          <Text
            style={{
              color: colors.white,
              fontSize: 12,
              fontWeight: '500',
            }}>
            {t('chat.offline_mode') || 'Offline - Messages will be sent when connected'}
          </Text>
        </View>
      )}

      <Animated.View style={[inputContainerStyle, inputShadowStyle, inputShadowAnimatedStyle]}>
        <AnimatedTextInput
          ref={textInputRef}
          value={textInput}
          onChangeText={handleTextChange}
          onContentSizeChange={handleContentSizeChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          placeholderTextColor={colors.placeholderText}
          style={{
            flex: 1,
            fontSize: 16,
            lineHeight: LINE_HEIGHT,
            color: colors.inputText,
            textAlignVertical: 'top',
            paddingVertical: spacing.sm,
            paddingTop: spacing.sm,
            minHeight: LINE_HEIGHT,
            maxHeight: LINE_HEIGHT * 5,
          }}
          multiline={true}
          maxLength={maxLength}
          editable={!disabled && !(isSubmitting && isOnline)}
          returnKeyType="send"
          onSubmitEditing={handleSubmit}
          scrollEnabled={inputHeight >= MAX_HEIGHT}
          enablesReturnKeyAutomatically={true}
        />

        {showCharacterCount && (
          <View style={{position: 'absolute', right: spacing.sm, bottom: 2}}>
            <Text
              style={{
                fontSize: 12,
                color: isOverLimit ? colors.error : colors.textSecondary,
                fontWeight: isOverLimit ? '600' : '400',
              }}>
              {characterCount}/{maxLength}
            </Text>
          </View>
        )}
      </Animated.View>

      <AnimatedPressable
        style={[sendButtonStyle, shadows.sm]}
        onPress={handleSubmit}
        disabled={!textInput.trim() || (isSubmitting && isOnline) || disabled}>
        {isSubmitting && isOnline ? (
          <ActivityIndicator size="small" color={colors.white} />
        ) : (
          <SendArrow isActive={!!textInput.trim() && !(isSubmitting && isOnline)} />
        )}
      </AnimatedPressable>
    </Animated.View>
  );
}
