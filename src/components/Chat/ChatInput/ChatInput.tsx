import React, {useState, useRef, useEffect} from 'react';
import {Pressable, TextInput, View, Text} from 'react-native';
import {SendArrow} from '~assets/icons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Animated, {
  Extrapolation,
  SharedValue,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import {useTheme} from '~contexts/ThemeContext';
import {haptics} from '~Utils/haptics';
import {spacing, borderRadius, shadows} from '~constants/design';
import {useMessageStatus} from '~hooks/chat/useMessageStatus';
import {MESSAGE_STATUS, MessageStatus} from '~constants/messageStatus';

const AnimatedTextInput = Animated.createAnimatedComponent(TextInput);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

interface ChatInputProps {
  submit: (text: string, tempMessageId?: string) => Promise<string | void>;
  h: SharedValue<number>;
  progress: SharedValue<number>;
  placeholder?: string;
  maxLength?: number;
  showCharacterCount?: boolean;
  disabled?: boolean;
  onMessageStatusChange?: (tempId: string, status: MessageStatus, realMessageId?: string) => void;
}

export default function ChatInput({
  submit,
  h,
  progress,
  placeholder = 'Type a message...',
  maxLength = 1000,
  showCharacterCount = false,
  disabled = false,
  onMessageStatusChange,
}: ChatInputProps) {
  const {colors} = useTheme();
  const [textInput, setTextInput] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [inputHeight, setInputHeight] = useState(44); // Start with single line height
  const {bottom} = useSafeAreaInsets();
  const textInputRef = useRef<TextInput>(null);
  const {addPendingMessage, updateMessageStatus} = useMessageStatus();

  // Debug logging for component mount and state
  useEffect(() => {
    console.log('🟡 ChatInput: Component mounted/rendered');
    return () => {
      console.log('🟡 ChatInput: Component unmounted');
    };
  }, []);

  useEffect(() => {
    console.log('🔵 ChatInput: Component state changed:', {
      textInput: textInput.length > 0 ? `"${textInput}"` : 'empty',
      textInputLength: textInput.length,
      isSubmitting,
      disabled,
      isFocused,
      hasSubmitFunction: typeof submit === 'function',
    });
  }, [textInput, isSubmitting, disabled, isFocused, submit]);

  // Constants for height calculation
  const MIN_HEIGHT = 44; // Single line height
  const MAX_HEIGHT = 44 + 22 * 4; // 5 lines total (1 base + 4 additional lines)
  const LINE_HEIGHT = 22;

  // Animation values
  const sendButtonScale = useSharedValue(0.8);
  const sendButtonOpacity = useSharedValue(0.6);
  const inputBorderWidth = useSharedValue(1);
  const inputShadowOpacity = useSharedValue(0);
  const animatedInputHeight = useSharedValue(MIN_HEIGHT);

  // Update send button animation based on text input
  useEffect(() => {
    const hasText = textInput.trim().length > 0;
    sendButtonScale.value = withSpring(hasText ? 1 : 0.8, {damping: 15});
    sendButtonOpacity.value = withTiming(hasText ? 1 : 0.6, {duration: 200});
  }, [textInput, sendButtonScale, sendButtonOpacity]);

  // Update input styling based on focus
  useEffect(() => {
    inputBorderWidth.value = withTiming(isFocused ? 2 : 1, {duration: 200});
    inputShadowOpacity.value = withTiming(isFocused ? 0.1 : 0, {duration: 200});
  }, [isFocused, inputBorderWidth, inputShadowOpacity]);

  // Handle content size change for dynamic height
  const handleContentSizeChange = (event: any) => {
    const {height} = event.nativeEvent.contentSize;
    const newHeight = Math.max(MIN_HEIGHT, Math.min(height + 20, MAX_HEIGHT)); // Add padding
    setInputHeight(newHeight);
    animatedInputHeight.value = withSpring(newHeight, {damping: 15, stiffness: 150});
  };

  const containerStyle = useAnimatedStyle(
    () => ({
      flexDirection: 'row',
      alignItems: 'flex-end',
      paddingHorizontal: spacing.lg,
      paddingTop: spacing.md,
      backgroundColor: colors.surface,
      paddingBottom: interpolate(progress.value, [0, 1], [bottom || spacing.lg, spacing.md], Extrapolation.CLAMP),
      width: '100%',
      transform: [{translateY: -h.value}],
      borderTopWidth: 1,
      borderTopColor: colors.border,
      // Remove shadow spread to avoid React Native warning - will apply separately
    }),
    [colors],
  );

  // Separate shadow style for container
  const containerShadowStyle = shadows.sm;

  const inputContainerStyle = useAnimatedStyle(() => ({
    flex: 1,
    height: animatedInputHeight.value,
    minHeight: MIN_HEIGHT,
    maxHeight: MAX_HEIGHT,
    borderRadius: borderRadius['2xl'],
    backgroundColor: colors.inputBackground,
    borderWidth: inputBorderWidth.value,
    borderColor: isFocused ? colors.primary : colors.inputBorder,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    marginRight: spacing.sm,
    // Move shadow properties to a separate style object to avoid React Native warning
    justifyContent: 'center',
  }));

  // Separate shadow style to avoid animated style conflicts
  const inputShadowStyle = {
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
    elevation: 2,
  };

  const inputShadowAnimatedStyle = useAnimatedStyle(() => ({
    shadowOpacity: inputShadowOpacity.value,
    elevation: inputShadowOpacity.value * 5,
  }));

  const sendButtonStyle = useAnimatedStyle(() => {
    const backgroundColor = textInput.trim() ? colors.primary : colors.gray300;

    return {
      width: 44,
      height: 44,
      borderRadius: borderRadius.full,
      backgroundColor,
      justifyContent: 'center',
      alignItems: 'center',
      transform: [{scale: sendButtonScale.value}],
      opacity: sendButtonOpacity.value,
    };
  });

  // Separate shadow style for send button
  const sendButtonShadowStyle = shadows.sm;

  const characterCountStyle = useAnimatedStyle(() => ({
    opacity: withTiming(showCharacterCount && textInput.length > maxLength * 0.8 ? 1 : 0, {duration: 200}),
  }));

  const handleSubmit = async () => {
    console.log('🔥 ChatInput: handleSubmit function called!');
    const text = textInput.trim();
    console.log('🔥 ChatInput: handleSubmit validation:', {
      text,
      textLength: text.length,
      isSubmitting,
      disabled,
      shouldReturn: !text || isSubmitting || disabled,
    });

    if (!text || isSubmitting || disabled) {
      console.log('🔥 ChatInput: handleSubmit returning early');
      return;
    }

    console.log('🔥 ChatInput: handleSubmit proceeding with send');

    // Generate a temporary message ID
    const tempMessageId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    console.log('🔥 ChatInput: Generated tempMessageId:', tempMessageId);

    // Haptic feedback for send action
    haptics.medium();
    console.log('🔥 ChatInput: Haptic feedback done');

    setIsSubmitting(true);
    console.log('🔥 ChatInput: Set isSubmitting to true');

    // Add pending message with "sending" status
    try {
      console.log('🔥 ChatInput: About to call addPendingMessage');
      // Skip addPendingMessage for now due to MESSAGE_STATUS import issues
      // addPendingMessage(tempMessageId, text);
      console.log('🔥 ChatInput: addPendingMessage skipped');
    } catch (error) {
      console.error('❌ ChatInput: addPendingMessage failed:', error);
    }

    try {
      console.log('🔥 ChatInput: About to call onMessageStatusChange');
      onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.SENDING);
      console.log('🔥 ChatInput: onMessageStatusChange completed');
    } catch (error) {
      console.error('❌ ChatInput: onMessageStatusChange failed:', error);
    }

    // Animate send button
    try {
      console.log('🔥 ChatInput: About to animate send button');
      sendButtonScale.value = withSpring(0.9, {damping: 15}, () => {
        sendButtonScale.value = withSpring(1, {damping: 15});
      });
      console.log('🔥 ChatInput: Send button animation started');
    } catch (error) {
      console.error('❌ ChatInput: Send button animation failed:', error);
    }

    try {
      console.log('🚀 ChatInput: About to call submit function');
      const realMessageId = await submit(text, tempMessageId);
      console.log('✅ ChatInput: Message sent successfully, ID:', realMessageId);

      // Update status to "sent"
      // Skip updateMessageStatus for now due to MESSAGE_STATUS import issues
      // updateMessageStatus(tempMessageId, 'sent' as any, realMessageId as string);
      onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.SENT, realMessageId as string);

      // Only clear the text input after successful submission
      setTextInput('');
      // Reset input height to single line
      setInputHeight(MIN_HEIGHT);
      animatedInputHeight.value = withSpring(MIN_HEIGHT, {damping: 15, stiffness: 150});
      console.log('✅ ChatInput: Text cleared and input reset');
    } catch (error) {
      console.error('❌ ChatInput: Failed to send message:', error);

      // Update status to "failed"
      // Skip updateMessageStatus for now due to MESSAGE_STATUS import issues
      // updateMessageStatus(tempMessageId, 'failed' as any);
      onMessageStatusChange?.(tempMessageId, MESSAGE_STATUS.FAILED);

      // Keep the text in the input on error
      haptics.error();
    } finally {
      console.log('🔄 ChatInput: Setting isSubmitting to false');
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: any) => {
    if (e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    haptics.light();
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleTextChange = (text: string) => {
    if (text.length <= maxLength) {
      setTextInput(text);
      // Reset height if text is completely cleared
      if (text.trim() === '') {
        setInputHeight(MIN_HEIGHT);
        animatedInputHeight.value = withSpring(MIN_HEIGHT, {damping: 15, stiffness: 150});
      }
    } else {
      haptics.warning();
    }
  };

  const isOverLimit = textInput.length > maxLength * 0.9;
  const characterCount = textInput.length;

  return (
    <Animated.View style={[containerStyle, containerShadowStyle]}>
      <Animated.View style={[inputContainerStyle, inputShadowStyle, inputShadowAnimatedStyle]}>
        <AnimatedTextInput
          ref={textInputRef}
          value={textInput}
          onChangeText={handleTextChange}
          onContentSizeChange={handleContentSizeChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          placeholderTextColor={colors.placeholderText}
          style={{
            flex: 1,
            fontSize: 16,
            lineHeight: LINE_HEIGHT,
            color: colors.inputText,
            textAlignVertical: 'top',
            paddingVertical: spacing.sm,
            paddingTop: spacing.sm,
            minHeight: LINE_HEIGHT,
            maxHeight: LINE_HEIGHT * 5, // 5 lines max
          }}
          multiline={true}
          maxLength={maxLength}
          editable={!disabled && !isSubmitting}
          returnKeyType="send"
          onSubmitEditing={handleSubmit}
          scrollEnabled={inputHeight >= MAX_HEIGHT}
          enablesReturnKeyAutomatically={true}
        />

        {showCharacterCount && (
          <Animated.View style={[characterCountStyle, {position: 'absolute', right: spacing.sm, bottom: 2}]}>
            <Text
              style={{
                fontSize: 12,
                color: isOverLimit ? colors.error : colors.textSecondary,
                fontWeight: isOverLimit ? '600' : '400',
              }}>
              {characterCount}/{maxLength}
            </Text>
          </Animated.View>
        )}
      </Animated.View>

      <AnimatedPressable
        style={[sendButtonStyle, sendButtonShadowStyle]}
        onPress={() => {
          console.log('🟢 ChatInput: Send button pressed!', {
            textInput,
            trimmed: textInput.trim(),
            isSubmitting,
            disabled,
            buttonDisabled: !textInput.trim() || isSubmitting || disabled,
          });
          try {
            console.log('🟢 ChatInput: About to call handleSubmit');
            handleSubmit();
            console.log('🟢 ChatInput: handleSubmit called successfully');
          } catch (error) {
            console.error('❌ ChatInput: Error calling handleSubmit:', error);
          }
        }}
        disabled={!textInput.trim() || isSubmitting || disabled}>
        <SendArrow isActive={!!textInput.trim() && !isSubmitting} />
      </AnimatedPressable>
    </Animated.View>
  );
}
