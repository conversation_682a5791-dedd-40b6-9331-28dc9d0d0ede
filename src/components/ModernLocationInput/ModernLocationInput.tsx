import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Keyboard,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius} from '~constants/design';
import Svg, {Path} from 'react-native-svg';
import {useDebounce} from '~components/SearchBarWithAutocomplete/hooks/useDebounce';
import {GOOGLE_API_KEY} from '@env';
import Animated, {FadeIn, FadeOut} from 'react-native-reanimated';
import axios from 'axios';

const GOOGLE_PLACES_API_BASE_URL = 'https://maps.googleapis.com/maps/api/place';

// LocationIcon component
const LocationIcon = ({color = '#666', size = 20}: {color?: string; size?: number}) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.364 3.63604C20.0518 5.32387 21 7.61305 21 10Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

interface LocationPrediction {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
}

interface ModernLocationInputProps {
  label: string;
  value: string;
  onLocationSelect: (location: {address: string; latitude: number; longitude: number; placeId: string}) => void;
  errorText?: string;
  placeholder?: string;
  required?: boolean;
  onFocus?: () => void;
}

const ModernLocationInput: React.FC<ModernLocationInputProps> = ({
  label,
  value,
  onLocationSelect,
  errorText,
  placeholder,
  required = false,
  onFocus,
}) => {
  const {t} = useTranslation();
  const {colors} = useTheme();
  const [searchText, setSearchText] = useState(value);
  const [predictions, setPredictions] = useState<LocationPrediction[]>([]);
  const [showPredictions, setShowPredictions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const textInputRef = useRef<TextInput>(null);

  // Update search text when value prop changes
  useEffect(() => {
    setSearchText(value);
  }, [value]);

  const searchLocations = async (text: string) => {
    console.log('searchLocations called with:', text);

    if (text.trim().length < 3) {
      console.log('Text too short, clearing predictions');
      setPredictions([]);
      setShowPredictions(false);
      return;
    }

    console.log('Starting location search...');
    setIsLoading(true);

    try {
      // Use the same pattern as working implementations
      const apiKey = GOOGLE_API_KEY;
      const apiUrl = `${GOOGLE_PLACES_API_BASE_URL}/autocomplete/json?key=${apiKey}&input=${text}`;
      console.log('Making request to:', apiUrl);
      console.log('API Key exists:', !!apiKey);
      console.log('GOOGLE_API_KEY:', GOOGLE_API_KEY);
      console.log('Using API Key:', apiKey);

      const result = await axios.request({
        method: 'post',
        url: apiUrl,
      });

      console.log('API Response status:', result.status);
      console.log('API Response data:', result.data);

      if (result && result.data) {
        if (result.data.predictions && result.data.predictions.length > 0) {
          console.log('Found predictions:', result.data.predictions.length);
          setPredictions(result.data.predictions.slice(0, 5));
          setShowPredictions(true);
        } else {
          console.log('No predictions found. Status:', result.data.status, 'Error:', result.data.error_message);
          setPredictions([]);
          setShowPredictions(false);
        }
      } else {
        console.log('Invalid response structure');
        setPredictions([]);
        setShowPredictions(false);
      }
    } catch (error: any) {
      console.error('Error fetching location predictions:', error);
      console.error('Error details:', error.response?.data || error.message);
      setPredictions([]);
      setShowPredictions(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Debounce search to avoid too many API calls
  useDebounce(
    () => {
      console.log('Debounced search triggered for:', searchText);
      searchLocations(searchText);
    },
    500,
    [searchText],
  );

  const handleLocationSelect = async (prediction: LocationPrediction) => {
    setIsLoading(true);
    try {
      // Use the same pattern as working implementations
      const apiKey = GOOGLE_API_KEY;
      const apiUrl = `${GOOGLE_PLACES_API_BASE_URL}/details/json?key=${apiKey}&place_id=${prediction.place_id}`;

      console.log('Making place details request to:', apiUrl); // Debug log

      const result = await axios.request({
        method: 'post',
        url: apiUrl,
      });

      console.log('Place details API response:', result.data); // Debug log

      if (result && result.data && result.data.result) {
        const {
          data: {
            result: {
              geometry: {location},
              formatted_address,
            },
          },
        } = result;
        const {lat, lng} = location;

        setSearchText(formatted_address);
        setShowPredictions(false);
        setPredictions([]); // Clear predictions to prevent showing them again
        Keyboard.dismiss();
        textInputRef.current?.blur();

        onLocationSelect({
          address: formatted_address,
          latitude: lat,
          longitude: lng,
          placeId: prediction.place_id,
        });
      } else {
        console.log('Place details API error:', result.data?.status, result.data?.error_message);
      }
    } catch (error) {
      console.error('Error fetching location details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTextChange = (text: string) => {
    console.log('Text changed to:', text);
    setSearchText(text);
    if (text.length === 0) {
      setShowPredictions(false);
      setPredictions([]);
    }
    // If user starts typing after selecting a location, clear the value
    // This allows them to search for a new location
    if (value && text !== value) {
      // User is changing the selected location, allow new search
      setShowPredictions(false);
      setPredictions([]);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    // Call parent onFocus to handle scrolling
    onFocus?.();
    // Don't show predictions if a location is already selected (value prop is set)
    // Only show predictions if we have search text, predictions exist, and no location is selected
    if (searchText.length >= 3 && predictions.length > 0 && !value) {
      setShowPredictions(true);
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    // Delay hiding predictions to allow for selection
    setTimeout(() => {
      setShowPredictions(false);
    }, 200);
  };

  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.md,
    },
    label: {
      fontSize: typography.fontSize.sm,
      fontWeight: '600',
      color: colors.textPrimary,
      marginBottom: spacing.xs,
    },
    required: {
      color: colors.error,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: errorText ? colors.error : isFocused ? colors.primary : colors.border,
      borderRadius: borderRadius.md,
      backgroundColor: colors.background,
      paddingHorizontal: spacing.md,
      minHeight: 48,
    },
    input: {
      flex: 1,
      fontSize: typography.fontSize.base,
      color: colors.textPrimary,
      paddingVertical: spacing.sm,
      marginLeft: spacing.sm,
    },
    errorText: {
      fontSize: typography.fontSize.xs,
      color: colors.error,
      marginTop: spacing.xs,
    },
    predictionsContainer: {
      backgroundColor: colors.background,
      borderWidth: 1,
      borderColor: colors.border,
      borderTopWidth: 0,
      borderBottomLeftRadius: borderRadius.md,
      borderBottomRightRadius: borderRadius.md,
      maxHeight: 150, // Reduced height to fit better with keyboard
      elevation: 3,
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.1,
      shadowRadius: 4,
      zIndex: 1000, // Ensure it appears above other elements
      position: 'relative',
    },
    predictionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderBottomWidth: 1,
    },
    predictionText: {
      flex: 1,
      marginLeft: spacing.sm,
    },
    mainText: {
      fontSize: typography.fontSize.sm,
      fontWeight: '500',
    },
    secondaryText: {
      fontSize: typography.fontSize.xs,
      marginTop: 2,
    },
    loadingContainer: {
      padding: spacing.md,
      alignItems: 'center',
    },
    emptyContainer: {
      padding: spacing.md,
      alignItems: 'center',
    },
    emptyText: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {label}
        {required && <Text style={styles.required}> *</Text>}
      </Text>

      <View style={styles.inputContainer}>
        <LocationIcon color={colors.textSecondary} size={20} />
        <TextInput
          ref={textInputRef}
          style={styles.input}
          value={searchText}
          onChangeText={handleTextChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder || t('onboarding.location_search_placeholder')}
          placeholderTextColor={colors.textSecondary}
          autoCapitalize="words"
          autoCorrect={false}
          returnKeyType="search"
        />
        {isLoading && <ActivityIndicator size="small" color={colors.primary} />}
      </View>

      {errorText && <Text style={styles.errorText}>{errorText}</Text>}

      {showPredictions && predictions.length > 0 && (
        <Animated.View entering={FadeIn} exiting={FadeOut} style={styles.predictionsContainer}>
          <ScrollView
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            nestedScrollEnabled={true}>
            {predictions.map(item => (
              <TouchableOpacity
                key={item.place_id}
                style={[styles.predictionItem, {borderBottomColor: colors.border}]}
                onPress={() => handleLocationSelect(item)}>
                <LocationIcon color={colors.textSecondary} size={16} />
                <View style={styles.predictionText}>
                  <Text style={[styles.mainText, {color: colors.textPrimary}]}>
                    {item.structured_formatting.main_text}
                  </Text>
                  <Text style={[styles.secondaryText, {color: colors.textSecondary}]}>
                    {item.structured_formatting.secondary_text}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </Animated.View>
      )}

      {showPredictions && predictions.length === 0 && !isLoading && searchText.length >= 3 && (
        <Animated.View entering={FadeIn} exiting={FadeOut} style={styles.predictionsContainer}>
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>{t('onboarding.location_search_hint')}</Text>
          </View>
        </Animated.View>
      )}
    </View>
  );
};

export default ModernLocationInput;
