import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
  ScrollView,
  Image,
  ImageBackground,
} from 'react-native';
import Animated, {FadeIn, FadeOut} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import {useTheme} from '~contexts/ThemeContext';
import {Event} from '~types/api/event';
import {spacing, typography, borderRadius} from '~constants';
import ModernSpinner from '~components/ModernSpinner';

const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

interface PayPerMatchModalProps {
  visible: boolean;
  event: Event;
  onClose: () => void;
  isLoading?: boolean;
}

const PayPerMatchModal: React.FC<PayPerMatchModalProps> = ({visible, event, onClose, isLoading = false}) => {
  const {colors, isDarkMode} = useTheme();
  const {top, bottom} = useSafeAreaInsets();

  const styles = createStyles(colors);

  const formatPrice = (price: number, currency: string) => {
    return `${currency}${price.toFixed(2)}`;
  };

  return (
    <Modal visible={visible} transparent animationType="none" statusBarTranslucent onRequestClose={onClose}>
      <Animated.View style={styles.overlay} entering={FadeIn.duration(300)} exiting={FadeOut.duration(300)}>
        <ImageBackground
          defaultSource={require('~assets/images/sad_face.png')}
          style={styles.modalContainer}
          resizeMode={'stretch'}
          source={require('~assets/images/sad_face.png')}>
          {/* Action Buttons */}
          <Text style={styles.optionTitle}>Sorry!</Text>
          <Text style={styles.desc}>
            We don’t sell tickets for this event! Please follow the link in the description or contact the organiser.
          </Text>
          <TouchableOpacity style={styles.confirmButton} disabled={isLoading} onPress={onClose} activeOpacity={0.8}>
            {isLoading ? (
              <ModernSpinner size={20} color={colors.white} />
            ) : (
              <Text style={styles.confirmButtonText}>Go Back to event</Text>
            )}
          </TouchableOpacity>
        </ImageBackground>
      </Animated.View>
    </Modal>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    desc: {
      fontSize: 16,
      color: '#FFFFFF',
      textAlign: 'center',
      marginHorizontal: 30,
    },
    optionTitle: {
      fontSize: 28,
      fontWeight: 'bold',
      color: '#FFFFFF',
      marginBottom: spacing.sm,
    },
    modalContainer: {
      backgroundColor: colors.background,
      width: SCREEN_WIDTH,
      height: SCREEN_HEIGHT,
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    closeButton: {
      padding: spacing.xs,
    },
    headerTitle: {
      flex: 1,
      textAlign: 'center',
      fontSize: typography.fontSize.lg,
      fontWeight: '600',
      color: colors.textPrimary,
    },
    headerSpacer: {
      width: 32,
    },
    content: {
      flex: 1,
      paddingHorizontal: spacing.lg,
    },
    eventInfoCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: borderRadius.md,
      padding: spacing.md,
      marginTop: spacing.md,
    },
    eventName: {
      fontSize: typography.fontSize.lg,
      fontWeight: '600',
      color: colors.textPrimary,
      marginBottom: spacing.xs,
    },
    eventMetaRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.xs,
    },
    eventMeta: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginLeft: spacing.xs,
    },
    payPerMatchCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: borderRadius.md,
      padding: spacing.lg,
      marginTop: spacing.md,
    },
    payPerMatchHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    payPerMatchTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: '600',
      color: colors.textPrimary,
      marginLeft: spacing.sm,
    },
    sadFaceIcon: {
      width: 24,
      height: 24,
      marginRight: spacing.sm,
    },
    payPerMatchDescription: {
      fontSize: typography.fontSize.base,
      color: colors.textSecondary,
      lineHeight: 22,
      marginBottom: spacing.lg,
    },
    priceContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: spacing.md,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderColor: colors.border,
      marginBottom: spacing.lg,
    },
    priceLabel: {
      fontSize: typography.fontSize.base,
      color: colors.textPrimary,
      fontWeight: '500',
    },
    priceValue: {
      fontSize: typography.fontSize.xl,
      fontWeight: '700',
      color: colors.primary,
    },
    featuresContainer: {
      marginTop: spacing.sm,
    },
    featuresTitle: {
      fontSize: typography.fontSize.base,
      fontWeight: '600',
      color: colors.textPrimary,
      marginBottom: spacing.sm,
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    featureText: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
      marginLeft: spacing.sm,
    },
    termsContainer: {
      marginTop: spacing.md,
      marginBottom: spacing.lg,
    },
    termsText: {
      fontSize: typography.fontSize.xs,
      color: colors.textSecondary,
      lineHeight: 18,
      textAlign: 'center',
    },
    actionContainer: {
      flexDirection: 'row',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      gap: spacing.md,
    },
    cancelButton: {
      flex: 1,
      paddingVertical: spacing.md,
      borderRadius: borderRadius.md,
      borderWidth: 1,
      borderColor: colors.border,
      alignItems: 'center',
      justifyContent: 'center',
    },
    cancelButtonText: {
      fontSize: typography.fontSize.base,
      fontWeight: '600',
      color: colors.textSecondary,
    },
    confirmButton: {
      backgroundColor: '#FF9500',
      paddingVertical: spacing.md,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 20,
      width: '70%',
    },
    confirmButtonText: {
      fontSize: typography.fontSize.base,
      fontWeight: '600',
      color: colors.white,
    },
    disabledButton: {
      opacity: 0.6,
    },
  });

export default PayPerMatchModal;
