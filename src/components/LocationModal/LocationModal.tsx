import _ from 'lodash';
import {useEffect, useLayoutEffect, useMemo, useRef, useState} from 'react';
import {Alert, Dimensions, Keyboard, PermissionsAndroid, Platform, Text, TouchableOpacity, View} from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import MapView, {Mark<PERSON>, PROVIDER_GOOGLE, Region} from 'react-native-maps';
import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import BottomSheet from '~/components/BottomSheet';

import {useMapsContext} from '~/providers/maps/zustand';
import {useTheme} from '~contexts/ThemeContext';
import {ModalHeight} from '~/types/modal';
import {reverseGeocode} from '~Utils/location';
import {ChevronSmallIcon, CloseIcon, LocationCircleIcon, FlagMarker, NavigationIcon} from '~assets/icons';
import {CustomModal} from '../CustomModal';
import BottomSheetContent from './BottomSheetContent/BottomSheetContent';
import useGetCurrentPosition from './hooks/useGetCurrentPosition';
import useGoogleMapsAnimation from './hooks/useGoogleMapsAnimation';
import ModernButton from '~components/ModernButton';

export const getAddressComponent = (addressComponents: any[], type: string) => {
  const component = addressComponents.find(component => component.types.includes(type));
  return component ? component.long_name : '';
};

export interface Location {
  latitude: number;
  longitude: number;
  address: string;
}

interface IProps {
  isVisible: boolean;
  close: () => void;
  onPressButton?: () => void;
  onLocationChange?: (location: Location | null) => void;
  setSelectedTab?: any;
  withButton?: boolean;
  coords?: any;
  isFromCreation?: boolean;
  withAccurateAddress?: boolean;
  isFromHomeScreen?: boolean;
}

interface ILocationItem {
  latitude: number;
  longitude: number;
  id: string;
}

const LocationModal = ({
  isVisible,
  close,
  onPressButton,
  withButton = false,
  coords,
  onLocationChange,
  setSelectedTab,
  isFromCreation = false,
  withAccurateAddress,
  isFromHomeScreen = false,
}: IProps) => {
  const {colors} = useTheme();
  const [eventLocations, setEventLocations] = useState<ILocationItem[]>([]);
  const mapRef = useRef<MapView>(null);
  const {getCurrentPosition, handleGoToSettings, handleUpdatePositionAfterSettings} = useGetCurrentPosition();
  const {focusOnUser, setIsCurrentGeolocationActive, isCurrentGeolocationActive, focusOnLocation} =
    useGoogleMapsAnimation(mapRef);
  const {userLocation: selfPosition, setCurrentPositionState} = useMapsContext();
  const {height} = useSafeAreaFrame();
  const [centalMarker, setCentralMarker] = useState<Region | null>(null);
  const [isSelectLocationVisible, setIsSelectLocationVisible] = useState(true);

  const handleRegionChange = _.throttle((region: Region) => {
    setCentralMarker(region);
  }, 200);

  const {top} = useSafeAreaInsets();

  const [position, setPosition] = useState({
    latitude: coords?.latitude || 51.5072178,
    longitude: coords?.longitude || -0.1275862,
    latitudeDelta: 1,
    longitudeDelta: 1,
  });

  useEffect(() => {
    setCentralMarker(position);
  }, [position]);

  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      position => {
        setPosition({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          latitudeDelta: 1,
          longitudeDelta: 1,
        });
      },
      error => {
        console.log('Error getting location:', error.code, error.message);
      },
      {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
    );
  };

  useLayoutEffect(() => {
    getCurrentLocation();
  }, []);

  const handleCurrentPositionButtonPressFromHomeScreen = () => {
    getCurrentPosition(true);
    close();
  };

  const handleSaveLocation = async () => {
    try {
      const location = await reverseGeocode({
        withAccurateAdrs: withAccurateAddress,
        coords: {latitude: centalMarker?.latitude, longitude: centalMarker?.longitude},
      });
      if (location && centalMarker) {
        if (onLocationChange) {
          onLocationChange({
            address: location,
            longitude: centalMarker?.longitude,
            latitude: centalMarker?.latitude,
          });
        } else {
          setCurrentPositionState({
            address: location,
            longitude: centalMarker?.longitude,
            latitude: centalMarker?.latitude,
          });
        }
        setSelectedTab();
      }
    } catch (e) {
      console.log(e);
    } finally {
      close();
    }
  };

  const currentPositionButton = useMemo(
    () => (
      <View style={{width: Dimensions.get('window').width}}>
        <ModernButton
          title="Save"
          variant="primary"
          size="sm"
          onPress={handleSaveLocation}
          style={{
            position: 'absolute',
            left: 20,
            bottom: 20,
            borderRadius: 50,
            zIndex: 1001,
            width: 80,
          }}
        />
        <TouchableOpacity
          style={{
            position: 'absolute',
            right: 20,
            bottom: 20,
            padding: 10,
            borderRadius: 50,
            backgroundColor: colors.white,
            zIndex: 1001,
          }}
          onPress={() => {
            // Always try to center map on user's current location, don't close modal
            if (selfPosition?.coords) {
              return focusOnUser();
            }
            // If no cached position, get current location and center map
            return onChangeLocation();
          }}>
          <NavigationIcon isActive={isCurrentGeolocationActive} />
        </TouchableOpacity>
      </View>
    ),
    [isCurrentGeolocationActive, selfPosition?.coords, focusOnUser, handleSaveLocation, onChangeLocation],
  );

  const onChangeLocation = () => {
    Geolocation.getCurrentPosition(
      position => {
        const newLocation = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        };

        // Focus on the user's current location
        focusOnLocation(newLocation);

        // Update the position state
        setPosition({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          latitudeDelta: 1,
          longitudeDelta: 1,
        });

        // Update the central marker to show current location
        setCentralMarker({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          latitudeDelta: 1,
          longitudeDelta: 1,
        });

        // Update the current position state in context
        setCurrentPositionState(newLocation);
      },
      error => {
        console.log('Error getting location:', error.code, error.message);
        // Show user-friendly error message
        Alert.alert('Location Error', 'Unable to get your current location. Please check your location permissions.');
      },
      {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
    );
  };
  const handlePositionChange = (position: 'open' | 'closed' | 'moving') => {
    if (position == 'closed') {
      Keyboard.dismiss();
    }
  };
  return (
    <CustomModal
      modalIsVisible={isVisible}
      onCloseModal={close}
      height={ModalHeight.MAX}
      backgroundColor={colors.white}>
      <TouchableOpacity
        onPress={close}
        style={{
          position: 'absolute',
          top: top + 5,
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
        }}>
        <CloseIcon />
      </TouchableOpacity>

      <TouchableOpacity
        onPress={onChangeLocation}
        style={{
          position: 'absolute',
          top: top + 15,
          right: 15,
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
        }}>
        <LocationCircleIcon />
      </TouchableOpacity>

      <View style={{position: 'absolute', top: 0, width: '100%', height: height}}>
        <MapView
          initialRegion={position}
          ref={mapRef}
          style={{flex: 1, minHeight: 330, maxHeight: height * 0.85, width: '100%'}}
          provider={PROVIDER_GOOGLE}
          showsMyLocationButton={true}
          showsUserLocation={true}
          followsUserLocation={false}
          showsCompass={false}
          toolbarEnabled={false}
          showsBuildings={false}
          showsIndoors={false}
          onPress={e => {
            const {latitude, longitude} = e.nativeEvent.coordinate;
            setPosition({
              latitude: latitude,
              longitude: longitude,
              latitudeDelta: 1,
              longitudeDelta: 1,
            });
          }}
          scrollEnabled={true}
          zoomEnabled={true}
          pitchEnabled={true}
          rotateEnabled={true}>
          {(centalMarker || coords) && (
            <Marker coordinate={centalMarker || coords} onPress={handleSaveLocation}>
              <View style={{alignItems: 'center', justifyContent: 'flex-start'}}>
                <View style={{marginTop: 10}}>
                  <FlagMarker />
                </View>
              </View>
            </Marker>
          )}
        </MapView>
      </View>

      <BottomSheet button={currentPositionButton} onPositionChange={handlePositionChange}>
        <View onStartShouldSetResponder={() => true}>
          <View style={{height: height}}>
            <BottomSheetContent setPosition={setPosition} focusOnLocation={focusOnLocation} />
          </View>
        </View>
      </BottomSheet>
    </CustomModal>
  );
};
export default LocationModal;
