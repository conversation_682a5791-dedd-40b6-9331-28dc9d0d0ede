import axios from 'axios';
import {Alert, Linking, PermissionsAndroid, Platform, ToastAndroid} from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import {useMapsContext} from '~providers/maps/zustand';
import PLATFORM from '../../../types/platform';
import {getAddressComponent} from '../LocationModal';
import {GOOGLE_API_KEY} from '@env';
import {useUpdateUser} from '~hooks/user/useUpdateUser';

const useGetCurrentPosition = () => {
  const {setUserLocation, setCurrentPositionState, setPermissionStatus, permissionStatus} = useMapsContext();
  const {mutateAsync: updateUserMutation} = useUpdateUser();

  const getLocationName = async (latitude: number, longitude: number) => {
    const apiKey = GOOGLE_API_KEY;
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}&language=en`;

    try {
      const response = await axios.get(url);
      const addressComponents = response.data.results[0].address_components;
      const city = getAddressComponent(addressComponents, 'locality');
      const city2 = getAddressComponent(addressComponents, 'postal_town');
      const state = getAddressComponent(addressComponents, 'administrative_area_level_1');
      const country = getAddressComponent(addressComponents, 'country');
      return `${city || city2 || state}, ${country}`;
    } catch (error) {
      console.error(error);
      return null;
    }
  };

  const getCurrentPositionGeolocation = (shouldSetCurrentPositionState?: boolean) => {
    Geolocation.getCurrentPosition(
      async position => {
        const locationName = await getLocationName(position.coords.latitude, position.coords.longitude);
        setUserLocation({...position, locationName});
        if (shouldSetCurrentPositionState) {
          setCurrentPositionState({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            address: locationName || '',
          });
          await updateUserMutation({
            coords_real: {lat: position.coords.latitude, long: position.coords.longitude},
          });
        }
      },
      error => {
        if (error.code === 3) {
          return;
        }
        setUserLocation(null);
        return;
      },
      {
        accuracy: {
          android: 'high',
          ios: 'best',
        },
        enableHighAccuracy: true,
        timeout: 15000,
        distanceFilter: 0,
        forceRequestLocation: true,
        forceLocationManager: true,
        showLocationDialog: true,
      },
    );
  };

  const openSetting = () => {
    Linking.openSettings().catch(() => {
      Alert.alert('Unable to open settings');
    });
  };

  const getCurrentPosition = (shouldSetCurrentPositionState?: boolean, shouldResetIfNoPermissions?: boolean) => {
    const hasPermissionIOS = async () => {
      const status = await Geolocation.requestAuthorization('whenInUse');

      if (status === 'granted') {
        return true;
      }

      if (status === 'disabled') {
        setUserLocation(null);
        Alert.alert(
          'Settings',
          'If you want to use your location to see where you are, you can enable the location services in the settings',
          [
            {text: 'Go to Settings', onPress: openSetting},
            {text: "Don't Use Location", onPress: () => {}},
          ],
        );
      }

      return false;
    };

    const hasLocationPermission = async () => {
      if (Platform.OS === 'ios') {
        const hasPermission = await hasPermissionIOS();
        setPermissionStatus(hasPermission ? 'granted' : 'denied');
        return hasPermission;
      }

      if (Platform.OS === 'android' && Platform.Version < 23) {
        return true;
      }

      const hasPermission = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION);

      if (hasPermission) {
        return true;
      }

      const status = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION);

      if (status === PermissionsAndroid.RESULTS.GRANTED) {
        setPermissionStatus(status);
        return true;
      }

      if (status === PermissionsAndroid.RESULTS.DENIED) {
        setPermissionStatus(status);
        setUserLocation(null);
        ToastAndroid.show('Location permission denied by user.', ToastAndroid.LONG);
      } else if (status === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
        setPermissionStatus(status);
        setUserLocation(null);
        ToastAndroid.show('Location permission revoked by user.', ToastAndroid.LONG);
      }

      return false;
    };

    const getLocation = async () => {
      const hasPermission = await hasLocationPermission();

      if (!hasPermission) {
        if (shouldResetIfNoPermissions) {
          await updateUserMutation({
            coords_real: null,
          });
        }
        return;
      }

      getCurrentPositionGeolocation(shouldSetCurrentPositionState);
    };
    getLocation();
  };

  const handleUpdatePositionAfterSettings = () => {
    getCurrentPositionGeolocation();
  };

  const handleGoToSettings = () => {
    if (PLATFORM.isAndroid) {
      if (permissionStatus === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
        setPermissionStatus(null);
        return Alert.alert(
          'Settings',
          'If you want to use your location to see where you are, you can enable the location services in the settings',
          [
            {
              text: 'Go to Settings',
              onPress: openSetting,
            },
            {
              text: "Don't Use Location",
              onPress: () => {
                setPermissionStatus(PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN);
              },
            },
          ],
        );
      }
      return getCurrentPosition();
    }
    if (PLATFORM.isIos) {
      return Alert.alert(
        'Settings',
        'If you want to use your location to see where you are, you can enable the location services in the settings',
        [
          {
            text: 'Go to Settings',
            onPress: openSetting,
          },
          {text: "Don't Use Location", onPress: () => {}},
        ],
      );
    }
  };

  return {
    getLocationName,
    getCurrentPosition,
    handleGoToSettings,
    handleUpdatePositionAfterSettings,
    getCurrentPositionGeolocation,
  };
};

export default useGetCurrentPosition;
