import {useEffect, useMemo, useState} from 'react';
import {useMapsContext} from '~providers/maps/zustand';
import MapView from 'react-native-maps';

const initialRegion = {
  latitude: 44.016521,
  longitude: 21.005859,
  latitudeDelta: 2,
  longitudeDelta: 2,
};
const userLocationId = 'userLocationId';

const TIMEOUTS = {
  zoomToUserAndLocation: 300,
  enableLocationsAfterUserZoom: 400,
  enableLocations: 750,
};

const useGoogleMapsAnimation = (mapRef: React.MutableRefObject<MapView | null>) => {
  const [isCurrentGeolocationActive, setIsCurrentGeolocationActive] = useState(false);
  const {userLocation: selfPosition} = useMapsContext();

  const focusOnLocation = ({latitude, longitude}: {latitude: number; longitude: number}) => {
    try {
      mapRef?.current?.animateCamera(
        {
          center: {
            latitude,
            longitude,
          },
          pitch: 4,
          heading: 0,
          altitude: 1000,
          zoom: 15.5,
        },
        {
          duration: 300,
        },
      );
    } catch (error) {
      console.warn('Camera animation failed:', error);
    }
  };

  const focusOnUser = () => {
    if (!!selfPosition?.coords && mapRef.current) {
      if (!isCurrentGeolocationActive) {
        mapRef.current.animateCamera(
          {
            center: {
              latitude: selfPosition.coords.latitude,
              longitude: selfPosition.coords.longitude,
            },
            pitch: 4,
            heading: 0,
            altitude: 1000,
            zoom: 15.5,
          },
          {
            duration: 300,
          },
        );
        setTimeout(() => setIsCurrentGeolocationActive(true), 400);
        return;
      }

      setTimeout(() => setIsCurrentGeolocationActive(false), 400);
    }
  };

  //   const handleMarkerPress = (location: ILocation) => {
  //     setChosenLocation(location);
  //     setLocationText({
  //       title: location.name,
  //       subTitle: `${location.streetName} ${location.streetNumber}`,
  //     });
  //     const actualRegion = mockedArrayWithRegions.filter((region: IRegion) => region.name === location.regionName);
  //     setChosenRegion(actualRegion[0]);
  //     const split = actualRegion[0].name.split('-');
  //     const title = split[0];
  //     const subTitle = split?.[1];
  //     setRegionText({
  //       title,
  //       subTitle,
  //     });
  //     setIsValidNumber(true);
  //   };

  return {
    initialRegion,
    setIsCurrentGeolocationActive,
    isCurrentGeolocationActive,
    focusOnUser,
    focusOnLocation,
  };
};
export default useGoogleMapsAnimation;
