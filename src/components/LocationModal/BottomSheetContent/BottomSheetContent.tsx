import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {SearchBarWithAutocomplete, useDebounce} from '~/components/SearchBarWithAutocomplete';
import HistoryLocations from '~/components/HistoryLocations';
import {useState} from 'react';
import {PredictionType} from '~types/maps/index.type';
import axios from 'axios';
import {GOOGLE_API_KEY} from '@env';
const GOOGLE_PACES_API_BASE_URL = 'https://maps.googleapis.com/maps/api/place';

const BottomSheetContent = ({
  setPosition,
  focusOnLocation,
}: {
  setPosition: (props: {latitude: number; longitude: number; latitudeDelta: number; longitudeDelta: number}) => void;
  focusOnLocation: (props: {latitude: number; longitude: number}) => void;
}) => {
  const [search, setSearch] = useState({term: '', fetchPredictions: false});
  const [showPredictions, setShowPredictions] = useState(false);
  const [predictions, setPredictions] = useState<PredictionType[]>([]);

  const {body} = styles;

  const onChangeText = async () => {
    if (search.term.trim() === '') {
      return;
    }
    if (!search.fetchPredictions) {
      return;
    }

    const apiUrl = `${GOOGLE_PACES_API_BASE_URL}/autocomplete/json?key=${GOOGLE_API_KEY}&input=${search.term}`;
    try {
      const result = await axios.request({
        method: 'post',
        url: apiUrl,
      });
      if (result) {
        const {
          data: {predictions},
        } = result;

        setPredictions(predictions);
        setShowPredictions(true);
      }
    } catch (e) {
      console.log(e);
    }
  };
  useDebounce(onChangeText, 1000, [search.term]);

  /**
   * Grab lattitude and longitude on prediction tapped
   *    by sending another reqyest using the place id.
   * You can check what kind of information you can get at:
   *    https://developers.google.com/maps/documentation/places/web-service/details#PlaceDetailsRequests
   */
  const onPredictionTapped = async (placeId: string, description: string) => {
    const apiUrl = `${GOOGLE_PACES_API_BASE_URL}/details/json?key=${GOOGLE_API_KEY}&place_id=${placeId}`;
    try {
      const result = await axios.request({
        method: 'post',
        url: apiUrl,
      });
      if (result) {
        const {
          data: {
            result: {
              geometry: {location},
            },
          },
        } = result;
        const {lat, lng} = location;
        setPosition({latitude: lat, longitude: lng, latitudeDelta: 1, longitudeDelta: 1});
        focusOnLocation({latitude: lat, longitude: lng});
        setSearch({term: description, fetchPredictions: false});
      }
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <View style={body}>
      <SearchBarWithAutocomplete
        value={search.term}
        onChangeText={text => {
          setSearch({term: text, fetchPredictions: true});
        }}
        showPredictions={showPredictions}
        predictions={predictions}
        onPredictionTapped={onPredictionTapped}
      />

      {/* <HistoryLocations onHistoryItemTapped={onHistoryItemTapped} /> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  body: {
    paddingHorizontal: 20,
  },
});

export default BottomSheetContent;
