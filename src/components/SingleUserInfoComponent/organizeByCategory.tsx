import storage from '@react-native-firebase/storage';
interface Category {
  category_id: number;
  category_name: string;
  color: string;
  image_url: string;
}

interface SubCategory {
  category_id: number;
  subcategory_id: number;
  image_url: string;
  subcategory_name: string;
  subcategory_repr: any;
}

const categories: Category[] = [
  {
    category_id: 2,
    category_name: 'Sports & Fitness',
    color: 'inherit',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Sports---Fitness/ce91c7e3-1855-49b9-be14-20b7a7dbaffe-Sports---Fitness.jpg',
  },
  {
    category_id: 3,
    category_name: 'Food,Drinks & Dining',
    color: 'primary',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Food--Drinks---Dining/f2e5ee31-7262-47ec-ba89-c2ce494c32c0-Food--Drinks---Dining.jpg',
  },
  {
    category_id: 4,
    category_name: 'Arts & culture',
    color: 'secondary',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Arts---culture/72685f46-0fa2-44d0-8f3f-5357be3c06eb-Arts---culture.jpg',
  },
  {
    category_id: 5,
    category_name: 'Music',
    color: 'success',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Music/f9b27110-b0e3-4b34-9353-3a40ddd33a66-Music.jpg',
  },
  {
    category_id: 6,
    category_name: 'Gaming',
    color: 'error',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Gaming/13fe3c0c-5d88-4ccb-a65b-af9e3393146a-Gaming.jpg',
  },
  {
    category_id: 7,
    category_name: 'Outdoor Activities',
    color: 'info',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Outdoor-Activities/a5dd0990-156c-4091-b1aa-d70fb740a266-Outdoor-Activities.jpg',
  },
  {
    category_id: 8,
    category_name: 'Fashion & Style',
    color: 'warning',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Fashion---Style/8bbff433-6513-47bf-a653-69183086b6ba-Fashion---Style.jpg',
  },
  {
    category_id: 9,
    category_name: 'Travel and Exploration',
    color: 'inherit',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Travel-and-Exploration/696f1780-21b6-4a2c-a4a4-ecb724d6cb4d-Travel-and-Exploration.jpg',
  },
  {
    category_id: 10,
    category_name: 'Party',
    color: 'primary',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Party/8c7ba3cc-da61-464a-be78-2f7109d58228-Party.jpg',
  },
  {
    category_id: 11,
    category_name: 'Business & Education',
    color: 'secondary',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Business---Education/38dd5b0c-5478-4d5f-97ac-77973e1c0160-Business---Education.jpg',
  },
  {
    category_id: 12,
    category_name: 'Social Causes & Activism',
    color: 'success',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Social-Causes---Activism/f844c977-8689-4c1f-ace2-00f3bafd9cff-Social-Causes---Activism.png',
  },
  {
    category_id: 13,
    category_name: 'Family & Parenting',
    color: 'error',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Family---Parenting/06aa0af6-efcc-4437-8ed0-7cc0fae90321-Family---Parenting.jpg',
  },
  {
    category_id: 14,
    category_name: 'Literature',
    color: 'info',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Literature/8a7546f1-ec7b-469c-a093-f05e81ef3225-Literature.png',
  },
  {
    category_id: 15,
    category_name: 'Animals',
    color: 'warning',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Animals/fc5b9791-ce22-4ed0-bc89-5df8ef58058b-Animals.jpg',
  },
  {
    category_id: 16,
    category_name: 'Romance',
    color: 'primary',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Romance/f7364874-bba0-472a-8519-eb201c861ef7-Romance.jpg',
  },
  {
    category_id: 17,
    category_name: 'Extreme',
    color: 'secondary',
    image_url:
      'https://storage.googleapis.com/pyxida-e6211.appspot.com/categoriesV3/Extreme/f250e33c-1fa6-4c7e-85bf-6e0e77e54bca-Extreme.jpg',
  },
];

export const organizeByCategory = (firstCategories: any[] | undefined, subcategories: SubCategory[] | undefined) => {  const sortedCategories = [...categories]?.filter(category =>
    firstCategories?.length ? firstCategories?.includes(category.category_id) : true,
  );

  const result = sortedCategories.reduce((acc, category) => {
    const {category_id, category_name, color} = category;
    return {
      ...acc,
      [category_name]: {
        color,
        subcategories: subcategories?.filter(sub => sub?.category_id === category_id),
      },
    };
  }, {});

  return result;
};
