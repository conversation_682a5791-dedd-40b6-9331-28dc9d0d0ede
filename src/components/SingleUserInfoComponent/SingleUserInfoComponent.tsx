import auth from '@react-native-firebase/auth';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {FadeIn, FadeOut, Layout} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Path, Svg} from 'react-native-svg';
import {SCREENS} from '~constants';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useGetUserSubcategories} from '~hooks/user/useGetUserSubcategories';
import FirebaseChatsService from '~services/FirebaseChats';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {organizeByCategory} from './organizeByCategory';
import {useTranslation} from 'react-i18next';
import firestore from '@react-native-firebase/firestore';
import {ChatType} from '~types/chat';
import {GOOGLE_API_KEY} from '@env';
import axios from 'axios';
import {LocationIcon} from '~assets/icons';
import DefaultItem from '~components/HomeScreenComponent/tabComponents/components/DefaultItem';
import {useAllEvents} from '~hooks/event/useAllEvents';
import {ORDER_BY, ORDER_DIR, TABS} from '~types/events';
import {getListFromPages} from '~components/HomeScreenComponent/helpers/getListFromPages';
import {Event} from '~types/api/event';
import {TabView, SceneMap, TabBar} from 'react-native-tab-view';
import {useAllOrganiserEvent} from '~hooks/event/useAllOrganiserEvent';
import {useTheme} from '~contexts/ThemeContext';

const CancelIcon = ({color}: {color: string}) => (
  <Svg viewBox="0 0 48 48" width="24px" height="24px">
    <Path
      fill={color}
      d="M 38.982422 6.9707031 A 2.0002 2.0002 0 0 0 37.585938 7.5859375 L 24 21.171875 L 10.414062 7.5859375 A 2.0002 2.0002 0 0 0 8.9785156 6.9804688 A 2.0002 2.0002 0 0 0 7.5859375 10.414062 L 21.171875 24 L 7.5859375 37.585938 A 2.0002 2.0002 0 1 0 10.414062 40.414062 L 24 26.828125 L 37.585938 40.414062 A 2.0002 2.0002 0 1 0 40.414062 37.585938 L 26.828125 24 L 40.414062 10.414062 A 2.0002 2.0002 0 0 0 38.982422 6.9707031 z"
    />
  </Svg>
);

const {height} = Dimensions.get('window');

export enum BUTTON_STATUS_ENUM {
  LOADING = 'loading',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  SENT = 'sent',
  EMPTY = 'empty',
}
interface SubCategory {
  category_id: number;
  subcategory_id: number;
  image_url: string;
  subcategory_name: string;
  subcategory_repr: any;
}

const SingleUserInfoComponent = () => {
  const {colors} = useTheme();
  const styles = createStyles(colors);
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.PERSONAL_INFO>>();
  const {data} = useGetUserAccount(auth().currentUser!.uid);
  const {bottom} = useSafeAreaInsets();
  const {t} = useTranslation();
  const [activeTab, setActiveTab] = useState(0);

  const navigation = useNavigation<NavigationProps>();
  const {source, tag, name, description, user_id, eventName, coords} = params.user;

  const {data: allSubcategories} = useGetUserSubcategories(user_id);
  const [location, setLocation] = useState('');
  const {
    data: eventData,
    isLoading,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    remove,
  } = useAllOrganiserEvent({
    distance_km: **********,
    tab: TABS.USER_SPECIFIC_EVENT,
    order_by: ORDER_BY.START_DATE,
    order_dir: ORDER_DIR.ASC,
    event_age_group: null,
    user_id: user_id,
    enabled: false,
    timeframe: 'future',
    filter_my_event_type: 'created',
  });

  const {
    data: eventGoingData,
    fetchNextPage: fetchNextGoingPage,
    hasNextPage: hasNextGoingPage,
    isFetchingNextPage: isFetchingGoingNextPage,
    refetch: refetchGoing,
  } = useAllEvents({
    distance_km: **********,
    tab: TABS.USER_SPECIFIC_GOING_EVENT,
    order_by: ORDER_BY.START_DATE,
    order_dir: ORDER_DIR.ASC,
    event_age_group: null,
    user_id: user_id,
    enabled: false,
    timeframe: 'future',
    filter_my_event_type: 'attending',
  });

  const [index, setIndex] = useState(0);
  const [routes] = useState([
    {key: 'created', title: 'Created'},
    {key: 'going', title: 'Going'},
  ]);
  const eventsGoingList = getListFromPages<Event>(eventGoingData);

  const eventsList = getListFromPages<Event>(eventData);

  useEffect(() => {
    if (user_id) {
      refetchGoing();
      refetch();
    }
  }, [user_id]);

  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, []);

  const descriptionComponent = useMemo(() => {
    return description ? (
      <Text
        style={{
          fontWeight: '500',
          color: colors.background,
          fontSize: 16,
        }}>
        {description.trim()}
      </Text>
    ) : null;
  }, [description]);

  const getLocationName = async (latitude: number, longitude: number) => {
    const apiKey = GOOGLE_API_KEY;
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}&language=en`;
    console.log(url, 'url');

    try {
      const response = await axios.get(url);

      // Initialize variables to hold the address components
      let locality = '';
      let postal_town = '';
      let country = '';
      let sublocality = '';
      let sublocalityLevel1 = '';
      let route = '';

      // Loop through all results and try to extract relevant components
      for (let result of response.data.results) {
        const addressComponents = result.address_components || [];

        // Extract the components from the current result
        postal_town = postal_town || getAddressComponent(addressComponents, 'postal_town');
        country = country || getAddressComponent(addressComponents, 'country');
        locality = locality || getAddressComponent(addressComponents, 'locality');
        sublocality = sublocality || getAddressComponent(addressComponents, 'sublocality');
        route = route || getAddressComponent(addressComponents, 'route');
        sublocalityLevel1 = sublocalityLevel1 || getAddressComponent(addressComponents, 'sublocality_level_1');

        // If we have found all the components we need, break the loop
        if (locality && postal_town && country && sublocality && sublocalityLevel1) {
          break;
        }
      }

      // Set the location using the best available components
      if (postal_town) {
        setLocation(`${sublocality || sublocalityLevel1 || locality}, ${postal_town}, ${country}`);
      } else {
        setLocation(`${sublocality || sublocalityLevel1 || locality}, ${country}`);
      }
    } catch (error) {
      console.error(error);
    }
  };

  // Helper function to get the component based on type
  const getAddressComponent = (components: any[], type: string) => {
    const component = components.find(comp => comp.types.includes(type));
    return component ? component.long_name : '';
  };

  useEffect(() => {
    if (coords) {
      getLocationName(coords?.lat!, coords?.long!);
    }
  }, []);

  const handleLoadMoreGoing = useCallback(() => {
    if (hasNextGoingPage && !isFetchingGoingNextPage) {
      fetchNextGoingPage();
    }
  }, [hasNextGoingPage, isFetchingGoingNextPage, fetchNextGoingPage]);

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const CreatedEvents = ({events}: {events: Event[]}) => (
    <FlatList
      contentContainerStyle={{paddingBottom: 100, flexGrow: 1, width: Dimensions.get('window').width - 32}}
      data={events}
      keyExtractor={(item, index) => `${index}-${item.event_id}`}
      renderItem={({item, index}) => <DefaultItem item={item} i={index} disableAnimation={true} />}
      onEndReached={handleLoadMore}
      nestedScrollEnabled
      onEndReachedThreshold={0.5}
      scrollEnabled={false}
    />
  );

  const GoingEvents = ({events}: {events: Event[]}) => (
    <FlatList
      contentContainerStyle={{paddingBottom: 100, flexGrow: 1, width: Dimensions.get('window').width - 32}}
      data={events}
      keyExtractor={(item, index) => `${index}-${item.event_id}`}
      renderItem={({item, index}) => <DefaultItem item={item} i={index} disableAnimation={true} />}
      onEndReached={handleLoadMoreGoing}
      nestedScrollEnabled
      onEndReachedThreshold={0.5}
      scrollEnabled={false}
    />
  );

  const renderScene = SceneMap({
    created: () => <CreatedEvents events={eventsList} />,
    going: () => <GoingEvents events={eventsGoingList} />,
  });

  const tabs = [
    {key: 'created', title: 'Created', component: <CreatedEvents events={eventsList} />},
    {key: 'going', title: 'Going', component: <GoingEvents events={eventsGoingList} />},
  ];

  return (
    <View style={{flexGrow: 1}}>
      <TouchableOpacity
        onPress={() => {
          navigation.goBack();
        }}
        style={{
          position: 'absolute',
          top: Platform.OS === 'ios' ? 65 : 40,
          right: 15,
          zIndex: 300,
          borderWidth: 2,
          borderRadius: 8,
          padding: 6,
          borderColor: colors.background,
        }}>
        <CancelIcon color={colors.background} />
      </TouchableOpacity>
      <LinearGradient
        colors={[colors.textPrimary, colors.textPrimary]}
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 0.15}}
        style={{
          flex: 1,
        }}>
        <ScrollView
          style={{flex: 1, marginBottom: 10}}
          contentContainerStyle={{flexGrow: 1, marginBottom: 30}}
          nestedScrollEnabled
          showsVerticalScrollIndicator={false}>
          <Image source={{uri: source}} style={styles.image} />
          <View style={{width: '100%'}}>
            <Text style={{fontSize: 32, color: 'orange', textAlign: 'center'}}>{name}</Text>
            {coords && (
              <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}}>
                {/* Location Icon */}
                <LocationIcon />
                {/* Location Text */}
                <Text style={{fontSize: 16, color: 'orange', textAlign: 'center', marginLeft: 8}}>{location}</Text>
              </View>
            )}

            <View style={{marginTop: 32, marginBottom: 24, paddingHorizontal: 16}}>
              <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>{descriptionComponent}</View>
            </View>

            {allSubcategories &&
              Object.entries<Record<string, {color: string; subcategories: SubCategory[]}>>(
                organizeByCategory([], allSubcategories),
              ).map(
                ([category, items]) =>
                  items?.subcategories?.length > 0 && (
                    <View style={{paddingHorizontal: 16}} key={category}>
                      {items?.subcategories?.length ? (
                        <Text style={{color: colors.white, fontSize: 18, fontWeight: '700', marginBottom: 5}}>
                          {category}
                        </Text>
                      ) : null}

                      <View style={styles.vStack}>
                        {items?.subcategories?.map((item, index) => (
                          <TouchableOpacity style={styles.button} disabled={true} key={item.subcategory_id.toString()}>
                            <Text style={styles.buttonText}>{item?.subcategory_name}</Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  ),
              )}
          </View>
          {eventsList && eventsList.length > 0 && allSubcategories && (
            <View style={{flexDirection: 'row', padding: 10, marginTop: 10}}>
              {tabs.map((tab, index) => (
                <TouchableOpacity
                  key={tab.key}
                  onPress={() => setActiveTab(index)}
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    padding: 10,
                    borderBottomWidth: 3,
                    borderBottomColor: activeTab === index ? 'orange' : 'transparent',
                  }}>
                  <Text style={{color: colors.white}}>{tab.title}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
          {eventsList && eventsList.length > 0 && allSubcategories && (
            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', width: '100%'}}>
              {tabs[activeTab].component}
            </View>
          )}
          {eventsList && eventsList.length > 0 && false && (
            <>
              <Text style={styles.titelText}>Events</Text>
              <TabView
                navigationState={{index, routes}}
                renderScene={renderScene}
                style={{flex: 1, height: 100000}}
                overScrollMode={'always'}
                onIndexChange={setIndex}
                initialLayout={{width: Dimensions.get('window').width}}
                renderTabBar={props => (
                  <TabBar
                    {...props}
                    style={{backgroundColor: 'transparent'}} // Transparent background
                    indicatorStyle={{backgroundColor: 'orange'}} // Orange indicator
                    labelStyle={{color: colors.white}}
                  />
                )}
              />
            </>
          )}
        </ScrollView>
        {params?.user?.user_id && params?.user?.user_id !== auth().currentUser!.uid && (
          <TouchableOpacity
            onPress={async () => {
              if (data) {
                const chatId = await FirebaseChatsService.createPrivateChat({
                  user_id1: auth().currentUser!.uid,
                  user_id2: params.user.user_id,
                  user_name1: `${data.first_name} ${data.last_name || ''}`,
                  user_name2: params.user.name,
                  user_image: data.photo,
                });

                const chatRef = firestore().collection('chats').doc(chatId);
                const doc = await chatRef.get();
                if (doc.exists) {
                  const chatData = doc.data() as ChatType;
                  const updatedMessages = chatData.history.map((message: any) => {
                    if (!message.readUserIds?.includes(auth().currentUser!.uid)) {
                      console.log('Updating message:', message);
                      return {...message, readUserIds: [...(message.readUserIds || []), auth().currentUser!.uid]};
                    }
                    return message;
                  });

                  await chatRef.update({history: updatedMessages});
                }

                navigation.navigate(SCREENS.CHAT_STACK, {key: chatId});
              }
            }}
            style={{
              paddingHorizontal: 30,
              paddingVertical: 12,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: colors.statusBlue,
              borderRadius: 100,
              marginHorizontal: 50,
              marginBottom: bottom + 20,
            }}>
            <Text style={{color: colors.background, fontSize: 16, lineHeight: 24}}>Chat</Text>
          </TouchableOpacity>
        )}
      </LinearGradient>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    titelText: {
      color: 'orange',
      fontSize: 22,
      fontWeight: 'bold',
      marginBottom: 5,
      marginLeft: 16,
    },
    image: {
      width: '100%',
      height: Dimensions.get('window').width * 0.5,
    },
    vStack: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
      marginBottom: 20,
      marginTop: 5,
    },
    button: {
      padding: 12,
      marginRight: 16,
      marginTop: 4,
      marginBottom: 8,
      borderRadius: 100,
      borderWidth: 1,
      borderColor: colors.textSecondary,
    },
    buttonText: {
      fontSize: 14,
      fontWeight: '400',
      color: colors.eventInfluencer,
    },
  });

export default SingleUserInfoComponent;
