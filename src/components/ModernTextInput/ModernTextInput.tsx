import React, {useState, useRef} from 'react';
import {
  TextInput,
  Animated,
  TextStyle,
  TouchableWithoutFeedback,
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  TextInputProps,
} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, typography, shadows} from '~constants/design';
import {EyeIcon, EyeOffIcon} from '~assets/icons';

interface ModernTextInputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  errorText?: string;
  isPassword?: boolean;
  disabled?: boolean;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'sm' | 'md' | 'lg';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  style?: ViewStyle;
  inputStyle?: TextStyle;
  fullWidth?: boolean;
}

const ModernTextInput: React.FC<ModernTextInputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  errorText,
  isPassword = false,
  disabled = false,
  variant = 'outlined',
  size = 'md',
  leftIcon,
  rightIcon,
  style,
  inputStyle,
  fullWidth = true,
  ...textInputProps
}) => {
  const {colors} = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!isPassword);
  const animatedIsFocused = useRef(new Animated.Value(value ? 1 : 0)).current;
  const inputRef = useRef<TextInput>(null);

  const handleFocus = () => {
    setIsFocused(true);
    Animated.timing(animatedIsFocused, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const handleBlur = () => {
    setIsFocused(false);
    Animated.timing(animatedIsFocused, {
      toValue: value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const handlePress = () => {
    inputRef.current?.focus();
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  // Size styles
  const sizeStyles = {
    sm: {
      height: 40,
      paddingHorizontal: spacing.sm,
      fontSize: typography.fontSize.sm,
      marginTop: 10,
    },
    md: {
      height: 48,
      paddingHorizontal: spacing.md,
      fontSize: typography.fontSize.base,
      marginTop: 10,
    },
    lg: {
      height: 56,
      paddingHorizontal: spacing.lg,
      fontSize: typography.fontSize.lg,
      marginTop: 10,
    },
  };

  // Variant styles
  const getVariantStyles = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: borderRadius.lg,
      ...shadows.none,
    };

    switch (variant) {
      case 'filled':
        return {
          ...baseStyle,
          backgroundColor: colors.surface,
          borderWidth: 0,
        };
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: colors.background,
          borderWidth: 0.5,
          borderColor: errorText ? colors.error : isFocused ? colors.primary : colors.border,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: colors.background,
          borderWidth: 0.5,
          borderColor: errorText ? colors.error : isFocused ? colors.primary : colors.border,
        };
    }
  };

  const containerStyle: ViewStyle = {
    width: fullWidth ? '100%' : 'auto',
    marginBottom: spacing.sm,
    ...style,
  };

  const inputContainerStyle: ViewStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    ...sizeStyles[size],
    ...getVariantStyles(),
    opacity: disabled ? 0.6 : 1,
  };

  const textInputStyle: TextStyle = {
    flex: 1,
    fontSize: sizeStyles[size].fontSize,
    color: colors.textPrimary,
    fontWeight: typography.fontWeight.normal,
    ...inputStyle,
  };

  const labelStyle: TextStyle = {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: errorText ? colors.error : colors.textSecondary,
    marginBottom: spacing.xs,
  };

  const errorStyle: TextStyle = {
    fontSize: typography.fontSize.xs,
    color: colors.error,
    marginTop: spacing.xs,
    fontWeight: typography.fontWeight.normal,
  };

  return (
    <View style={containerStyle}>
      {label && <Text style={labelStyle}>{label}</Text>}

      <TouchableWithoutFeedback onPress={handlePress}>
        <Animated.View style={inputContainerStyle}>
          {leftIcon && <View style={{marginRight: spacing.sm}}>{leftIcon}</View>}

          <TextInput
            ref={inputRef}
            style={textInputStyle}
            value={value}
            onChangeText={onChangeText}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            placeholderTextColor={colors.textSecondary}
            secureTextEntry={isPassword && !isPasswordVisible}
            editable={!disabled}
            {...textInputProps}
          />

          {isPassword && (
            <TouchableOpacity
              onPress={togglePasswordVisibility}
              style={{
                marginLeft: spacing.sm,
                padding: spacing.xs,
              }}>
              {isPasswordVisible ? <EyeIcon /> : <EyeOffIcon />}
            </TouchableOpacity>
          )}

          {rightIcon && !isPassword && <View style={{marginLeft: spacing.sm}}>{rightIcon}</View>}
        </Animated.View>
      </TouchableWithoutFeedback>

      {errorText && <Text style={errorStyle}>{errorText}</Text>}
    </View>
  );
};

export default ModernTextInput;
