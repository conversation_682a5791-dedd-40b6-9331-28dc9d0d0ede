import React, {useState} from 'react';
import {View, Text, Modal, TouchableOpacity, ScrollView, SafeAreaView, StatusBar, Switch} from 'react-native';
import Slider from '@react-native-community/slider';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, typography} from '~constants/design';
import {haptics} from '~Utils/haptics';
import {useTranslation} from 'react-i18next';

interface FullScreenFilterModalProps {
  visible: boolean;
  onClose: () => void;
  selectedTimeframe: any;
  selectedEventType: any;
  timeframes: any[];
  eventTypes: any[];
  isForKids: boolean;
  radius: number;
  onApply: (timeframe: any, eventType: any, isForKids: boolean, radius: number) => void;
}

const FullScreenFilterModal: React.FC<FullScreenFilterModalProps> = ({
  visible,
  onClose,
  selectedTimeframe,
  selectedEventType,
  timeframes,
  eventTypes,
  isForKids,
  radius,
  onApply,
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();

  const [tempTimeframe, setTempTimeframe] = useState(selectedTimeframe);
  const [tempEventType, setTempEventType] = useState(selectedEventType);
  const [tempIsForKids, setTempIsForKids] = useState(isForKids);
  const [tempRadius, setTempRadius] = useState(radius);

  const handleApply = () => {
    haptics.medium();
    onApply(tempTimeframe, tempEventType, tempIsForKids, tempRadius);
    onClose();
  };

  const handleReset = () => {
    haptics.light();
    setTempTimeframe(timeframes[0]);
    setTempEventType(eventTypes[0]);
    setTempIsForKids(false);
    setTempRadius(100);
  };

  const FilterSection = ({title, children}: {title: string; children: React.ReactNode}) => (
    <View style={{marginBottom: spacing.lg}}>
      <Text
        style={{
          fontSize: typography.fontSize.base,
          fontWeight: typography.fontWeight.bold,
          color: colors.textPrimary,
          marginBottom: spacing.sm,
        }}>
        {title}
      </Text>
      {children}
    </View>
  );

  const FilterOption = ({option, isSelected, onPress}: {option: any; isSelected: boolean; onPress: () => void}) => (
    <TouchableOpacity
      onPress={onPress}
      style={{
        paddingVertical: spacing.sm,
        paddingHorizontal: spacing.md,
        borderRadius: borderRadius.md,
        backgroundColor: isSelected ? colors.primary + '20' : 'transparent',
        borderWidth: isSelected ? 1 : 0,
        borderColor: isSelected ? colors.primary : 'transparent',
        marginBottom: spacing.xs,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
      <Text
        style={{
          fontSize: typography.fontSize.base,
          fontWeight: isSelected ? typography.fontWeight.semibold : typography.fontWeight.medium,
          color: isSelected ? colors.primary : colors.textPrimary,
        }}>
        {option.title}
      </Text>
      {isSelected && (
        <View
          style={{
            width: 20,
            height: 20,
            borderRadius: 10,
            backgroundColor: colors.primary,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text style={{color: colors.white, fontSize: 12, fontWeight: 'bold'}}>✓</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="fullScreen">
      <SafeAreaView style={{flex: 1, backgroundColor: colors.background}}>
        <StatusBar barStyle="dark-content" backgroundColor={colors.background} />

        {/* Header */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: spacing.md,
            paddingVertical: spacing.sm,
            borderBottomWidth: 1,
            borderBottomColor: colors.border,
          }}>
          <TouchableOpacity onPress={onClose}>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                fontWeight: typography.fontWeight.medium,
                color: colors.primary,
              }}>
              {t('generic.cancel')}
            </Text>
          </TouchableOpacity>

          <Text
            style={{
              fontSize: typography.fontSize.lg,
              fontWeight: typography.fontWeight.bold,
              color: colors.textPrimary,
            }}>
            {t('home.filters')}
          </Text>

          <TouchableOpacity onPress={handleReset}>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                fontWeight: typography.fontWeight.medium,
                color: colors.primary,
              }}>
              {t('generic.reset')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView
          style={{flex: 1}}
          contentContainerStyle={{
            padding: spacing.md,
          }}>
          {/* Dates Section */}
          <FilterSection title={t('home.dates')}>
            {timeframes.map(timeframe => (
              <FilterOption
                key={timeframe.id}
                option={timeframe}
                isSelected={tempTimeframe?.id === timeframe.id}
                onPress={() => {
                  haptics.light();
                  setTempTimeframe(timeframe);
                }}
              />
            ))}
          </FilterSection>

          {/* Event Types Section */}
          <FilterSection title={t('events.eventtype')}>
            {eventTypes.map(eventType => (
              <FilterOption
                key={eventType.id}
                option={eventType}
                isSelected={tempEventType?.id === eventType.id}
                onPress={() => {
                  haptics.light();
                  setTempEventType(eventType);
                }}
              />
            ))}
          </FilterSection>

          {/* Children Events Section */}
          <FilterSection title={t('home.more')}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingVertical: spacing.sm,
                paddingHorizontal: spacing.md,
                borderRadius: borderRadius.md,
                backgroundColor: colors.surface,
                marginBottom: spacing.sm,
              }}>
              <Text
                style={{
                  fontSize: typography.fontSize.base,
                  fontWeight: typography.fontWeight.medium,
                  color: colors.textPrimary,
                }}>
                {t('home.children_events')}
              </Text>
              <Switch
                value={tempIsForKids}
                onValueChange={value => {
                  haptics.light();
                  setTempIsForKids(value);
                }}
                trackColor={{
                  false: colors.border,
                  true: colors.primary,
                }}
                thumbColor={colors.white}
              />
            </View>

            {/* Radius Section */}
            <View
              style={{
                paddingVertical: spacing.sm,
                paddingHorizontal: spacing.md,
                borderRadius: borderRadius.md,
                backgroundColor: colors.surface,
              }}>
              <View style={{marginBottom: spacing.sm}}>
                <Text
                  style={{
                    fontSize: typography.fontSize.base,
                    fontWeight: typography.fontWeight.semibold,
                    color: colors.textPrimary,
                    marginBottom: spacing.xs,
                  }}>
                  {t('home.radius')}
                </Text>
                <Text
                  style={{
                    fontSize: typography.fontSize.sm,
                    color: colors.textSecondary,
                  }}>
                  {tempRadius} km
                </Text>
              </View>
              <Slider
                style={{width: '100%', height: 30}}
                minimumValue={1}
                maximumValue={1000}
                minimumTrackTintColor={colors.primary}
                maximumTrackTintColor={colors.border}
                step={1}
                value={tempRadius}
                onValueChange={value => {
                  setTempRadius(value);
                }}
                onSlidingComplete={() => {
                  haptics.light();
                }}
              />
            </View>
          </FilterSection>
        </ScrollView>

        {/* Apply Button */}
        <View
          style={{
            padding: spacing.md,
            borderTopWidth: 1,
            borderTopColor: colors.border,
          }}>
          <TouchableOpacity
            onPress={handleApply}
            style={{
              backgroundColor: colors.primary,
              paddingVertical: spacing.sm,
              borderRadius: borderRadius.lg,
              alignItems: 'center',
            }}>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                fontWeight: typography.fontWeight.bold,
                color: colors.white,
              }}>
              {t('events.apply')}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default FullScreenFilterModal;
