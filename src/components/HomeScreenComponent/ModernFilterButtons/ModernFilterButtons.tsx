import React from 'react';
import {View, Text, ScrollView, Pressable} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, borderRadius, typography} from '~constants/design';
import {FadeIn} from '~components/MicroInteractions/MicroInteractions';
import {haptics} from '~Utils/haptics';
import {useTranslation} from 'react-i18next';

interface ModernFilterButtonsProps {
  setIsMapView: (value: boolean) => void;
  isMapView: boolean;
  selectedTimeframe: any;
  selectedEventType: any;
  onTimeframePress?: () => void;
  onEventTypePress?: () => void;
  onFilterPress?: () => void;
}

const ModernFilterButtons: React.FC<ModernFilterButtonsProps> = ({
  setIsMapView,
  isMapView,
  selectedTimeframe,
  selectedEventType,
  onTimeframePress,
  onEventTypePress,
  onFilterPress,
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();

  const handleTimeframePress = () => {
    haptics.light();
    onTimeframePress?.();
  };

  const handleEventTypePress = () => {
    haptics.light();
    onEventTypePress?.();
  };

  const handleFilterPress = () => {
    haptics.light();
    onFilterPress?.();
  };

  // Modern compact filter chip inspired by Airbnb/Uber
  const FilterChip = ({
    label,
    isSelected = false,
    onPress,
    count,
  }: {
    label: string;
    isSelected?: boolean;
    onPress: () => void;
    count?: number;
  }) => (
    <Pressable
      onPress={onPress}
      style={({pressed}) => ({
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: spacing.sm,
        paddingVertical: spacing.xs,
        borderRadius: borderRadius.full,
        backgroundColor: isSelected ? colors.textPrimary : colors.surface,
        borderWidth: 1,
        borderColor: isSelected ? colors.textPrimary : colors.border,
        marginRight: spacing.xs,
        minHeight: 32,
        transform: [{scale: pressed ? 0.95 : 1}],
      })}>
      <Text
        style={{
          fontSize: typography.fontSize.xs,
          fontWeight: typography.fontWeight.semibold,
          color: isSelected ? colors.white : colors.textPrimary,
        }}>
        {label}
      </Text>
      {count && count > 0 && (
        <View
          style={{
            backgroundColor: isSelected ? colors.white : colors.primary,
            borderRadius: borderRadius.full,
            paddingHorizontal: spacing.xs,
            paddingVertical: 2,
            marginLeft: spacing.xs,
            minWidth: 18,
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: 10,
              fontWeight: typography.fontWeight.bold,
              color: isSelected ? colors.textPrimary : colors.white,
            }}>
            {count}
          </Text>
        </View>
      )}
    </Pressable>
  );

  // Compact view toggle inspired by modern apps
  const ViewToggle = () => (
    <View
      style={{
        flexDirection: 'row',
        backgroundColor: colors.surface,
        borderRadius: borderRadius.full,
        padding: 2,
        borderWidth: 1,
        borderColor: colors.border,
        height: 32,
      }}>
      <Pressable
        onPress={() => setIsMapView(false)}
        style={({pressed}) => ({
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: spacing.sm,
          borderRadius: borderRadius.full,
          backgroundColor: !isMapView ? colors.textPrimary : 'transparent',
          flex: 1,
          justifyContent: 'center',
          transform: [{scale: pressed ? 0.95 : 1}],
        })}>
        <Text
          style={{
            fontSize: typography.fontSize.xs,
            fontWeight: typography.fontWeight.semibold,
            color: !isMapView ? colors.white : colors.textSecondary,
          }}>
          {t('home.list')}
        </Text>
      </Pressable>

      <Pressable
        onPress={() => setIsMapView(true)}
        style={({pressed}) => ({
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: spacing.sm,
          borderRadius: borderRadius.full,
          backgroundColor: isMapView ? colors.textPrimary : 'transparent',
          flex: 1,
          justifyContent: 'center',
          transform: [{scale: pressed ? 0.95 : 1}],
        })}>
        <Text
          style={{
            fontSize: typography.fontSize.xs,
            fontWeight: typography.fontWeight.semibold,
            color: isMapView ? colors.white : colors.textSecondary,
          }}>
          {t('home.map')}
        </Text>
      </Pressable>
    </View>
  );

  return (
    <FadeIn duration={300}>
      <View style={{paddingHorizontal: spacing.md}}>
        {/* Single Row - Compact Design */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: spacing.sm,
          }}>
          {/* View Toggle */}
          <ViewToggle />

          {/* Filter Chips */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{flex: 1}}
            contentContainerStyle={{
              alignItems: 'center',
              paddingRight: spacing.md,
            }}>
            {/* Timeframe Filter */}
            <FilterChip
              label={selectedTimeframe?.name || t('home.today')}
              onPress={handleTimeframePress}
              isSelected={!!selectedTimeframe}
            />

            {/* Event Type Filter */}
            <FilterChip
              label={selectedEventType?.name || t('home.all_events')}
              onPress={handleEventTypePress}
              isSelected={!!selectedEventType}
            />

            {/* Price Filter */}
            <FilterChip label={t('home.free_events')} onPress={() => {}} />

            {/* Distance Filter */}
            <FilterChip label={t('home.nearby')} onPress={() => {}} />

            {/* More Filters Button */}
            <Pressable
              onPress={handleFilterPress}
              style={({pressed}) => ({
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: spacing.sm,
                paddingVertical: spacing.xs,
                borderRadius: borderRadius.full,
                backgroundColor: colors.surface,
                borderWidth: 1,
                borderColor: colors.border,
                minHeight: 32,
                transform: [{scale: pressed ? 0.95 : 1}],
              })}>
              <Text
                style={{
                  fontSize: typography.fontSize.xs,
                  fontWeight: typography.fontWeight.semibold,
                  color: colors.textSecondary,
                }}>
                ⚙️ {t('home.more')}
              </Text>
            </Pressable>
          </ScrollView>
        </View>
      </View>
    </FadeIn>
  );
};

export default ModernFilterButtons;
