import React, {useEffect, useRef} from 'react';
import {Animated, StyleSheet} from 'react-native';
import ModernEventPin from '~assets/icons/ModernEventPin';
import {haptics} from '~Utils/haptics';

interface AnimatedPinProps {
  isSelected: boolean;
  eventType: string;
}

const AnimatedPin: React.FC<AnimatedPinProps> = ({isSelected, eventType}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const bounceAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef<Animated.CompositeAnimation | null>(null);

  useEffect(() => {
    if (isSelected) {
      // Provide haptic feedback when pin is selected
      haptics.mapPinSelect();
      console.log('📍 Pin selected - starting animations');

      // More dramatic bounce animation for better visibility
      Animated.sequence([
        // Initial bounce up - more dramatic
        Animated.timing(bounceAnim, {
          toValue: -15, // Increased from -8 to -15
          duration: 150, // Faster for more impact
          useNativeDriver: true,
        }),
        // Bounce back down with overshoot
        Animated.spring(bounceAnim, {
          toValue: 0,
          tension: 200, // Reduced tension for more bounce
          friction: 6, // Reduced friction for more bounce
          useNativeDriver: true,
        }),
      ]).start();

      // More obvious pulsing animation
      pulseAnim.current = Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.3, // Increased from 1.15 to 1.3 for more visibility
            duration: 800, // Faster pulsing
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1.1, // Increased from 1.05 to 1.1
            duration: 800,
            useNativeDriver: true,
          }),
        ]),
      );
      pulseAnim.current.start();
      console.log('✅ Pin animations started');
    } else {
      console.log('📍 Pin deselected - stopping animations');
      // Stop pulsing and return to default scale
      if (pulseAnim.current) {
        pulseAnim.current.stop();
        pulseAnim.current = null;
      }

      // Smooth deselection animation
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200, // Faster deselection
          useNativeDriver: true,
        }),
        Animated.timing(bounceAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
      console.log('✅ Pin animations stopped');
    }

    // Cleanup on unmount or when isSelected changes
    return () => {
      if (pulseAnim.current) {
        pulseAnim.current.stop();
        pulseAnim.current = null;
      }
    };
  }, [isSelected, scaleAnim, bounceAnim]);

  // Map event types to our modern pin types
  const getEventPinType = (type: string): 'business' | 'influencer' | 'community' | 'pyxi_select' => {
    switch (type) {
      case 'business':
        return 'business';
      case 'influencer':
        return 'influencer';
      case 'community':
        return 'community';
      case 'pyxi_select':
        return 'pyxi_select';
      default:
        return 'community';
    }
  };

  return (
    <Animated.View
      style={[
        styles.iconContainer,
        {
          transform: [{scale: scaleAnim}, {translateY: bounceAnim}],
          // Allow content to overflow the container bounds
          overflow: 'visible',
        },
      ]}>
      <ModernEventPin
        eventType={getEventPinType(eventType)}
        size={35} // Reduced from 50 to 35 (will appear as ~46 with 1.3x map scale)
        isSelected={false} // Pass false to prevent double scaling
        disableInternalScaling={true} // New prop to disable internal scaling
      />
    </Animated.View>
  );
};

export default AnimatedPin;

const styles = StyleSheet.create({
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    // Ensure content can overflow for smooth animations
    overflow: 'visible',
    // Add z-index to ensure pin appears above other elements during animation
    zIndex: 1000,
  },
});
