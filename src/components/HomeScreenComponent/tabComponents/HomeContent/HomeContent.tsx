import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  ActivityIndicator,
  DeviceEventEmitter,
  Image,
  ImageBackground,
  Keyboard,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  InteractionManager,
} from 'react-native';
import Animated from 'react-native-reanimated';
import {useIsFocused, useNavigation} from '@react-navigation/native';

import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import {getListFromPages} from '~components/HomeScreenComponent/helpers/getListFromPages';

import {useSimplifiedMapEvents} from '~hooks/event/useSimplifiedMapEvents';
import {useListViewEvents} from '~hooks/event/useListViewEvents';
import {useHomeStore} from '~providers/home/<USER>';
import {Event} from '~types/api/event';
import {EVENT_AGE_GROUP, ORDER_BY, ORDER_DIR, TABS} from '~types/events';
import {responsive, spacing} from '~constants';
import {getMapStyle} from '~constants/locationMap';
import ModernEventsList from '../components/ModernEventsList';
import {Marker, PROVIDER_GOOGLE, Region} from 'react-native-maps';
// import BottomSheet from '@gorhom/bottom-sheet'; // Removed - using conditional rendering instead
import {NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import {useMapEventPrefetch} from '~hooks/performance/useMapEventPrefetch';
import LikeButton from '~components/LikeButton';
import {useTranslation} from 'react-i18next';
import auth from '@react-native-firebase/auth';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import useUpdateBusiness from '~hooks/business/useUpdateBusiness';
import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
// React 19 compatible ClusteredMapView
import ModernClusteredMapView from '~lib';
import FastImage from 'react-native-fast-image';
import EventDetailsBottomSheet from './EventDetailsBottomSheet';
import {useMapsContext} from '~providers/maps/zustand';
import {useTheme} from '~contexts/ThemeContext';

import ModernEventPin from '~assets/icons/ModernEventPin';
import {
  runOnUIThread,
  safeEmit,
  safeMapAnimation,
  // safeBottomSheetOperation, // Removed - no longer using BottomSheet
  createSafeEventListener,
} from '~Utils/uiThreadSafety';

const haversine = require('haversine');

interface HomeContentInterface {
  scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any;
  selectedTimeframe: {id: number; title: string};
  globalInputValue: string;
  isFree?: boolean;
  isNeighbourhood?: boolean;
  isMapView: boolean;
  setIsMapView: (value: boolean) => void;
}

const HomeContent: React.FC<HomeContentInterface> = ({
  scrollHandler,
  filterEvents,
  selectedTimeframe,
  globalInputValue,
  isFree,
  isNeighbourhood,
  isMapView,
  setIsMapView,
}) => {
  console.log('🏠 [HOME CONTENT] Component is rendering!', {isMapView, isNeighbourhood});
  const {colors, isDarkMode} = useTheme();
  const {radius, isForKids} = useHomeStore();
  const navigation = useNavigation<NavigationProps>();
  // Use Google's built-in light/dark themes via userInterfaceStyle prop

  const styles = useMemo(
    () =>
      StyleSheet.create({
        pinContainer: {
          alignItems: 'center',
          overflow: 'visible',
        },
        pinCircle: {
          width: 40, // Reduced from 60 to 40 (will appear as ~52 with 1.3x scale)
          height: 40, // Reduced from 60 to 40
          borderRadius: 20, // Reduced from 30 to 20
          borderWidth: 2, // Reduced from 3 to 2
          borderColor: colors.black,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.white,
        },
        pinImage: {
          width: 36, // Reduced from 54 to 36 (will appear as ~47 with 1.3x scale)
          height: 36, // Reduced from 54 to 36
          borderRadius: 18, // Reduced from 27 to 18
        },
        pinTriangle: {
          width: 0,
          height: 0,
          borderLeftWidth: 8, // Reduced from 12 to 8 (will appear as ~10 with 1.3x scale)
          borderRightWidth: 8, // Reduced from 12 to 8
          borderTopWidth: 12, // Reduced from 18 to 12 (will appear as ~16 with 1.3x scale)
          borderLeftColor: 'transparent',
          borderRightColor: 'transparent',
          borderTopColor: colors.black,
          marginTop: -3,
        },

        iconContainer: {
          zIndex: 100,
          backgroundColor: colors.overlayBackground,
          width: 35,
          height: 35,
          borderRadius: 35,
          position: 'absolute',
          top: 5,
          right: 10,
          flexDirection: 'row',
          gap: 10,
        },
        imageEvent: {
          width: 300,
          height: 100,
          borderRadius: 10,
        },
        dateRangeContainer: {
          zIndex: 1,
          position: 'absolute',
          top: 10,
          left: 10,
          backgroundColor: colors.overlayBackground,
          borderRadius: 10,
          padding: 6,
        },
        eventTitle: {
          fontWeight: 'bold',
          color: colors.white,
          fontSize: 12,
          position: 'absolute',
          bottom: 20,
          left: 10,
          zIndex: 100,
        },
        eventCard: {
          height: 100,
          width: 300,
          alignSelf: 'center',
          borderRadius: 10,
          zIndex: 1909000,
        },
        calloutWrapper: {
          alignItems: 'center',
        },
        calloutContainer: {
          width: 200,
          backgroundColor: colors.white + 'B3',
          borderRadius: 8,
          paddingHorizontal: 8,
          paddingVertical: 3,
        },
        calloutTitle: {
          fontWeight: 'bold',
          fontSize: 11,
        },
        calloutDescription: {
          fontSize: 11,
          color: 'orange',
          position: 'absolute',
          bottom: 7,
          left: 10,
          zIndex: 100,
        },
        arrow: {
          width: 0,
          height: 0,
          borderLeftWidth: 10,
          borderRightWidth: 10,
          borderTopWidth: 10,
          borderLeftColor: 'transparent',
          borderRightColor: 'transparent',
          alignSelf: 'center',
          borderTopColor: colors.white + '4D',
          marginTop: -1,
        },
      }),
    [colors],
  );
  const {t} = useTranslation();
  const {data: businessAccountData} = useGetBusinessAccount(auth().currentUser!.uid);
  const {getCurrentPosition} = useGetCurrentPosition();

  // Get current position state first
  const {currentPositionState, setCurrentPositionState} = useMapsContext();

  // Request location permissions when map view is shown (only once)
  useEffect(() => {
    if (isMapView && !currentPositionState) {
      console.log('🗺️ [MAP] Map view is active, requesting location permissions');
      // Only request location permissions if we don't have position yet
      getCurrentPosition(true, false);
    }
  }, [isMapView]); // Remove getCurrentPosition and currentPositionState from dependencies to prevent infinite loop

  // Use simplified map events with coordinate-based caching for map view
  const {
    data: mapData,
    isLoading: isMapLoading,
    isFetching: isMapFetching,
    fetchNextPage: mapFetchNextPage,
    hasNextPage: mapHasNextPage,
    isFetchingNextPage: isMapFetchingNextPage,
    refetch: mapRefetch,
    remove: mapRemove,
    updateMapViewport,
    currentRadius: mapCurrentRadius,
    clearCache: clearMapCache,
    getCacheStats: getMapCacheStats,
  } = useSimplifiedMapEvents({
    radius,
    isForKids,
    selectedTimeframe,
    globalInputValue,
    isNeighbourhood: isNeighbourhood || false,
    userLocation: currentPositionState,
    enabled: isMapView && !!currentPositionState, // Only fetch when in map view AND have location
  });

  // Use list view events with dynamic radius expansion for list view
  const {
    data: listData,
    isLoading: isListLoading,
    isFetching: isListFetching,
    fetchNextPage: listFetchNextPage,
    hasNextPage: listHasNextPage,
    isFetchingNextPage: isListFetchingNextPage,
    refetch: listRefetch,
    remove: listRemove,
    currentRadius,
    expandRadius,
    resetRadius,
    isExpanding,
    eventCount,
    canLoadMore,
    clearCache: clearListCache,
    getCacheStats: getListCacheStats,
  } = useListViewEvents({
    initialRadius: radius,
    isForKids,
    selectedTimeframe,
    globalInputValue,
    isNeighbourhood: isNeighbourhood || false,
    userLocation: currentPositionState,
    enabled: !isMapView && !!currentPositionState, // Only fetch when in list view AND have location
  });

  // Note: Regular events hook removed - now using specialized hooks for map and list views

  // Use appropriate data source based on view mode
  const data = isMapView ? mapData : listData;
  const isLoading = isMapView ? isMapLoading : isListLoading;
  const isFetching = isMapView ? isMapFetching : isListFetching;
  const fetchNextPage = isMapView ? mapFetchNextPage : listFetchNextPage;
  const hasNextPage = isMapView ? mapHasNextPage : listHasNextPage;
  const isFetchingNextPage = isMapView ? isMapFetchingNextPage : isListFetchingNextPage;
  const refetch = isMapView ? mapRefetch : listRefetch;
  const remove = isMapView ? mapRemove : listRemove;

  const {top} = useSafeAreaInsets();
  const {width, height} = useSafeAreaFrame();
  const isFocused = useIsFocused();
  // const sheetRef = useRef<BottomSheet>(null); // Removed - no longer using BottomSheet
  // Removed BottomSheet snap points - using direct view switching instead
  // const snapPoints = useMemo(() => ['1%', '100%'], []);
  // const [currentPosition, setCurrentPosition] = useState(0);
  // Extract events from paginated data with proper handling of API response structure
  const eventsList = useMemo(() => {
    if (!data?.pages) {
      return [];
    }

    const events = data.pages.flatMap(page => {
      // Handle different response structures
      if (page?.items && Array.isArray(page.items)) {
        return page.items;
      } else if (page?.data && Array.isArray(page.data)) {
        return page.data;
      } else if (Array.isArray(page)) {
        return page;
      }
      return [];
    });

    return events;
  }, [data, isMapView]);

  const [refreshing, setRefreshing] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const mapRef = useRef<any>(null);
  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {mutateAsync: updateBusinessMutation} = useUpdateBusiness();
  const [prevRegion, setPrevRegion] = useState<Region | null>(null);
  const lastRegionRef = useRef<Region | null>(null); // Store the last region to compare

  // Helper function to check if two regions are effectively the same
  const areRegionsEqual = (region1: Region | null, region2: Region | null): boolean => {
    if (!region1 || !region2) {
      return false;
    }
    const latDiff = Math.abs(region1.latitude - region2.latitude);
    const lonDiff = Math.abs(region1.longitude - region2.longitude);
    return latDiff < 0.0001 && lonDiff < 0.0001; // Adjust threshold as needed
  };

  // Update map only when currentPositionState changes significantly and map is visible
  useEffect(() => {
    if (isMapView && currentPositionState && currentPositionState.latitude && mapRef.current) {
      const newRegion: Region = {
        latitude: currentPositionState.latitude,
        longitude: currentPositionState.longitude,
        latitudeDelta: 0.015,
        longitudeDelta: 0.015,
      };

      // Only update if the new region is different from the last known region
      if (!areRegionsEqual(newRegion, lastRegionRef.current)) {
        console.log('🗺️ [MAP] Centering map on user location:', currentPositionState);
        // Ensure map update happens on UI thread - no animation for instant loading
        safeMapAnimation(mapRef, map => {
          map.animateToRegion(newRegion, 0); // Instant update with no animation
          lastRegionRef.current = newRegion; // Update last known region
        });
      }
    }
  }, [isMapView, currentPositionState]);

  /*  useEffect(() => {
    if (isNeighbourhood && eventsList && eventsList.length > 0 && mapRef.current) {
      const event = eventsList[eventsList.length - 1]; // Get the last event
      const newRegion: Region = {
        latitude: event.coords.lat,
        longitude: event.coords.long,
        latitudeDelta: 0.015,
        longitudeDelta: 0.015,
      };

      // Only animate if the new region is different from the last known region
      if (!areRegionsEqual(newRegion, lastRegionRef.current)) {
        mapRef.current.animateToRegion(newRegion);
        lastRegionRef.current = newRegion; // Update last known region
      }
    }
  }, [eventsList, isNeighbourhood]) */

  useEffect(() => {
    if (eventsList && globalInputValue) {
      safeEmit('onSearchResult', {events: eventsList});
    }
  }, [eventsList, globalInputValue]);

  useEffect(() => {
    remove();
    refetch();
    const listener = DeviceEventEmitter.addListener('eventSelection', data => {
      runOnUIThread(() => {
        setSelectedEvent(data.event);
      });
    });

    return () => {
      listener.remove();
    };
  }, [globalInputValue]); // Remove refetch and remove from dependencies to prevent infinite loop

  useEffect(() => {
    // Always request location on mount to have it ready for map view
    if (!currentPositionState) {
      getCurrentPosition(true, false);
    }
  }, []);

  useEffect(() => {
    const eventListener = DeviceEventEmitter.addListener('homeLocationChange', data => {
      runOnUIThread(() => {
        // Removed BottomSheet operation - no longer needed
        // safeBottomSheetOperation(sheetRef, sheet => {
        //   sheet.snapToIndex(0);
        // });

        if (data.lat && data.long) {
          const newRegion: Region = {
            latitude: data.lat,
            longitude: data.long,
            latitudeDelta: 0.015,
            longitudeDelta: 0.015,
          };
          if (!areRegionsEqual(newRegion, lastRegionRef.current)) {
            safeMapAnimation(mapRef, map => {
              map.animateToRegion(newRegion);
              lastRegionRef.current = newRegion;
            });
          }
        }
      });
    });

    return () => {
      eventListener.remove();
      // Cleanup throttle timeout on unmount
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      runOnUIThread(() => {
        setSelectedEvent(null);
      });
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {});

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  useEffect(() => {
    remove();
    refetch();
  }, [radius, isForKids, selectedTimeframe, isNeighbourhood]); // Remove refetch and remove from dependencies to prevent infinite loop

  // Removed BottomSheet position control - using direct view switching instead
  // useEffect(() => {
  //   const targetIndex = isMapView ? 0 : 1;
  //   if (currentPosition !== targetIndex) {
  //     safeBottomSheetOperation(sheetRef, sheet => {
  //       sheet.snapToIndex(targetIndex);
  //     });
  //   }
  // }, [isMapView, currentPosition]);

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      console.log(`📄 [HOME CONTENT] Loading more events (${isMapView ? 'map' : 'list'} view)`);
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage, isMapView]);

  // Removed handleSheetChange - no longer using BottomSheet
  // const handleSheetChange = useCallback((index: number) => {
  //   console.log('handleSheetChange', index);
  //   runOnUIThread(() => {
  //     setSelectedEvent(null);
  //     setCurrentPosition(index);
  //   });
  // }, []);

  // Create a stable position reference that only updates on significant changes
  // Use a ref to store the last stable position to prevent unnecessary recalculations
  const lastStablePositionRef = useRef<{latitude: number; longitude: number} | null>(null);

  const stablePosition = useMemo(() => {
    if (!currentPositionState) return null;

    const roundedPosition = {
      latitude: Math.round(currentPositionState.latitude * 100) / 100, // Round to 2 decimal places (~1km precision)
      longitude: Math.round(currentPositionState.longitude * 100) / 100,
    };

    // Only update if the position has changed significantly (more than 5km)
    if (
      !lastStablePositionRef.current ||
      Math.abs(lastStablePositionRef.current.latitude - roundedPosition.latitude) >= 0.05 ||
      Math.abs(lastStablePositionRef.current.longitude - roundedPosition.longitude) >= 0.05
    ) {
      lastStablePositionRef.current = roundedPosition;
    }

    return lastStablePositionRef.current;
  }, [currentPositionState?.latitude, currentPositionState?.longitude]);

  const filteredEvents = useMemo(() => {
    let events = eventsList;

    // Ensure events is always an array
    if (!events || !Array.isArray(events)) {
      return [];
    }

    // Apply time-based filtering first
    if (events && filterEvents) {
      events = filterEvents(events, selectedTimeframe);
      // Ensure filterEvents didn't return null/undefined
      if (!events || !Array.isArray(events)) {
        return [];
      }
    }

    // Then apply free events filter if needed
    if (isFree && events) {
      events = events.filter(event => event.is_paid == false);
    }

    // Apply client-side distance filtering as a safety net (except for neighbourhood events which may have different logic)
    if (events && events.length > 0 && stablePosition && !isNeighbourhood) {
      events = events.filter(event => {
        // Ensure event has valid coordinates
        if (!event.coords || typeof event.coords.lat !== 'number' || typeof event.coords.long !== 'number') {
          console.warn(`⚠️ Event ${event.event_id} has invalid coordinates:`, event.coords);
          return false;
        }

        const distance = haversine(
          {latitude: stablePosition.latitude, longitude: stablePosition.longitude},
          {latitude: event.coords.lat, longitude: event.coords.long},
          {unit: 'km'},
        );

        return distance <= radius;
      });
    }

    return events;
  }, [eventsList, isFree, filterEvents, selectedTimeframe, isNeighbourhood, radius, stablePosition]);

  // Map event prefetching - prefetch when 10 or fewer events
  const {prefetchEvent, isPrefetched, getPrefetchStats} = useMapEventPrefetch({
    events: filteredEvents || [],
    enabled: isMapView && (filteredEvents?.length || 0) <= 10,
    maxEventsToPrefetch: 10,
    prefetchDelay: 1000,
    prefetchStrategy: 'delayed',
  });

  // Memoize the markers to prevent unnecessary re-renders
  const memoizedMarkers = useMemo(() => {
    if (!filteredEvents || !isMapView) return [];

    return filteredEvents.map(event => {
      const pinScaleFactor = 1; // Reduced from 2.2 to 1.4 (will appear as ~1.8 with 1.3x map scale)

      return (
        <Marker
          key={`marker-${event.event_id}`} // Stable key based only on event ID for better performance
          coordinate={{
            latitude: event.coords.lat,
            longitude: event.coords.long,
          }}
          onPress={() => {
            runOnUIThread(() => {
              Keyboard.dismiss();
              setSelectedEvent(event);

              // Trigger immediate prefetch for this event if not already prefetched
              if (!isPrefetched(event.event_id)) {
                prefetchEvent(event.event_id);
              }
            });
          }}
          tracksViewChanges={false} // Optimize performance
          style={{
            overflow: 'visible', // Ensure larger pins aren't clipped
            zIndex: 1000, // Ensure pin renders above other elements
          }}>
          {event.event_type === 'influencer' ? (
            <View style={styles.pinContainer}>
              <View style={styles.pinCircle}>
                <FastImage source={{uri: event.host_photo}} style={styles.pinImage} />
              </View>
              <View style={styles.pinTriangle} />
            </View>
          ) : (
            <ModernEventPin
              eventType={(event.event_type as 'business' | 'influencer' | 'community' | 'pyxi_select') || 'community'}
              isSelected={selectedEvent?.event_id === event.event_id}
              scaleFactor={pinScaleFactor}
            />
          )}
        </Marker>
      );
    });
  }, [filteredEvents, isMapView, selectedEvent, isPrefetched, prefetchEvent, styles]);

  useEffect(() => {
    if (isMapView && filteredEvents) {
      console.log(`🗺️ [PIN RENDER] Rendering ${filteredEvents.length} pins on map`);
    }
  }, [filteredEvents, isMapView]);

  const handleLocationChange = useCallback(
    async (location: Region | null) => {
      if (!location) {
        return;
      }

      try {
        // Emit loading start event for search bar - ensure it's on UI thread
        safeEmit('mapLoadingStart');

        if (businessAccountData?.uid) {
          const payload = {uid: auth().currentUser!.uid, coords: {lat: location.latitude, long: location.longitude}};
          await updateBusinessMutation(payload);
          return;
        }

        await updateUserMutation({
          coords_real: {lat: location.latitude, long: location.longitude},
        });

        // Ensure refetch happens on UI thread
        runOnUIThread(() => {
          refetch();
        });
      } catch (error) {
        console.log(error);
      }
    },
    [businessAccountData, updateBusinessMutation, updateUserMutation, refetch],
  );

  // Add throttling to prevent excessive region change updates
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleRegionChangeComplete = useCallback(
    (newRegion: Region) => {
      // Store the exact region from onRegionChangeComplete
      lastRegionRef.current = newRegion;

      // Clear any existing timeout
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }

      // Throttle the actual state updates to prevent excessive re-renders - reduced delay for smoother pin loading
      throttleTimeoutRef.current = setTimeout(() => {
        // Ensure UI updates happen on main thread
        runOnUIThread(() => {
          Keyboard.dismiss();
          safeEmit('hideSuggestion');

          // Update state only if the region has changed significantly
          if (!areRegionsEqual(newRegion, prevRegion)) {
            // Update viewport for event fetching when in map view
            if (isMapView && updateMapViewport) {
              updateMapViewport(newRegion);
            }

            // Only update position state if it's a significant change (>500m)
            const currentDistance = prevRegion
              ? haversine(
                  {latitude: prevRegion.latitude, longitude: prevRegion.longitude},
                  {latitude: newRegion.latitude, longitude: newRegion.longitude},
                  {unit: 'km'},
                )
              : Infinity;

            if (currentDistance >= 0.5 || !prevRegion) {
              setCurrentPositionState({
                latitude: newRegion.latitude,
                longitude: newRegion.longitude,
                address: '',
              });
            }

            // Only update user's permanent location for significant moves (>= 2km)
            if (currentDistance >= 2 || !prevRegion) {
              handleLocationChange(newRegion);
              setPrevRegion(newRegion);
            }
          }
        });
      }, 150); // 150ms throttle - reduced for smoother pin loading
    },
    [prevRegion, handleLocationChange, setCurrentPositionState, isMapView, updateMapViewport],
  );

  useEffect(() => {
    safeEmit('eventSelection', {event: selectedEvent});
  }, [selectedEvent]);

  // Emit loading end event when loading states change
  useEffect(() => {
    if (!isLoading && !isFetching) {
      safeEmit('mapLoadingEnd');
    }
  }, [isLoading, isFetching]);

  return (
    <View style={{flex: 1, overflow: 'visible'}}>
      <View style={{flex: 1, marginTop: Platform.OS === 'ios' ? 65 + top : 75 + top, overflow: 'visible'}}>
        {isMapView && (
          <View
            style={{
              flex: 1,
              overflow: 'visible',
              transform: [{scale: 2.0}], // Increased to 2x scaling for maximum size
              // Adjust margins to compensate for 2x scaling
              marginTop: -90,
              marginBottom: -100,
              marginLeft: -100,
              marginRight: -100,
            }}>
            {/* Only render map when we have user location to prevent wrong initial location */}
            {currentPositionState ? (
              <ModernClusteredMapView
                ref={mapRef}
                style={{
                  flex: 1,
                  overflow: 'visible', // Prevent cluster clipping at map level
                }}
                provider={PROVIDER_GOOGLE}
                userInterfaceStyle={isDarkMode ? 'dark' : 'light'}
                customMapStyle={getMapStyle()}
                showsUserLocation={true}
                showsMyLocationButton={true}
                followsUserLocation={false}
                showsCompass={false}
                toolbarEnabled={false}
                showsBuildings={false}
                showsIndoors={false}
                initialRegion={{
                  latitude: currentPositionState.latitude,
                  longitude: currentPositionState.longitude,
                  latitudeDelta: 0.015,
                  longitudeDelta: 0.015,
                }}
              clustering={{
                enabled: true,
                radius: 30, // Reduced from 40 to 30 for less aggressive clustering
                maxZoom: 22, // Increased to allow individual pins at higher zoom levels
                minZoom: 1,
                minPoints: 4, // Increased from 3 to 4 - require more pins to cluster
                extent: 512, // Ensure proper clustering extent
                nodeSize: 50, // Reduced from 60 to 50 for tighter clustering
              }}
              clusterStyle={{
                color: colors.eventInfluencer,
                textColor: 'white',
                borderWidth: 0, // Remove border to prevent clipping
                borderColor: 'transparent',
              }}
              animation={{
                enabled: true,
                duration: 150, // Faster animation for smoother pin loading
                useNativeDriver: true,
              }}
              enableViewportCulling={true}
              maxMarkersToRender={1000}
              onPress={() => {
                runOnUIThread(() => {
                  Keyboard.dismiss();
                  safeEmit('hideSuggestion');
                });
              }}
              onClusterPress={(_event: any) => {
                // Handle cluster press
              }}
              onRegionChangeComplete={handleRegionChangeComplete}>
              {memoizedMarkers}
            </ModernClusteredMapView>
            ) : (
              // Render map with default location immediately, will update when user location is available
              <ModernClusteredMapView
                ref={mapRef}
                style={{
                  flex: 1,
                  overflow: 'visible', // Prevent cluster clipping at map level
                }}
                provider={PROVIDER_GOOGLE}
                userInterfaceStyle={isDarkMode ? 'dark' : 'light'}
                customMapStyle={getMapStyle()}
                showsUserLocation={true}
                showsMyLocationButton={true}
                followsUserLocation={false}
                showsCompass={false}
                toolbarEnabled={false}
                showsBuildings={false}
                showsIndoors={false}
                initialRegion={{
                  latitude: 37.7749, // Default to San Francisco
                  longitude: -122.4194,
                  latitudeDelta: 0.015,
                  longitudeDelta: 0.015,
                }}
                clustering={{
                  enabled: true,
                  radius: 30, // Reduced from 40 to 30 for less aggressive clustering
                  maxZoom: 22, // Increased to allow individual pins at higher zoom levels
                  minZoom: 1,
                  minPoints: 4, // Increased from 3 to 4 - require more pins to cluster
                  extent: 512, // Ensure proper clustering extent
                  nodeSize: 50, // Reduced from 60 to 50 for tighter clustering
                }}
                clusterStyle={{
                  color: colors.eventInfluencer,
                  textColor: 'white',
                  borderWidth: 0, // Remove border to prevent clipping
                  borderColor: 'transparent',
                }}
                animation={{
                  enabled: true,
                  duration: 150, // Faster animation for smoother pin loading
                  useNativeDriver: true,
                }}
                enableViewportCulling={true}
                maxMarkersToRender={1000}
                onPress={() => {
                  runOnUIThread(() => {
                    Keyboard.dismiss();
                    safeEmit('hideSuggestion');
                  });
                }}
                onClusterPress={(_event: any) => {
                  // Handle cluster press
                }}
                onRegionChangeComplete={handleRegionChangeComplete}>
                {memoizedMarkers}
              </ModernClusteredMapView>
            )}
          </View>
        )}

        {/* Conditional rendering instead of BottomSheet */}
        {!isMapView && (
          <View style={{flex: 1, backgroundColor: colors.background}}>
            <ModernEventsList
              data={filteredEvents}
              isLoading={isLoading}
              isFetching={isFetching}
              isFetchingNextPage={isFetchingNextPage}
              refreshing={refreshing}
              onRefresh={() => refetch()}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              isMyEvents={false}
              emptyStateTitle="No events found"
              emptyStateSubtitle="Try adjusting your search or location to find events near you!"
              estimatedItemSize={280}
              contentContainerStyle={{
                paddingTop: spacing.sm,
                paddingBottom: responsive.getValue({xs: 80, sm: 100, md: 120}, 100),
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
};

export default HomeContent;
