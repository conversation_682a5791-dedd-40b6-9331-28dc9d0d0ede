import {useNavigation} from '@react-navigation/native';
import moment from 'moment-timezone';
import {Pressable, View, Text, Image, StyleSheet} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {FadeInLeft, FadeInUp, FadeOutDown} from 'react-native-reanimated';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {NavigationProps} from '~types/navigation/navigation.type';
import {Event} from '~types/api/event';
import LikeButton from '~components/LikeButton';
import {CheckIcon} from '~assets/icons';
import {getMessageOfRecurrence} from '~Utils/event';
import ModernCard from '~components/ModernCard/ModernCard';
import {spacing, borderRadius, typography, shadows, responsive} from '~constants';
import {AnimatedPressable, FadeIn} from '~components/MicroInteractions/MicroInteractions';
import {accessibility} from '~Utils/accessibility';
import {haptics} from '~Utils/haptics';
import {useTheme} from '~contexts/ThemeContext';
import styles from '~components/HomeScreenComponent/styles';

const DefaultItem = ({
  item,
  i,
  isMyEvents,
  disableAnimation,
}: {
  item: Event;
  i: number;
  isMyEvents?: boolean;
  disableAnimation?: boolean;
}) => {
  const {colors} = useTheme();
  const navigation = useNavigation<NavigationProps>();

  // Modern responsive styles using the design system
  const modernStyles = StyleSheet.create({
    cardContainer: {
      position: 'relative',
    },
    container: {
      overflow: 'hidden',
    },
    imageWrapper: {
      width: '100%',
      height: responsive.getValue({xs: 180, sm: 200, md: 220, lg: 240}, 200),
      position: 'relative',
      borderRadius: borderRadius.xl,
      overflow: 'hidden',
    },
    image: {
      width: '100%',
      height: '100%',
      borderRadius: borderRadius.xl,
    },
    dateRangeContainer: {
      position: 'absolute',
      top: spacing.sm,
      left: spacing.sm,
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.xs,
    },
    dateTextBg: {
      backgroundColor: colors.overlayBackground,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.md,
      backdropFilter: 'blur(10px)',
    },
    dateText: {
      color: colors.white,
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.semibold,
      lineHeight: typography.fontSize.xs * typography.lineHeight.tight,
    },
    selectIcon: {
      width: 28,
      height: 28,
      borderRadius: borderRadius.full,
    },
    neighbourHoodEvent: {
      position: 'absolute',
      top: spacing.xl + spacing.sm,
      left: spacing.sm,
      backgroundColor: colors.eventInfluencer + 'E6',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.md,
      backdropFilter: 'blur(10px)',
    },
    neighbourHoodText: {
      color: colors.white,
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.semibold,
      lineHeight: typography.fontSize.xs * typography.lineHeight.tight,
    },
    contentContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: colors.overlayBackground,
      padding: responsive.getValue({xs: spacing.md, sm: spacing.lg, md: spacing.xl}, spacing.lg),
      borderBottomLeftRadius: borderRadius.xl,
      borderBottomRightRadius: borderRadius.xl,
      backdropFilter: 'blur(10px)',
    },
    textContainer: {
      flex: 1,
      gap: spacing.xs,
    },
    name: {
      color: colors.white,
      fontSize: responsive.getValue(
        {xs: typography.fontSize.lg, sm: typography.fontSize.xl, md: typography.fontSize['2xl']},
        typography.fontSize.xl,
      ),
      fontWeight: typography.fontWeight.bold,
      lineHeight: responsive.getValue(
        {
          xs: typography.fontSize.lg * typography.lineHeight.tight,
          sm: typography.fontSize.xl * typography.lineHeight.tight,
          md: typography.fontSize['2xl'] * typography.lineHeight.tight,
        },
        typography.fontSize.xl * typography.lineHeight.tight,
      ),
      textShadowColor: colors.overlayBackground,
      textShadowOffset: {width: 0, height: 1},
      textShadowRadius: 2,
    },
    bottomTextContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.xs,
    },
    addressName: {
      color: colors.gray200,
      fontSize: responsive.getValue(
        {xs: typography.fontSize.sm, sm: typography.fontSize.base, md: typography.fontSize.lg},
        typography.fontSize.base,
      ),
      fontWeight: typography.fontWeight.medium,
      lineHeight: responsive.getValue(
        {
          xs: typography.fontSize.sm * typography.lineHeight.normal,
          sm: typography.fontSize.base * typography.lineHeight.normal,
          md: typography.fontSize.lg * typography.lineHeight.normal,
        },
        typography.fontSize.base * typography.lineHeight.normal,
      ),
      flex: 1,
      textShadowColor: colors.overlayBackground,
      textShadowOffset: {width: 0, height: 1},
      textShadowRadius: 1,
    },
    statusBadge: {
      position: 'absolute',
      bottom: spacing.xl + spacing.sm,
      left: spacing.sm,
      backgroundColor: colors.overlayBackground,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.md,
      gap: spacing.xs,
    },
    pyxiSelectBadge: {
      right: spacing.sm,
      left: undefined,
      bottom: spacing.xl + spacing.lg,
    },
    statusText: {
      color: colors.white,
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.semibold,
      lineHeight: typography.fontSize.xs * typography.lineHeight.tight,
    },
    actionContainer: {
      position: 'absolute',
      top: spacing.md + spacing.sm + spacing.xs, // Account for card margin + padding
      right: spacing.md + spacing.sm + spacing.xs, // Account for card margin + padding
      backgroundColor: colors.overlayBackground,
      borderRadius: borderRadius.full,
      padding: spacing.sm,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      borderWidth: 1,
      borderColor: colors.white + '1A',
      zIndex: 10,
    },
    paidIconContainer: {
      position: 'absolute',
      top: spacing.lg,
      backgroundColor: colors.overlayBackground,
      borderRadius: borderRadius.full,
      padding: spacing.xs,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      borderWidth: 1,
      borderColor: colors.white + '1A',
    },
    paidIcon: {
      width: 20,
      height: 20,
      tintColor: colors.warning,
    },
    redDotImg: {
      position: 'absolute',
      top: spacing.sm,
      right: spacing.sm,
      width: 12,
      height: 12,
      zIndex: 10,
    },
  });
  const {setIsTabBarDisabled} = useTabBar();
  const isPyxiSelectEvent = item.host_id === 'CmCCvWvpRVhhFlHdgU9QE6hbun82';
  const isNeighnourHoodEvent = item.host_id === 'bxcOenW6joNFwgiMBctR0zJT07F2';

  const isEventRunning = () => {
    const now = moment();
    return now.isBetween(moment(item.start_date), moment(item.end_date));
  };

  const handleEventPress = () => {
    haptics.buttonPress();
    setIsTabBarDisabled(true);
    navigation.navigate(SCREENS.HOME_EVENT, {
      tag: `${item.event_id}=tag`,
      eventId: item.event_id,
      item: item,
    });
  };

  const eventDate = `${moment.utc(item.start_date).format('DD MMM YYYY')} - ${moment.utc(item.end_date).format('DD MMM YYYY')}`;
  const accessibilityProps = accessibility.eventCardProps(
    item.name,
    eventDate,
    item.address_name,
    item.total_attendee_count,
  );

  return (
    <FadeIn delay={i * 100} duration={300}>
      <View style={modernStyles.cardContainer}>
        <AnimatedPressable
          onPress={handleEventPress}
          scaleValue={0.98}
          hapticFeedback={false} // We handle haptics manually
        >
          <ModernCard
            variant="elevated"
            padding="xs"
            margin={responsive.getValue({xs: 'sm', sm: 'md', md: 'lg'}, 'md')}
            style={modernStyles.container}
            {...accessibilityProps}>
            <View style={modernStyles.imageWrapper}>
              <FastImage
                style={modernStyles.image}
                source={{
                  uri: item.image_url,
                  priority: 'high',
                }}
              />

              {/* Modern date range badge */}
              <View style={modernStyles.dateRangeContainer}>
                <View style={modernStyles.dateTextBg}>
                  <Text style={modernStyles.dateText}>
                    {`${moment.utc(item.start_date).format('DD MMM YYYY')} - ${moment.utc(item.end_date).format('DD MMM YYYY')}`}
                  </Text>
                </View>
              </View>

        {/* Діапазон дат у верхній частині */}
        <View style={styles.dateRangeContainer}>
          <View style={styles.dateTextBg}>
            <Text
              style={{
                color: '#ffff',
                fontSize: 13,
                fontWeight: 'bold',
              }}>
              {`${moment.utc(item.start_date).format('DD MMM YYYY')} - ${moment.utc(item.end_date).format('DD MMM YYYY')}`}
            </Text>
          </View>
        </View>
        {isNeighnourHoodEvent && (
          <View style={[styles.neighbourHoodEvent, {top: item.user_event ? 80 : 43}]}>
            <Text
              style={{
                color: '#ffff',
                fontSize: 13,
                fontWeight: 'bold',
              }}>
              {`Neighbourhood Event`}
            </Text>
          </View>
        )}

              {item.user_event && (
                <View style={modernStyles.statusBadge}>
                  <Text style={modernStyles.statusText}>Going</Text>
                  <CheckIcon color={colors.success} height={16} width={16} />
                </View>
              )}

        {isPyxiSelectEvent && (
          <View style={[styles.goingIconContainer, {right: 10, left: undefined, top: 50, paddingHorizontal: 0, paddingRight: 0, height: 25}]}>
            {isPyxiSelectEvent && (
              <Image source={require('../../../../../assets/icons/pyxiSelectLogo.png')} resizeMode={'cover'} style={styles.selectIcon} />
            )}
          </View>
        )}

              {item.is_paid && (
                <View
                  style={[modernStyles.paidIconContainer, {right: isMyEvents ? spacing.sm : spacing.xl + spacing.lg}]}>
                  <FastImage
                    style={modernStyles.paidIcon}
                    source={require('../../../../../assets/images/dollar.png')}
                  />
                </View>
              )}

              <View style={modernStyles.contentContainer}>
                <View style={modernStyles.textContainer}>
                  <Text style={modernStyles.name} numberOfLines={2}>
                    {item.name}
                  </Text>

                  <View style={modernStyles.bottomTextContainer}>
                    <Text style={modernStyles.addressName} numberOfLines={2}>
                      {item.address_name}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
            {isEventRunning() && (
              <Image
                style={modernStyles.redDotImg}
                resizeMode={'contain'}
                source={require('../../../../../assets/images/red-dot.png')}
              />
            )}
            {false && (
              <View style={{backgroundColor: colors.primary + '22', borderRadius: 10}}>
                {item.event_group && (
                  <Text style={{fontSize: 16, fontWeight: 'bold', paddingVertical: 10, paddingHorizontal: 10}}>
                    {getMessageOfRecurrence(item.event_group)}
                  </Text>
                )}
              </View>
            )}
          </ModernCard>
        </AnimatedPressable>

        {/* Action buttons outside clickable area */}
        {!isMyEvents && (
          <View style={modernStyles.actionContainer}>
            <LikeButton liked={item.user_liked} eventId={item.event_id} />
          </View>
        )}
      </View>
      {isEventRunning() && false && (
        <Image
          style={styles.redDotImg}
          resizeMode={'contain'}
          source={require('../../../../../assets/images/red-dot.png')}
        />
      )}
      {false && (
        <View style={{backgroundColor: '#FF950022', borderRadius: 10}}>
          {item.event_group && (
            <Text style={{fontSize: 16, fontWeight: 'bold', paddingVertical: 10, paddingHorizontal: 10}}>
              {getMessageOfRecurrence(item.event_group)}
            </Text>
          )}
        </View>
      )}
    </FadeIn>
  );
};

export default DefaultItem;
