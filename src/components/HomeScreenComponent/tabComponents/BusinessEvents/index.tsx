import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  Text,
  View,
  TouchableOpacity,
  DeviceEventEmitter,
  Keyboard,
} from 'react-native';
import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import {getListFromPages} from '~components/HomeScreenComponent/helpers/getListFromPages';
import {useAllEvents} from '~hooks/event/useAllEvents';
import {useHomeStore} from '~providers/home/<USER>';
import {Event} from '~types/api/event';
import {EVENTS_TIMEFRAME, EVENT_AGE_GROUP, ORDER_BY, ORDER_DIR, TABS} from '~types/events';
import {spacing} from '~constants';
import ModernEventsList from '../components/ModernEventsList';
import {useTheme} from '~contexts/ThemeContext';
import MapView, {Marker, PROVIDER_GOOGLE, Region} from 'react-native-maps';
import BottomSheet from '@gorhom/bottom-sheet';

import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
import AnimatedPin from '../HomeContent/AnimatedPin';
import EventDetailsBottomSheet from '../HomeContent/EventDetailsBottomSheet';
import {useMapEventPrefetch} from '~hooks/performance/useMapEventPrefetch';

interface BusinessEventsProps {
  scrollHandler: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  filterEvents: (data: any, selectedTimeframe: {id: number; title: string}) => any;
  selectedTimeframe: {id: number; title: string};
  globalInputValue: string;
  isMapView?: boolean;
  setIsMapView?: (value: boolean) => void;
}

const BusinessEvents: React.FC<BusinessEventsProps> = ({
  scrollHandler,
  filterEvents,
  selectedTimeframe,
  globalInputValue,
  isMapView,
  setIsMapView,
}) => {
  const {width, height} = useSafeAreaFrame();
  const {radius, isForKids} = useHomeStore();
  const {t} = useTranslation();
  const {colors} = useTheme();
  const {data, isLoading, isFetching, fetchNextPage, hasNextPage, isFetchingNextPage, refetch} = useAllEvents({
    distance_km: radius,
    tab: TABS.SUGGESTIONS,
    order_by: ORDER_BY.START_DATE,
    order_dir: ORDER_DIR.ASC,
    event_age_group: isForKids ? EVENT_AGE_GROUP.CHILDREN : null,
    filter_type: selectedTimeframe.title === t('home.upcoming_events') ? undefined : selectedTimeframe.title,
  });
  const {top} = useSafeAreaInsets();

  // Map-related state and refs
  const sheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ['1%', '100%'], []);
  const [currentPosition, setCurrentPosition] = useState(0);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const mapRef = useRef<MapView>(null);
  const [currentLocation, setCurrentLocation] = useState({latitude: 51.5072, longitude: 0.1276});
  const {getCurrentPosition, currentPosition: currentPositionState} = useGetCurrentPosition();

  const eventsList = getListFromPages<Event>(data);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Emit loading start event for search bar
    DeviceEventEmitter.emit('mapLoadingStart');
    refetch();
  }, [radius, isForKids, refetch, selectedTimeframe]);

  // Emit loading end event when loading states change
  useEffect(() => {
    if (!isLoading && !isFetching) {
      DeviceEventEmitter.emit('mapLoadingEnd');
    }
  }, [isLoading, isFetching]);

  // Map-related effects
  useEffect(() => {
    getCurrentPosition();
  }, []);

  useEffect(() => {
    DeviceEventEmitter.addListener('eventSelection', data => {
      setSelectedEvent(data.event);
    });

    return () => {
      DeviceEventEmitter.removeAllListeners('eventSelection');
    };
  }, []);

  // Control BottomSheet position when map/list view changes
  useEffect(() => {
    if (sheetRef.current) {
      const targetIndex = isMapView ? 0 : 1;
      if (currentPosition !== targetIndex) {
        sheetRef.current.snapToIndex(targetIndex);
      }
    }
  }, [isMapView, currentPosition]);

  const renderFooter = () => {
    return isFetchingNextPage ? <></> : null;
  };

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleSheetChange = useCallback((index: number) => {
    setSelectedEvent(null);
    setCurrentPosition(index);
  }, []);

  const contentLayout = useMemo(() => {
    const platformLayout = Platform.OS === 'ios' ? 115 : 125;
    return platformLayout + top;
  }, [top]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setTimeout(() => {
      refetch();
      setRefreshing(false);
    }, 2000);
  }, []);

  const filteredEvents = useMemo(() => {
    if (!eventsList) {
      return [];
    }
    let events = eventsList;

    // Apply time-based filtering first
    if (events && filterEvents) {
      events = filterEvents(events, selectedTimeframe);
    }

    // Then apply search filtering if needed
    if (globalInputValue && events) {
      events = events.filter(event => event.name.toLowerCase().includes(globalInputValue.toLowerCase()));
    }

    return events || [];
  }, [eventsList, selectedTimeframe, globalInputValue, filterEvents]);

  // Map event prefetching - prefetch when 10 or fewer events
  const {prefetchEvent, isPrefetched} = useMapEventPrefetch({
    events: filteredEvents || [],
    enabled: isMapView && (filteredEvents?.length || 0) <= 10,
    maxEventsToPrefetch: 10,
    prefetchDelay: 1000,
    prefetchStrategy: 'delayed',
  });

  useEffect(() => {
    if (!isLoading && !isFetching && !isFetchingNextPage && filteredEvents.length * 170 < height) {
      handleLoadMore();
    }
  }, [isLoading, isFetching, isFetchingNextPage, filteredEvents, handleLoadMore, height]);

  if (isLoading || !eventsList?.length) {
    return <View style={{flex: 1, backgroundColor: colors.background}} />;
  }

  return (
    <View style={{flex: 1}}>
      <View style={{flex: 1, marginTop: Platform.OS === 'ios' ? 75 + top : 85 + top}}>
        <View
          style={{
            flex: 1,
            transform: [{scale: 2.0}], // Increased to 2x scaling for maximum size
            // Adjust margins to compensate for 2x scaling
            marginTop: -100,
            marginBottom: -100,
            marginLeft: -100,
            marginRight: -100,
          }}>
          <MapView
            ref={mapRef}
            style={{flex: 1}}
            provider={PROVIDER_GOOGLE}
            showsUserLocation={true}
            showsMyLocationButton={true}
            showsCompass={false}
            toolbarEnabled={false}
            showsBuildings={false}
            showsIndoors={false}
            initialRegion={{
              latitude: currentPositionState?.latitude ?? currentLocation.latitude,
              longitude: currentPositionState?.longitude ?? currentLocation.longitude,
              latitudeDelta: 0.015,
              longitudeDelta: 0.015,
            }}
            onPress={() => {
              Keyboard.dismiss();
              DeviceEventEmitter.emit('hideSuggestion');
            }}>
            {filteredEvents?.map((event, index) => (
              <Marker
                key={`${event.event_id}-${index}`}
                coordinate={{
                  latitude: event.coords.lat,
                  longitude: event.coords.long,
                }}
                onPress={() => {
                  Keyboard.dismiss();
                  setSelectedEvent(event);

                  // Trigger immediate prefetch for this event if not already prefetched
                  if (!isPrefetched(event.event_id)) {
                    prefetchEvent(event.event_id);
                  }
                }}>
                <AnimatedPin isSelected={selectedEvent?.event_id === event.event_id} eventType={event.event_type} />
              </Marker>
            ))}
          </MapView>
        </View>
        <BottomSheet
          ref={sheetRef}
          snapPoints={snapPoints}
          index={isMapView ? 0 : 1}
          enablePanDownToClose={false}
          enableDynamicSizing={false}
          handleComponent={() => null}
          onChange={handleSheetChange}
          handleStyle={{backgroundColor: colors.background, borderRadius: 0}}
          backgroundStyle={{backgroundColor: colors.background, borderRadius: 0}}
          activeOffsetY={[-5, 5]}
          failOffsetX={[-999, 999]}
          enableContentPanningGesture={false}
          enableHandlePanningGesture={false}>
          <ModernEventsList
            data={filteredEvents}
            isLoading={isLoading}
            isFetching={isFetching}
            isFetchingNextPage={isFetchingNextPage}
            refreshing={refreshing}
            onRefresh={onRefresh}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            isMyEvents={false}
            emptyStateTitle={t('home.empty_list_business_events')}
            emptyStateSubtitle="Discover amazing business events and networking opportunities in your area!"
            estimatedItemSize={280}
          />
        </BottomSheet>
      </View>
      {selectedEvent && <EventDetailsBottomSheet event={selectedEvent} />}
    </View>
  );
};

export default BusinessEvents;
