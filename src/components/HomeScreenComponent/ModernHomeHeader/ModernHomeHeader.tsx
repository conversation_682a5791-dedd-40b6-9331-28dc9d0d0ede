import React from 'react';
import {View, Platform} from 'react-native';
import Animated from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from '~contexts/ThemeContext';
import {spacing} from '~constants/design';
import ModernEventSearch from '../ModernEventSearch/ModernEventSearch';
import QuickFilters from '../QuickFilters/QuickFilters';

interface ModernHomeHeaderProps {
  scrollAnimatedStyle: any;
  globalInputValue: string;
  setGlobalInputValue: (value: string) => void;
  selectedTimeframe: any;
  selectedEventType: any;
  isMapView: boolean;
  setIsMapView: (value: boolean) => void;
  onTimeframePress?: () => void;
  onEventTypePress?: () => void;
  onFilterPress?: () => void;
  onQuickFilterSelect?: (timeframe: any) => void;
  isForKids?: boolean;
  radius?: number;
  timeframes?: any[];
  eventTypes?: any[];
}

const ModernHomeHeader: React.FC<ModernHomeHeaderProps> = ({
  scrollAnimatedStyle,
  globalInputValue,
  setGlobalInputValue,
  selectedTimeframe,
  selectedEventType,
  isMapView,
  setIsMapView,
  onTimeframePress,
  onEventTypePress,
  onFilterPress,
  onQuickFilterSelect,
  isForKids,
  radius,
  timeframes,
  eventTypes,
}) => {
  const {colors} = useTheme();
  const {top} = useSafeAreaInsets();

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 300,
          paddingTop: Platform.OS === 'ios' ? top : spacing.md,
          backgroundColor: colors.background,
          // paddingBottom: spacing.md,
          // Add subtle border bottom for better separation
          borderBottomWidth: 1,
          borderBottomColor: colors.border + '20', // 20% opacity
          overflow: 'visible',
        },
        scrollAnimatedStyle,
      ]}>
      {/* Search Section */}
      <ModernEventSearch
        globalInputValue={globalInputValue}
        setGlobalInputValue={setGlobalInputValue}
        onFilterPress={onFilterPress}
        selectedTimeframe={selectedTimeframe}
        selectedEventType={selectedEventType}
        isForKids={isForKids}
        radius={radius}
        timeframes={timeframes}
        eventTypes={eventTypes}
      />

      {/* Reduced spacing between search and filters */}
      <View style={{height: spacing.xs}} />

      {/* Quick Filters Section */}
      <QuickFilters
        selectedTimeframe={selectedTimeframe}
        onTimeframeSelect={onQuickFilterSelect || (() => {})}
        isMapView={isMapView}
        setIsMapView={setIsMapView}
      />
    </Animated.View>
  );
};

export default ModernHomeHeader;
