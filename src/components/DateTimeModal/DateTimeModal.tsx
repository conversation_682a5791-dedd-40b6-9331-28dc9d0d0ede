import OneDatePicker from '../OneDatePicker';
import {View} from 'react-native';
import getStyles from './styles';
import ModalWithButtons from '../ModalWithButtons';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  isVisible: boolean;
  close: () => void;
  onPress: () => void;
  handleChooseMaxDate: (value?: Date) => void;
  date: Date;
  withoutMaxAvailableDate?: boolean;
  mixAvailableDate?: Date;
  maxAvailableDate?: Date;
  mode?: 'date' | 'time' | 'datetime';
}

const DateTimeModal = ({
  isVisible,
  close,
  onPress,
  handleChooseMaxDate: handleChooseDate,
  date,
  withoutMaxAvailableDate,
  mixAvailableDate,
  maxAvailableDate,
  mode,
}: IProps) => {
  const {colors} = useTheme();
  const styles = getStyles(colors);

  return (
    <ModalWithButtons onCloseModal={close} modalIsVisible={isVisible} onPress={onPress} backgroundColor={colors.white}>
      <View style={styles.container}>
        <OneDatePicker
          handleChooseDate={handleChooseDate}
          date={date}
          mixAvailableDate={mixAvailableDate}
          maxAvailableDate={maxAvailableDate}
          withoutMaxAvailableDate={withoutMaxAvailableDate}
          mode={mode}
        />
      </View>
    </ModalWithButtons>
  );
};

export default DateTimeModal;
