import React from 'react';
import {View, StyleSheet} from 'react-native';
import Svg, {Path, Circle, Defs, LinearGradient, Stop} from 'react-native-svg';
import {Event} from '~types/api/event';
import FastImage from 'react-native-fast-image';

interface EventPinProps {
  event: Event;
  size?: number;
  isSelected?: boolean;
}

const EventPin: React.FC<EventPinProps> = ({event, size = 40, isSelected = false}) => {
  const getEventColor = () => {
    // Use consistent purple color for all individual event pins to match the older design
    return '#6B46C1'; // Beautiful purple color from the older design
  };

  const pinColor = getEventColor();
  const scaleFactor = isSelected ? 1.2 : 1;
  const pinSize = size * scaleFactor;

  // For influencer events with host photos, use circular image
  if (event.event_type === 'influencer' && event.host_photo) {
    return (
      <View style={styles.container}>
        <Svg width={pinSize + 10} height={pinSize + 15} viewBox="0 0 50 60" style={{transform: [{scale: scaleFactor}]}}>
          <Defs>
            <LinearGradient id={`gradient-${event.event_id}`} x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor={pinColor} stopOpacity="1" />
              <Stop offset="100%" stopColor={pinColor} stopOpacity="0.8" />
            </LinearGradient>
          </Defs>

          {/* Pin shadow */}
          <Path
            d="M25 5C32.18 5 38 10.82 38 18C38 28 25 45 25 45S12 28 12 18C12 10.82 17.82 5 25 5Z"
            fill="rgba(0,0,0,0.1)"
            transform="translate(1, 2)"
          />

          {/* Main pin body */}
          <Path
            d="M25 5C32.18 5 38 10.82 38 18C38 28 25 45 25 45S12 28 12 18C12 10.82 17.82 5 25 5Z"
            fill={`url(#gradient-${event.event_id})`}
          />

          {/* Inner circle for image */}
          <Circle cx="25" cy="18" r="10" fill="white" stroke="rgba(255,255,255,0.3)" strokeWidth="1" />
        </Svg>

        {/* Host photo overlay */}
        <FastImage
          source={{uri: event.host_photo}}
          style={[
            styles.hostPhoto,
            {
              width: 20,
              height: 20,
              borderRadius: 10,
              position: 'absolute',
              top: 8,
              left: (pinSize + 10) / 2 - 10,
            },
          ]}
        />

        {/* Selection indicator */}
        {isSelected && (
          <View
            style={[
              styles.selectionRing,
              {
                width: pinSize + 20,
                height: pinSize + 20,
                borderRadius: (pinSize + 20) / 2,
                top: -5,
                left: -5,
              },
            ]}
          />
        )}
      </View>
    );
  }

  // Standard pin for other event types
  return (
    <View style={styles.container}>
      <Svg width={pinSize + 10} height={pinSize + 15} viewBox="0 0 50 60" style={{transform: [{scale: scaleFactor}]}}>
        <Defs>
          <LinearGradient id={`gradient-${event.event_id}`} x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={pinColor} stopOpacity="1" />
            <Stop offset="100%" stopColor={pinColor} stopOpacity="0.8" />
          </LinearGradient>
        </Defs>

        {/* Pin shadow */}
        <Path
          d="M25 5C32.18 5 38 10.82 38 18C38 28 25 45 25 45S12 28 12 18C12 10.82 17.82 5 25 5Z"
          fill="rgba(0,0,0,0.1)"
          transform="translate(1, 2)"
        />

        {/* Main pin body */}
        <Path
          d="M25 5C32.18 5 38 10.82 38 18C38 28 25 45 25 45S12 28 12 18C12 10.82 17.82 5 25 5Z"
          fill={`url(#gradient-${event.event_id})`}
        />

        {/* Inner circle for icon */}
        <Circle cx="25" cy="18" r="10" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)" strokeWidth="1" />

        {/* Simple white dot as icon */}
        <Circle cx="25" cy="18" r="4" fill="white" />

        {/* Selection indicator */}
        {isSelected && <Circle cx="25" cy="18" r="12" fill="none" stroke="white" strokeWidth="2" opacity="0.8" />}
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  hostPhoto: {
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.5)',
  },
  selectionRing: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: 'white',
    backgroundColor: 'transparent',
  },
});

export default EventPin;
