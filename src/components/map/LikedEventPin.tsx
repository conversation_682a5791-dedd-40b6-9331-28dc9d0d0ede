import React, {useEffect} from 'react';
import Svg, {Path, Circle, Defs, LinearGradient, Stop} from 'react-native-svg';
import {useTheme} from '~contexts/ThemeContext';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';

interface LikedEventPinProps {
  eventType: 'business' | 'influencer' | 'community' | 'pyxi_select';
  size?: number;
  isSelected?: boolean;
  disableInternalScaling?: boolean;
  onPress?: () => void;
  animateOnMount?: boolean;
  scaleFactor?: number;
  enhancedVisibility?: boolean;
  subtlePulse?: boolean;
}

const LikedEventPin: React.FC<LikedEventPinProps> = ({
  eventType,
  size = 40,
  isSelected = false,
  disableInternalScaling = false,
  onPress,
  animateOnMount = true,
  scaleFactor = 1,
  enhancedVisibility = false,
  subtlePulse = false,
}) => {
  const {colors} = useTheme();

  // Animation values
  const scale = useSharedValue(0);
  const bounce = useSharedValue(1);
  const pulse = useSharedValue(1);
  const breathe = useSharedValue(1);

  // Initialize animations
  useEffect(() => {
    if (animateOnMount) {
      // Entrance animation with enhanced bounce
      scale.value = withSequence(
        withTiming(0.8, {duration: 100}),
        withSpring(1, {
          damping: 8,
          stiffness: 200,
          mass: 0.8,
        }),
      );
    } else {
      scale.value = 1;
    }
  }, [animateOnMount]);

  // Selection animation
  useEffect(() => {
    if (isSelected) {
      bounce.value = withSequence(
        withSpring(1.2, {damping: 10, stiffness: 300}),
        withSpring(1.1, {damping: 8, stiffness: 200}),
      );
    } else {
      bounce.value = withSpring(1, {damping: 12, stiffness: 250});
    }
  }, [isSelected]);

  // Subtle pulse for enhanced visibility
  useEffect(() => {
    if (subtlePulse || enhancedVisibility) {
      pulse.value = withSequence(
        withTiming(1.05, {duration: 1500}),
        withTiming(1, {duration: 1500}),
      );
    }
  }, [subtlePulse, enhancedVisibility]);

  // Breathing animation for better visibility
  useEffect(() => {
    if (enhancedVisibility) {
      const breathingAnimation = () => {
        breathe.value = withSequence(
          withTiming(1.02, {duration: 2000}),
          withTiming(1, {duration: 2000}),
        );
      };
      breathingAnimation();
      const interval = setInterval(breathingAnimation, 4000);
      return () => clearInterval(interval);
    }
  }, [enhancedVisibility]);

  // Animated styles with enhanced visibility
  const animatedStyle = useAnimatedStyle(() => {
    let combinedScale = disableInternalScaling ? 1 : scale.value * bounce.value * pulse.value * breathe.value;

    return {
      transform: [{scale: combinedScale}],
    };
  });

  const AnimatedSvg = Animated.createAnimatedComponent(Svg);

  // Use red color for liked events
  const pinColor = colors.error; // Red color for liked events
  const shadowColor = colors.error + '40'; // Red shadow

  const finalSize = size * scaleFactor;
  const strokeWidth = Math.max(1, finalSize * 0.05);

  return (
    <AnimatedSvg
      width={finalSize}
      height={finalSize * 1.2}
      viewBox={`0 0 ${finalSize} ${finalSize * 1.2}`}
      style={[animatedStyle, {overflow: 'visible'}]}
      onPress={onPress}>
      <Defs>
        {/* Enhanced gradient for liked events */}
        <LinearGradient id="likedPinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor={pinColor} stopOpacity="1" />
          <Stop offset="50%" stopColor={colors.error} stopOpacity="0.9" />
          <Stop offset="100%" stopColor={colors.errorDark || pinColor} stopOpacity="1" />
        </LinearGradient>

        {/* Shadow gradient */}
        <LinearGradient id="likedShadowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <Stop offset="0%" stopColor={shadowColor} stopOpacity="0.3" />
          <Stop offset="100%" stopColor={shadowColor} stopOpacity="0.1" />
        </LinearGradient>
      </Defs>

      {/* Enhanced shadow for better visibility */}
      <Circle
        cx={finalSize / 2}
        cy={finalSize * 0.35}
        r={finalSize * 0.28}
        fill="url(#likedShadowGradient)"
        transform={`translate(2, 4)`}
      />

      {/* Main pin body with heart shape for liked events */}
      <Path
        d={`M${finalSize / 2},${finalSize * 0.85} 
           C${finalSize * 0.2},${finalSize * 0.5} 
           ${finalSize * 0.2},${finalSize * 0.25} 
           ${finalSize / 2},${finalSize * 0.35}
           C${finalSize * 0.8},${finalSize * 0.25} 
           ${finalSize * 0.8},${finalSize * 0.5} 
           ${finalSize / 2},${finalSize * 0.85} Z`}
        fill="url(#likedPinGradient)"
        stroke={colors.white}
        strokeWidth={strokeWidth}
      />

      {/* Heart icon inside the pin */}
      <Path
        d={`M${finalSize / 2},${finalSize * 0.55}
           C${finalSize * 0.35},${finalSize * 0.4}
           ${finalSize * 0.35},${finalSize * 0.3}
           ${finalSize / 2},${finalSize * 0.35}
           C${finalSize * 0.65},${finalSize * 0.3}
           ${finalSize * 0.65},${finalSize * 0.4}
           ${finalSize / 2},${finalSize * 0.55} Z`}
        fill={colors.white}
        opacity="0.9"
      />

      {/* Enhanced highlight for premium feel */}
      <Circle
        cx={finalSize * 0.4}
        cy={finalSize * 0.3}
        r={finalSize * 0.08}
        fill={colors.white}
        opacity="0.6"
      />

      {/* Selection indicator */}
      {isSelected && (
        <Circle
          cx={finalSize / 2}
          cy={finalSize * 0.35}
          r={finalSize * 0.32}
          fill="none"
          stroke={colors.white}
          strokeWidth={strokeWidth * 1.5}
          opacity="0.8"
        />
      )}
    </AnimatedSvg>
  );
};

export default LikedEventPin;
