import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useTheme} from '~contexts/ThemeContext';

interface ClusterMarkerProps {
  count: number;
  onPress?: () => void;
}

// Helper function to determine cluster size based on count (limited to 64x64 with 2x map scale)
const getClusterStyle = (count: number) => {
  if (count >= 50) {
    return {
      width: 32, // Max size 32px (will appear as 64px with 2x scale)
      height: 32,
      borderRadius: 16,
      fontSize: 10,
      color: '#FF4500', // Deep orange-red for highest count
    };
  }
  if (count >= 25) {
    return {
      width: 30, // 30px (will appear as 60px with 2x scale)
      height: 30,
      borderRadius: 15,
      fontSize: 9,
      color: '#FF6B00', // Dark orange
    };
  }
  if (count >= 15) {
    return {
      width: 28, // 28px (will appear as 56px with 2x scale)
      height: 28,
      borderRadius: 14,
      fontSize: 8,
      color: '#FF8C42', // Medium orange
    };
  }
  if (count >= 10) {
    return {
      width: 26, // 26px (will appear as 52px with 2x scale)
      height: 26,
      borderRadius: 13,
      fontSize: 7,
      color: '#FFA366', // Light orange
    };
  }
  if (count >= 8) {
    return {
      width: 24, // 24px (will appear as 48px with 2x scale)
      height: 24,
      borderRadius: 12,
      fontSize: 6,
      color: '#FFB380', // Lighter orange
    };
  }
  if (count >= 4) {
    return {
      width: 22, // 22px (will appear as 44px with 2x scale)
      height: 22,
      borderRadius: 11,
      fontSize: 5,
      color: '#FFC299', // Very light orange
    };
  }
  return {
    width: 20, // 20px (will appear as 40px with 2x scale)
    height: 20,
    borderRadius: 10,
    fontSize: 4,
    color: '#FFD1B3', // Lightest orange for smallest clusters
  };
};

const ClusterMarker: React.FC<ClusterMarkerProps> = ({count, onPress}) => {
  const {colors} = useTheme();
  const clusterStyle = getClusterStyle(count);

  return (
    <View style={[styles.container, {width: clusterStyle.width, height: clusterStyle.width}]}>
      {/* Background circle with opacity - absolutely positioned and centered */}
      <View
        style={[
          styles.backgroundCircle,
          {
            width: clusterStyle.width,
            height: clusterStyle.height,
            borderRadius: clusterStyle.borderRadius,
            backgroundColor: clusterStyle.color, // Dynamic color based on count
          },
        ]}
      />

      {/* Main cluster circle - absolutely positioned and centered */}
      <View
        style={[
          styles.clusterCircle,
          {
            width: clusterStyle.width * 0.76, // Inner circle is ~76% of outer circle
            height: clusterStyle.height * 0.76,
            borderRadius: clusterStyle.borderRadius * 0.76,
            backgroundColor: clusterStyle.color, // Same dynamic color
          },
        ]}>
        <Text
          style={[
            styles.clusterText,
            {
              fontSize: clusterStyle.fontSize,
              color: '#FFFFFF',
            },
          ]}>
          {count > 99 ? '99+' : count}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backgroundCircle: {
    position: 'absolute',
    opacity: 0.5,
    zIndex: 0,
    top: 0,
    left: 0,
  },
  clusterCircle: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    top: '12%', // Center the smaller circle within the larger one
    left: '12%', // 12% = (100% - 76%) / 2
  },
  clusterText: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default ClusterMarker;
