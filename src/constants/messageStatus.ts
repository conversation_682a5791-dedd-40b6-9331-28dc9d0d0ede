/**
 * Message Status Constants
 * 
 * Centralized message status definitions to prevent import issues
 * and ensure consistency across the application.
 */

export const MESSAGE_STATUS = {
  PENDING: 'pending',
  SENDING: 'sending', 
  SENT: 'sent',
  DELIVERED: 'delivered',
  READ: 'read',
  FAILED: 'failed',
} as const;

export type MessageStatus = typeof MESSAGE_STATUS[keyof typeof MESSAGE_STATUS];

// Export individual constants for convenience
export const {
  PENDING,
  SENDING,
  SENT,
  DELIVERED,
  READ,
  FAILED,
} = MESSAGE_STATUS;

// Helper functions for status checking
export const isMessagePending = (status: string): boolean => status === MESSAGE_STATUS.PENDING;
export const isMessageSending = (status: string): boolean => status === MESSAGE_STATUS.SENDING;
export const isMessageSent = (status: string): boolean => status === MESSAGE_STATUS.SENT;
export const isMessageDelivered = (status: string): boolean => status === MESSAGE_STATUS.DELIVERED;
export const isMessageRead = (status: string): boolean => status === MESSAGE_STATUS.READ;
export const isMessageFailed = (status: string): boolean => status === MESSAGE_STATUS.FAILED;

// Status groups for easier checking
export const PENDING_STATUSES = [MESSAGE_STATUS.PENDING, MESSAGE_STATUS.SENDING];
export const SUCCESS_STATUSES = [MESSAGE_STATUS.SENT, MESSAGE_STATUS.DELIVERED, MESSAGE_STATUS.READ];
export const FINAL_STATUSES = [MESSAGE_STATUS.SENT, MESSAGE_STATUS.DELIVERED, MESSAGE_STATUS.READ, MESSAGE_STATUS.FAILED];

export const isPendingStatus = (status: string): boolean => PENDING_STATUSES.includes(status as any);
export const isSuccessStatus = (status: string): boolean => SUCCESS_STATUSES.includes(status as any);
export const isFinalStatus = (status: string): boolean => FINAL_STATUSES.includes(status as any);

export default MESSAGE_STATUS;
