import {BASE_API_URL} from '@env';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {SubCategoryType} from '~types/categories';

export function useGetSubcategories() {
  return useQuery<SubCategoryType[], Error>(
    'allSubCategories', // Query key
    async () => {
      const token = await FirebaseAuth.getAuthToken();
      const baseUrl = BASE_API_URL;
      const url = `${baseUrl}subcategories`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: token,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user children');
      }

      const data = await response.json();

      return data as SubCategoryType[];
    },
  );
}
