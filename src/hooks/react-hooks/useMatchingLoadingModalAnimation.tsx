import {useNavigation} from '@react-navigation/native';
import {useCallback} from 'react';
import {SCREENS} from '~constants';
import {useGetEventById} from '~hooks/event/useGetEventById';
import {useGetMatches} from '~hooks/event/useGetMatches';
import {useMatchingLoaderAnimationStore} from '~providers/matching/zustand';
import {NavigationProps} from '~types/navigation/navigation.type';

const useMatchingLoadingModalAnimation = () => {
  const {
    open,
    close,
    isVisible,
    setMatchingEventId,
    matchingEventId,
    noMatchesButtonIsVisible,
    setNoMatchesButtonIsVisible,
    domain, // Access the new domain property
    setDomain, // Access the setter for domain
    refresh, // Access the refresh flag
    setRefresh, // Access the setter for refresh flag
    matchingType,
    setMatchingType,
  } = useMatchingLoaderAnimationStore();

  const {data, refetch} = useGetMatches(matchingEventId, refresh, domain, matchingType);
  const navigation = useNavigation<NavigationProps>();
  const {
    data: fetchedItem,
    refetch: refetchEvent,
    isLoading: isEventLoading,
  } = useGetEventById(matchingEventId as number);

  const matchingModalCallback = useCallback(async () => {
    const refetchData = await refetch();
    await refetchEvent();
    if (Array.isArray(refetchData.data) && refetchData.data?.length) {
      // Use a timeout to ensure navigation happens after modal state updates
      setTimeout(() => {
        try {
          navigation.navigate(SCREENS.MATCHING_USERS, {
            event: fetchedItem,
          });
          close();
        } catch (error) {
          console.error('Navigation error:', error);
          // Fallback: try navigating to the parent stack first
          try {
            navigation.getParent()?.navigate(SCREENS.MATCHING_USERS, {
              event: fetchedItem,
            });
            close();
          } catch (fallbackError) {
            console.error('Fallback navigation error:', fallbackError);
          }
        }
      }, 100);
      return;
    }

    setNoMatchesButtonIsVisible(true);
  }, [close, data, matchingEventId, navigation, setNoMatchesButtonIsVisible, fetchedItem, refetchEvent, setRefresh]);

  return {
    closeMatchingLoadingModal: close,
    openMatchingLoadingModal: open,
    matchingModalCallback,
    isMatchingLoadingModalVisible: isVisible,
    setCurrentMatchingEvent: setMatchingEventId,
    matchedPeople: data || [],
    onCloseButtonPress: close,
    noMatchesButtonIsVisible,
    domain, // Expose the domain property
    setDomain, // Expose the setter for domain
    refresh, // Expose the refresh flag
    setRefresh, // Expose the setter for refresh flag
    matchingType,
    setMatchingType,
  };
};

export default useMatchingLoadingModalAnimation;
