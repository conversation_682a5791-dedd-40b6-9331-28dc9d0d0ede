import {BASE_API_URL} from '@env';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {USER_EVENT_STATUS} from '~types/api/user';

export type StatusResponseType = {
  order_id: string;
  status: USER_EVENT_STATUS;
};

export function useGetEventUserStatus({event_id, user_id}: {event_id: number; user_id: string}) {
  return useQuery<StatusResponseType | undefined, Error>(['eventUserStatus', event_id], async () => {

    if (!event_id) {
      return undefined;
    }
    const token = await FirebaseAuth.getAuthToken();
        console.log('eventUserStatus', 'datadatadatadatadata', token, `${BASE_API_URL}events/${event_id}/subscriptions/${user_id}/status`);
    const response = await fetch(`${BASE_API_URL}events/${event_id}/subscriptions/${user_id}/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!response.ok) {
    console.log(await response.json(), 'datadatadatadatadata. err');
      return;
    }

    const data = await response.json();
    console.log(data, 'datadatadatadatadata');
    

    return data as StatusResponseType;
  });
}
