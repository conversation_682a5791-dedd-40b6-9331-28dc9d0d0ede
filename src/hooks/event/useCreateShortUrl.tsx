import {useMutation} from 'react-query';
import {BASE_API_URL} from '@env';
import {ShortUrl} from '~types/api/event';
import axios from 'axios';

export const useCreateShortUrl = () => {
  const {mutate, isLoading} = useMutation(async (url: string) => {
    const response = await axios.post(`${BASE_API_URL}shorten/?original_url=${url}`);
    if (!response.data) {
      throw new Error('Failed to create short link');
    }

    const data = await response.data;

    return data as ShortUrl;
  });
  return {
    createShortUrl: mutate,
    isLoading,
  };
};
