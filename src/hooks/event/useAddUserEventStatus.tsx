import {BASE_API_URL} from '@env';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';

interface IAddUserEventStatus {
  event_id: string;
  count?: string;
}
export function useAddUserEventStatus(hideError?: boolean) {
  const queryClient = useQueryClient();

  return useMutation<boolean | undefined, Error, IAddUserEventStatus>(
    async (requestData: IAddUserEventStatus) => {
      const token = await FirebaseAuth.getAuthToken();
      const {event_id, count} = requestData;
      const response = await fetch(`${BASE_API_URL}events/${event_id}/subscriptions?count=${count}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });
      if (!response.ok) {
        try {
          const errorResponse: any = await response.json();
          console.log(errorResponse, 'errorResponse.detail');

          if (errorResponse.detail && typeof errorResponse.detail === 'string' && !hideError) {
            Notifier.showNotification({
              title: errorResponse.detail,
              Component: NotifierComponents.Alert,
              componentProps: {
                alertType: 'error',
              },
            });
          }
        } catch (error) {}
        throw new Error('Failed to change user status');
      }

      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(['eventAttendees', 'eventUserStatus']);
      },
    },
  );
}
