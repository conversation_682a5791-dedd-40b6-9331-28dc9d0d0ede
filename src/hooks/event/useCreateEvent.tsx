import {BASE_API_URL} from '@env';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {Event} from '~types/api/event';

export function useCreateEvent() {
  const queryClient = useQueryClient();

  return useMutation<Event, Error, Omit<Event, 'host_id' | 'event_id' | 'user_liked' | 'likes_count'>>(
    async (requestData: Omit<Event, 'host_id' | 'event_id' | 'user_liked' | 'likes_count'>) => {
      console.log(JSON.stringify(requestData));
      const token = await FirebaseAuth.getAuthToken();
      // const event_id = crypto.randomUUID();
      const response = await fetch(`${BASE_API_URL}events/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify(requestData),
      });
      if (!response.ok) {
        throw new Error('Failed to create the event');
      }

      const data = await response.json();

      return data as Event;
    },
    {onSuccess: () => queryClient.refetchQueries(['eventsAll'])},
  );
}
