import {BASE_API_URL} from '@env';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {MatchAvailable} from '~types/api/event';

export const useGetEventMatchingStatus = (event_id?: string) => {
  return useQuery<unknown, Error, MatchAvailable>(['matchStatus'], async () => {
    if (!event_id) {
      return;
    }
    const token = await FirebaseAuth.getAuthToken();
    const response = await fetch(`${BASE_API_URL}events/${event_id}/interested-users/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to get user type');
    }

    const data = await response.json();

    return data;
  });
};
