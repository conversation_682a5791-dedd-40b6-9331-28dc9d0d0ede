import {useInfiniteQuery} from 'react-query';
import axios from 'axios';
import FirebaseAuth from '~services/FirebaseAuthService';
import {BASE_API_URL} from '@env';
import {GetEventsParams} from '~types/events';

const NUMBER_OF_EVENTS_PER_PAGE = 20; // Optimized pagination

export const useAllOrganiserEvent = ({
  tab,
  order_by,
  order_dir,
  distance_km,
  event_age_group,
  enabled = false,
  q,
  filter_my_event_type,
  filter_type,
  user_id,
  timeframe,
}: Omit<GetEventsParams, 'offset' | 'limit'> & {enabled?: boolean}) => {
  const fetchEvents = async ({pageParam = 0}) => {
    const token = await FirebaseAuth.getAuthToken();
    console.log(token, 'token');

    const offset = pageParam * NUMBER_OF_EVENTS_PER_PAGE;
    const limit = NUMBER_OF_EVENTS_PER_PAGE;

    const params: GetEventsParams = {
      limit,
      offset,
      tab,
      order_by,
      order_dir,
      distance_km,
      event_age_group,
      q,
      filter_my_event_type,
      filter_type,
      user_id,
      timeframe,
    };

    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };
    console.log(BASE_API_URL + 'events/', {params, ...config}, 'BASE_API_URL + {params, ...config}');

    const response = await axios.get(BASE_API_URL + 'events/', {params, ...config});
    console.log(response, 'BASE_API_URL + {params, ...config}');
    return response.data;
  };

  return useInfiniteQuery('eventsAllOrganise', fetchEvents, {
    getNextPageParam: (lastPage, pages) => {
      const items = lastPage?.data || lastPage?.items || [];
      const nextPage = items.length === NUMBER_OF_EVENTS_PER_PAGE ? pages.length : undefined;
      return nextPage;
    },
    enabled,
    // Performance optimizations
    cacheTime: 1000 * 60 * 30, // 30 minutes cache
    staleTime: 1000 * 60 * 5, // 5 minutes stale time
    keepPreviousData: true,
    refetchOnWindowFocus: false, // Don't refetch on focus for user events
    refetchOnReconnect: true,
  });
};
