import {useCallback, useEffect, useRef, useState, useMemo} from 'react';
import {Region} from 'react-native-maps';
import {useOptimizedEvents} from '~hooks/performance/useOptimizedEvents';
import {GetEventsParams} from '~types/events';
import {TABS, ORDER_BY, ORDER_DIR, EVENT_AGE_GROUP} from '~types/events';
import haversine from 'haversine';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import useUpdateBusiness from '~hooks/business/useUpdateBusiness';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import auth from '@react-native-firebase/auth';
import {useMapsContext} from '~providers/maps/zustand';
import { useAllEvents } from './useAllEvents';

interface UseMapViewportEventsProps {
  radius: number;
  isForKids: boolean;
  selectedTimeframe: any;
  globalInputValue: string;
  isNeighbourhood: boolean;
  userLocation: {latitude: number; longitude: number} | null;
  enabled?: boolean;
}

interface MapViewportState {
  center: {latitude: number; longitude: number};
  radius: number;
  lastFetchTime: number;
}

export const useMapViewportEvents = ({
  radius,
  isForKids,
  selectedTimeframe,
  globalInputValue,
  isNeighbourhood,
  userLocation,
  enabled = true,
}: UseMapViewportEventsProps) => {
  const [mapViewport, setMapViewport] = useState<MapViewportState | null>(null);
  const lastFetchRef = useRef<MapViewportState | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isUpdatingLocation, setIsUpdatingLocation] = useState(false);

  // Get required hooks for updating user location
  const {data: businessAccountData} = useGetBusinessAccount(auth().currentUser!.uid);
  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {mutateAsync: updateBusinessMutation} = useUpdateBusiness();
  const {setCurrentPositionState} = useMapsContext();

  // Calculate if we need to fetch new events based on viewport change
  const shouldFetchNewEvents = useCallback((newViewport: MapViewportState): boolean => {
    if (!lastFetchRef.current) {
      return true;
    }

    const lastFetch = lastFetchRef.current;

    // Calculate distance between current viewport center and last fetch center
    const distance = haversine(
      {latitude: lastFetch.center.latitude, longitude: lastFetch.center.longitude},
      {latitude: newViewport.center.latitude, longitude: newViewport.center.longitude},
      {unit: 'km'},
    );

    // Fetch new events if:
    // 1. User moved more than 5km from last fetch point
    // 2. Radius changed significantly (more than 10km difference)
    // 3. More than 5 minutes since last fetch
    const distanceThreshold = 5; // km
    const radiusThreshold = 10; // km
    const timeThreshold = 5 * 60 * 1000; // 5 minutes

    const shouldFetch =
      distance > distanceThreshold ||
      Math.abs(newViewport.radius - lastFetch.radius) > radiusThreshold ||
      Date.now() - lastFetch.lastFetchTime > timeThreshold;

    console.log(`🗺️ [VIEWPORT] Should fetch new events:`, {
      shouldFetch,
      distance: Math.round(distance * 10) / 10,
      distanceThreshold,
      radiusDiff: Math.abs(newViewport.radius - lastFetch.radius),
      radiusThreshold,
      timeSinceLastFetch: Math.round((Date.now() - lastFetch.lastFetchTime) / 1000),
      timeThresholdSeconds: timeThreshold / 1000,
    });

    return shouldFetch;
  }, []);

  // Temporarily update user location for viewport-based event fetching
  const updateUserLocationForViewport = useCallback(
    async (center: {latitude: number; longitude: number}) => {
      if (isUpdatingLocation) {
        return; // Prevent concurrent updates
      }

      try {
        setIsUpdatingLocation(true);
        console.log(`🗺️ [VIEWPORT] Temporarily updating user location for event fetch:`, center);

        // Update the maps context state
        setCurrentPositionState({
          latitude: center.latitude,
          longitude: center.longitude,
          address: '',
        });

        // Update user location in the backend
        if (businessAccountData?.uid) {
          const payload = {uid: auth().currentUser!.uid, coords: {lat: center.latitude, long: center.longitude}};
          await updateBusinessMutation(payload);
        } else {
          await updateUserMutation({
            coords_real: {lat: center.latitude, long: center.longitude},
          });
        }

        console.log(`🗺️ [VIEWPORT] User location updated successfully for viewport`);
      } catch (error) {
        console.error('🗺️ [VIEWPORT] Failed to update user location:', error);
      } finally {
        setIsUpdatingLocation(false);
      }
    },
    [
      isUpdatingLocation,
      businessAccountData,
      auth().currentUser,
      updateBusinessMutation,
      updateUserMutation,
      setCurrentPositionState,
    ],
  );

  // Update map viewport with debouncing
  const updateMapViewport = useCallback(
    (region: Region) => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      debounceTimeoutRef.current = setTimeout(async () => {
        const newViewport: MapViewportState = {
          center: {
            latitude: region.latitude,
            longitude: region.longitude,
          },
          radius: radius,
          lastFetchTime: Date.now(),
        };

        if (shouldFetchNewEvents(newViewport)) {
          console.log(`🗺️ [VIEWPORT] Updating viewport for new events fetch:`, {
            center: newViewport.center,
            radius: newViewport.radius,
          });

          // Update user location temporarily for this viewport
          await updateUserLocationForViewport(newViewport.center);

          setMapViewport(newViewport);
          lastFetchRef.current = newViewport;
        }
      }, 750); // 750ms debounce - reduced for smoother pin loading
    },
    [radius, shouldFetchNewEvents, updateUserLocationForViewport],
  );

  // Initialize with user location
  useEffect(() => {
    console.log(`🗺️ [VIEWPORT INIT] Checking initialization:`, {
      hasUserLocation: !!userLocation,
      userLocation,
      hasMapViewport: !!mapViewport,
      mapViewport,
      radius,
    });

    if (userLocation && !mapViewport) {
      const initialViewport: MapViewportState = {
        center: userLocation,
        radius: radius,
        lastFetchTime: Date.now(),
      };
      console.log(`🗺️ [VIEWPORT INIT] Setting initial viewport:`, initialViewport);
      setMapViewport(initialViewport);
      lastFetchRef.current = initialViewport;
      console.log(`🗺️ [VIEWPORT INIT] Viewport initialized successfully`);
    } else if (!userLocation) {
      console.log(`🗺️ [VIEWPORT INIT] No user location available yet`);
    } else if (mapViewport) {
      console.log(`🗺️ [VIEWPORT INIT] Viewport already exists:`, mapViewport);
    }
  }, [userLocation, mapViewport, radius]);

  // Fetch events for the current viewport
  console.log(`🗺️ [VIEWPORT HOOK] Fetching events for viewport:`, {
    enabled: enabled && !!mapViewport,
    mapViewport,
    userLocation,
    radius: mapViewport?.radius || radius,
  });

  const {data, isLoading, isFetching, fetchNextPage, hasNextPage, isFetchingNextPage, refetch, remove, error} =
    useAllEvents({
      distance_km: mapViewport?.radius || radius,
      tab: TABS.DISCOVER,
      order_by: ORDER_BY.START_DATE,
      order_dir: ORDER_DIR.ASC,
      event_age_group: isForKids ? EVENT_AGE_GROUP.CHILDREN : null,
      filter_type: selectedTimeframe?.title === 'Upcoming Events' ? undefined : selectedTimeframe?.title,
      q: globalInputValue,
      isNeighbourhood: isNeighbourhood,
      enabled: enabled && !!mapViewport,
      // Enable performance optimizations
    });

  // Enhanced logging for debugging
  useEffect(() => {
    console.log('🗺️ [VIEWPORT EVENTS] Debug info:', {
      enabled: enabled && !!mapViewport,
      hasMapViewport: !!mapViewport,
      mapViewportCenter: mapViewport?.center,
      mapViewportRadius: mapViewport?.radius,
      userLocation,
      radius,
      isLoading,
      isFetching,
      hasData: !!data,
      dataPages: data?.pages?.length || 0,
      firstPageEvents: (data?.pages?.[0]?.data || data?.pages?.[0]?.items || []).length,
      error: error?.message,
    });
  }, [enabled, mapViewport, userLocation, radius, isLoading, isFetching, data, error]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Combine online and offline data with fallback logic
  const combinedData = useMemo(() => {
    return data;
  }, [data]);

  return {
    data: combinedData,
    isLoading,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    remove,
    updateMapViewport,
    currentViewport: mapViewport,
    isUpdatingLocation,
    error,
  };
};
