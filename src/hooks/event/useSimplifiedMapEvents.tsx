import {useCallback, useEffect, useRef, useState, useMemo} from 'react';
import {Region} from 'react-native-maps';
import {Event} from '~types/api/event';
import {useInMemoryEventCache} from '~hooks/performance/useInMemoryEventCache';
import {useOptimizedEvents} from '~hooks/performance/useOptimizedEvents';
import {TABS, ORDER_BY, ORDER_DIR, EVENT_AGE_GROUP} from '~types/events';
import {calculateRectangularBounds, shouldUseCachedData, RectangularBounds} from '../../Utils/cache/rectangularBounds';
import { useAllEvents } from './useAllEvents';
const haversine = require('haversine');

interface UseSimplifiedMapEventsProps {
  radius: number;
  isForKids?: boolean;
  selectedTimeframe?: any;
  globalInputValue?: string;
  isNeighbourhood?: boolean;
  userLocation?: {latitude: number; longitude: number} | null;
  enabled?: boolean;
}

interface MapViewportState {
  center: {latitude: number; longitude: number};
  radius: number;
  lastFetchTime: number;
  zoomLevel?: number;
  bounds?: RectangularBounds;
}

/**
 * Simplified map events hook that uses in-memory coordinate-based caching
 * Fetches new events only when necessary and caches them by coordinates
 */
export const useSimplifiedMapEvents = ({
  radius,
  isForKids,
  selectedTimeframe,
  globalInputValue,
  isNeighbourhood,
  userLocation,
  enabled = true,
}: UseSimplifiedMapEventsProps) => {
  const [mapViewport, setMapViewport] = useState<MapViewportState | null>(null);
  const [currentRadius, setCurrentRadius] = useState(radius);
  const lastFetchRef = useRef<MapViewportState | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const maxRadius = useRef(1500); // Maximum radius of 1500km (increased for better coverage)

  // Initialize in-memory cache with increased limits
  const eventCache = useInMemoryEventCache({
    maxCacheAge: 30, // 30 minutes
    maxCacheEntries: 100, // Increased to 100 cache entries
    coordinatePrecision: 3, // ~100m precision
  });

  // Send cache performance to analytics every 5 minutes (only once)
  useEffect(() => {
    const interval = setInterval(
      () => {
        eventCache.sendCachePerformanceToAnalytics();
      },
      5 * 60 * 1000,
    ); // 5 minutes

    return () => clearInterval(interval);
  }, []); // Remove eventCache dependency to prevent infinite loop

  /**
   * Calculate radius based on zoom level
   * Lower zoom (zoomed out) = larger radius
   * Higher zoom (zoomed in) = smaller radius
   */
  const calculateRadiusFromZoom = useCallback((zoomLevel: number): number => {
    // Zoom levels typically range from 1-20
    // Zoom 1 = very zoomed out (world view)
    // Zoom 20 = very zoomed in (building level)

    // Base radius calculation: higher zoom = smaller radius
    let calculatedRadius;

    if (zoomLevel <= 8) {
      // Very zoomed out - use large radius (200-500km)
      calculatedRadius = Math.max(200, 500 - (zoomLevel - 1) * 50);
    } else if (zoomLevel <= 12) {
      // Medium zoom - moderate radius (50-200km)
      calculatedRadius = Math.max(50, 200 - (zoomLevel - 8) * 37.5);
    } else if (zoomLevel <= 16) {
      // Zoomed in - smaller radius (10-50km)
      calculatedRadius = Math.max(10, 50 - (zoomLevel - 12) * 10);
    } else {
      // Very zoomed in - minimum radius (5-10km)
      calculatedRadius = Math.max(5, 10 - (zoomLevel - 16) * 1.25);
    }

    // Ensure we don't exceed maximum radius and round to integer for API compatibility
    const finalRadius = Math.round(Math.min(calculatedRadius, maxRadius.current));

    console.log(
      `🔍 [MAP] Calculated radius from zoom: zoom=${zoomLevel}, radius=${finalRadius}km (rounded to integer)`,
    );
    return finalRadius;
  }, []);

  /**
   * Calculate zoom level from region
   */
  const calculateZoomLevel = useCallback((region: Region): number => {
    // Simple zoom calculation based on latitude delta
    // This is an approximation - more accurate would use map dimensions
    const zoom = Math.round(Math.log(360 / region.latitudeDelta) / Math.LN2);
    return Math.max(1, Math.min(20, zoom)); // Clamp between 1-20
  }, []);

  /**
   * Check if we need to fetch new events based on viewport change
   */
  const shouldFetchNewEvents = useCallback(
    (newViewport: MapViewportState): boolean => {
      if (!lastFetchRef.current) {
        return true;
      }

      const lastFetch = lastFetchRef.current;

      // Calculate distance moved
      const distance = haversine(
        {latitude: lastFetch.center.latitude, longitude: lastFetch.center.longitude},
        {latitude: newViewport.center.latitude, longitude: newViewport.center.longitude},
        {unit: 'km'},
      );

      // Thresholds for fetching new events
      const distanceThreshold = Math.max(radius * 0.3, 2); // 30% of radius or 2km minimum
      const radiusThreshold = 100; // 100km radius change threshold
      const timeThreshold = 5 * 60 * 1000; // 5 minutes

      const shouldFetch =
        distance > distanceThreshold ||
        Math.abs(newViewport.radius - lastFetch.radius) > radiusThreshold ||
        Date.now() - lastFetch.lastFetchTime > timeThreshold;

      console.log(`🗺️ [SIMPLIFIED] Should fetch new events:`, {
        shouldFetch,
        distance: Math.round(distance * 10) / 10,
        distanceThreshold,
        radiusDiff: Math.abs(newViewport.radius - lastFetch.radius),
        radiusThreshold,
        timeSinceLastFetch: Math.round((Date.now() - lastFetch.lastFetchTime) / 1000),
        timeThresholdSeconds: timeThreshold / 1000,
      });

      return shouldFetch;
    },
    [radius],
  );

  /**
   * Build filters object for caching
   */
  const buildFilters = useMemo(
    () => ({
      isForKids,
      selectedTimeframe: selectedTimeframe?.title,
      globalInputValue,
      isNeighbourhood,
    }),
    [isForKids, selectedTimeframe?.title, globalInputValue, isNeighbourhood],
  );

  /**
   * Get cached events for current viewport
   */
  const getCachedEventsForViewport = useCallback(
    (viewport: MapViewportState): Event[] | null => {
      return eventCache.getCachedEvents(viewport.center, viewport.radius, buildFilters);
    },
    [eventCache, buildFilters],
  );

  /**
   * Fetch events from API
   */
  const {
    data: apiData,
    isLoading,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    remove,
    error,
  } = useAllEvents({
    distance_km: currentRadius, // Use dynamic radius based on zoom level
    tab: TABS.DISCOVER,
    order_by: ORDER_BY.START_DATE,
    order_dir: ORDER_DIR.ASC,
    event_age_group: isForKids ? EVENT_AGE_GROUP.CHILDREN : null,
    filter_type: selectedTimeframe?.title === 'Upcoming Events' ? undefined : selectedTimeframe?.title,
    q: globalInputValue,
    isNeighbourhood: isNeighbourhood,
    enabled: enabled && !!mapViewport,
  });

  /**
   * Process and cache API data
   */
  useEffect(() => {
    if (apiData?.pages && mapViewport) {
      const events = apiData.pages.flatMap(page => {
        if (page?.items && Array.isArray(page.items)) {
          return page.items;
        } else if (page?.data && Array.isArray(page.data)) {
          return page.data;
        } else if (Array.isArray(page)) {
          return page;
        }
        return [];
      });

      if (events.length > 0) {
        // Store events in cache with rectangular bounds
        eventCache.storeEvents(mapViewport.center, mapViewport.radius, events, buildFilters, mapViewport.bounds);
        lastFetchRef.current = mapViewport;
      }
    }
  }, [apiData, mapViewport, eventCache, buildFilters]);

  /**
   * Update map viewport with smart rectangular cache logic
   */
  const updateMapViewport = useCallback(
    (region: Region) => {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      debounceTimeoutRef.current = setTimeout(() => {
        // Calculate zoom level and dynamic radius
        const zoomLevel = calculateZoomLevel(region);
        const dynamicRadius = calculateRadiusFromZoom(zoomLevel);

        // Update current radius state
        setCurrentRadius(dynamicRadius);

        const currentLocation = {
          latitude: region.latitude,
          longitude: region.longitude,
        };

        // Check if current location is within any cached rectangular bounds
        const boundsCheck = eventCache.isWithinCachedBounds(currentLocation, dynamicRadius, buildFilters);

        if (boundsCheck.withinBounds && boundsCheck.cachedEvents) {
          console.log(`📍 [SMART CACHE] Using cached events, location within bounds`);

          // Update viewport without triggering fetch
          const newViewport: MapViewportState = {
            center: currentLocation,
            radius: dynamicRadius,
            lastFetchTime: Date.now(),
            zoomLevel: zoomLevel,
          };

          setMapViewport(newViewport);
          return; // Don't fetch new data
        }

        // Calculate rectangular bounds for new fetch
        const bounds = calculateRectangularBounds(region, dynamicRadius);

        const newViewport: MapViewportState = {
          center: currentLocation,
          radius: dynamicRadius,
          lastFetchTime: Date.now(),
          zoomLevel: zoomLevel,
          bounds: bounds,
        };

        console.log(`🗺️ [SMART CACHE] Location outside cached bounds, updating viewport:`, {
          center: newViewport.center,
          radius: newViewport.radius,
          zoomLevel: newViewport.zoomLevel,
          originalRadius: radius,
          hasBounds: !!bounds,
        });

        // Update viewport and trigger fetch
        setMapViewport(newViewport);
        lastFetchRef.current = newViewport;

        // Cull events outside viewport for memory management
        eventCache.cullEventsOutsideViewport(newViewport.center, newViewport.radius);
      }, 500); // 500ms debounce - reduced for smoother pin loading
    },
    [radius, eventCache, calculateZoomLevel, calculateRadiusFromZoom, buildFilters],
  );

  /**
   * Get events for current viewport (cached or fresh)
   */
  const data = useMemo(() => {
    if (!mapViewport) {
      return null;
    }

    // First try to get cached events
    const cachedEvents = getCachedEventsForViewport(mapViewport);
    if (cachedEvents && cachedEvents.length > 0) {
      console.log(`🎯 [SIMPLIFIED] Using ${cachedEvents.length} cached events`);
      return {
        pages: [
          {
            data: cachedEvents,
            meta: {
              current_page: 1,
              last_page: 1,
              per_page: cachedEvents.length,
              total: cachedEvents.length,
            },
          },
        ],
        pageParams: [undefined],
      };
    }

    // Try to find nearby events from cache
    const nearbyEvents = eventCache.findNearbyEvents(mapViewport.center, mapViewport.radius, buildFilters);
    if (nearbyEvents.length > 0) {
      console.log(`🔍 [SIMPLIFIED] Using ${nearbyEvents.length} nearby cached events`);
      return {
        pages: [
          {
            data: nearbyEvents,
            meta: {
              current_page: 1,
              last_page: 1,
              per_page: nearbyEvents.length,
              total: nearbyEvents.length,
            },
          },
        ],
        pageParams: [undefined],
      };
    }

    // If no cached events and API data is available, use API data
    if (apiData?.pages?.length) {
      return apiData;
    }

    // No data available
    return null;
  }, [mapViewport, getCachedEventsForViewport, eventCache, buildFilters, apiData]);

  /**
   * Initialize viewport when user location is available
   */
  useEffect(() => {
    if (userLocation && !mapViewport) {
      const initialViewport: MapViewportState = {
        center: userLocation,
        radius: radius,
        lastFetchTime: Date.now(),
      };
      setMapViewport(initialViewport);
    }
  }, [userLocation, mapViewport, radius]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    data,
    isLoading,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    remove,
    error,
    updateMapViewport,

    // Map specific
    currentRadius,

    // Cache utilities
    clearCache: eventCache.clearCache,
    getCacheStats: eventCache.getCacheStats,

    // Current viewport info
    currentViewport: mapViewport,
  };
};
