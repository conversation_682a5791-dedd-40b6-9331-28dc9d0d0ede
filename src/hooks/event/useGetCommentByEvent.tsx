import {useTranslation} from 'react-i18next';
import {Alert} from 'react-native';
import {BASE_API_URL} from '@env';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {Comment} from '~types/api/event';

export const useGetCommentByEvent = (event_id: number) => {
  const {t} = useTranslation();

  return useQuery<Comment[], Error>(
    ['commentByEvent', event_id],
    async () => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${BASE_API_URL}events/${event_id}/comments?page_size=10000`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      if (response.status === 404) {
        Alert.alert(t('events.event_has_been_deleted'));
      }
      if (!response.ok) {
        throw new Error('Failed to get the event by id');
      }

      const data = await response.json();

      return data.comments as Comment[];
    },
    {cacheTime: 0},
  );
};
