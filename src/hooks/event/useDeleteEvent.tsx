import {BASE_API_URL} from '@env';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {DeleteEventResponse, DeleteEventRequest} from '~types/api/event';

export function useDeleteEvent() {
  const queryClient = useQueryClient();

  return useMutation<DeleteEventResponse, Error, DeleteEventRequest>(
    async (requestData: DeleteEventRequest) => {
      const token = await FirebaseAuth.getAuthToken();
      console.log('Auth token:', token);
      const url = `${BASE_API_URL}events/${requestData.event_id}`;
      console.log('URL:', url);

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      const responseBody = await response.text();
      console.log('Response status:', response.status);
      console.log('Response body:', responseBody);

      if (!response.ok) {
        throw new Error('Failed to delete the event');
      }

      return {details: 'Event deleted successfully'};
    },
    {
      onSuccess: () => {
        console.log('Event deleted successfully, refetching events');
        queryClient.refetchQueries('eventsAll');
      },
    },
  );
}
