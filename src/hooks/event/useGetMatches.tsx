import {BASE_API_URL} from '@env';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {User} from '~types/api/user';

export function useGetMatches(
  event_id: number | null,
  refresh = false,
  domain: string | null,
  matchingType: 'neighbours' | 'anyone' | null = null,
) {
  return useQuery<User[] | undefined, Error>(
    ['eventMatches', event_id],
    async () => {
      console.log('event_id:', event_id);
      if (!event_id) {
        return;
      }

      const token = await FirebaseAuth.getAuthToken();
      const queryParams = new URLSearchParams({
        distance_km: '100',
        refresh: refresh.toString(),
        limit: '5',
        ...(domain && {domain}),
        ...(matchingType && {matching_type: matchingType}),
      });
      console.log(`${BASE_API_URL}events/${event_id}/interested-users?${queryParams.toString()}`, 'testetete');
      const response = await fetch(
        `${BASE_API_URL}events/${event_id}/interested-users?${queryParams.toString()}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: token,
          },
        },
      );

      if (!response.ok) {
        const dataFinal = await response.json();
        console.log(JSON.stringify(dataFinal, undefined, 4), 'dataFinaldataFinal');
        return;
      }

      const data = (await response.json()) as User[];
      console.log(data);

      return data;
    },
    {enabled: true},
  );
}
