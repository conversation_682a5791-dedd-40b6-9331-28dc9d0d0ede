import {useTranslation} from 'react-i18next';
import {Alert} from 'react-native';
import {BASE_API_URL} from '@env';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {OrderDetail} from '~types/api/event';

export const useGetOrderList = () => {
  const {t} = useTranslation();

  return useQuery<OrderDetail[], Error>(['orderById'], async () => {
    const token = await FirebaseAuth.getAuthToken();
    const response = await fetch(`${BASE_API_URL}orders/`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    });

    if (response.status === 404) {
      Alert.alert(t('events.event_has_been_deleted'));
    }
    if (!response.ok) {
      throw new Error('Failed to get the event by id');
    }

    const data = await response.json();

    return data;
  });
};
