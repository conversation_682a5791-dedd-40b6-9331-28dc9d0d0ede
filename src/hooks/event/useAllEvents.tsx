import {useInfiniteQuery} from 'react-query';
import axios from 'axios';
import FirebaseAuth from '~services/FirebaseAuthService';

import {BASE_API_URL} from '@env';
import {GetEventsParams} from '~types/events';

const NUMBER_OF_EVENTS_PER_PAGE = 10000;

export const useAllEvents = ({
  tab,
  order_by,
  order_dir,
  distance_km,
  event_age_group,
  enabled = false,
  q,
  filter_my_event_type,
  filter_type,
  user_id,
  timeframe
}: Omit<GetEventsParams, 'offset' | 'limit'> & {enabled?: boolean}) => {
  const fetchEvents = async ({pageParam = 0}) => {
    const token = await FirebaseAuth.getAuthToken();
    console.log(token, 'token');
    

    const offset = pageParam * NUMBER_OF_EVENTS_PER_PAGE;
    const limit = NUMBER_OF_EVENTS_PER_PAGE;

    const params: GetEventsParams = {
      limit,
      offset,
      tab,
      order_by,
      order_dir,
      distance_km,
      event_age_group,
      q,
      filter_my_event_type,
      filter_type,
      user_id,
      timeframe
    };
    
    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };

    const response = await axios.get(BASE_API_URL + 'events/', {params, ...config});
    return response.data;
  };

  return useInfiniteQuery('eventsAll', fetchEvents, {
    getNextPageParam: (lastPage, pages) => {
      const nextPage = lastPage.items.length === 10 ? pages.length : undefined;
      return nextPage;
    },
    enabled,
    cacheTime: 0,
  });
};

/* Not working optimised variant
import {GetEventsParams} from '~types/events';
import {useOptimizedEvents} from '~hooks/performance/useOptimizedEvents';

// Wrapper for backward compatibility - now uses optimized version
export const useAllEvents = ({
  tab,
  order_by,
  order_dir,
  distance_km,
  event_age_group,
  enabled = true, // Changed default to true for better UX
  q,
  filter_my_event_type,
  filter_type,
  user_id,
  timeframe,
  isNeighbourhood = false,
}: Omit<GetEventsParams, 'offset' | 'limit'> & {enabled?: boolean}) => {
  return useOptimizedEvents({
    tab,
    order_by,
    order_dir,
    distance_km,
    event_age_group,
    enabled,
    q,
    filter_my_event_type,
    filter_type,
    user_id,
    timeframe,
    isNeighbourhood,
    // Enable performance optimizations
    prefetchNext: true,
    backgroundRefetch: true,
  });
};
*/
