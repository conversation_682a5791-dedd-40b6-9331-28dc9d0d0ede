import axios from 'axios';
import {BASE_API_URL} from '@env';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {GetEventAttendeesResponse} from '~types/api/event';
import {SUBSCRIPTION_STATUS} from '~types/api/user';

export function useGetEventAttendees({
  event_id,
  subscription_status,
}: {
  event_id: number;
  subscription_status: SUBSCRIPTION_STATUS;
}) {
  return useQuery<GetEventAttendeesResponse | undefined, Error>(
    ['eventAttendees', event_id],
    async () => {
      if (!event_id) {
        return undefined;
      }
      const token = await FirebaseAuth.getAuthToken();

      const config = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      };

      const params = {
        subscription_status,
      };

      const response = await axios.get(`${BASE_API_URL}events/${event_id}/subscriptions`, {params, ...config});
      const response1 = await axios.get(`${BASE_API_URL}events/${event_id}/external/subscriptions`, {
        params,
        ...config,
      });

      return [...response.data, ...response1.data];
    },
    {enabled: true},
  );
}
