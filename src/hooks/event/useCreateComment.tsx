import {BASE_API_URL} from '@env';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {DeleteEventResponse, CreateCommentEventRequest} from '~types/api/event';

export function useCreateComment() {
  const queryClient = useQueryClient();

  return useMutation<DeleteEventResponse, Error, CreateCommentEventRequest>(
    async (requestData: CreateCommentEventRequest) => {
      const token = await FirebaseAuth.getAuthToken();
      const url = `${BASE_API_URL}events/${requestData.event_id}/comments`;
      const response = await fetch(url, {
        method: 'POST',
        body: JSON.stringify(requestData),
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      const responseBody = await response.json();
      console.log(requestData, token, 'responseresponseresponse');

      if (!response.ok) {
        throw new Error('Failed to delete the event');
      }

      return {details: 'Comment created successfully'};
    },
    {
      onSuccess: () => {
        console.log('Event cancled successfully, refetching events');
        queryClient.refetchQueries('commentByEvent');
      },
    },
  );
}
