import {useMutation} from 'react-query';
import {BASE_API_URL} from '@env';
import axios from 'axios';
import FirebaseAuth from '~services/FirebaseAuthService';

export const useAddReviewByEventId = () => {
  const {mutate, isLoading} = useMutation(async (payload: {event_id: number; comment: string; rating: number}) => {
    const token = await FirebaseAuth.getAuthToken();
    const response = await axios.post(
      `${BASE_API_URL}events/${payload.event_id}/review`,
      {
        rating: payload.rating,
        comment: payload.comment,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      },
    );
    if (!response.data) {
      throw new Error('Failed to create short link');
    }

    const data = await response.data;
    console.log(data, 'data');

    return data as any;
  });
  return {
    giveReview: mutate,
    isLoading,
  };
};
