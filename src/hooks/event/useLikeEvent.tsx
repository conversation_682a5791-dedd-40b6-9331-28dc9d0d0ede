import {BASE_API_URL} from '@env';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {AddLikePayload, Event} from '~types/api/event';

export function useLikeEvent() {
  const queryClient = useQueryClient();

  return useMutation<string, Error, AddLikePayload>(
    async ({eventId}: AddLikePayload) => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${BASE_API_URL}events/${eventId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });
      console.log('add like', response);
      if (!response.ok) {
        throw new Error('Failed to like the event');
      }

      const data = await response.json();

      return data as string;
    },
    {
      onMutate: async ({eventId}) => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries(['eventsAll']);
        await queryClient.cancelQueries(['eventsAllOrganise']);
        await queryClient.cancelQueries(['eventById', eventId]);

        // Snapshot the previous values
        const previousEventsData = queryClient.getQueryData(['eventsAll']);
        const previousOrganiserEventsData = queryClient.getQueryData(['eventsAllOrganise']);
        const previousEventData = queryClient.getQueryData(['eventById', eventId]);

        // Helper function to update event in paginated data
        const updateEventInPages = (oldData: any) => {
          if (!oldData) {
            return oldData;
          }

          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              items: page.items.map((event: Event) =>
                event.event_id === eventId
                  ? {
                      ...event,
                      user_liked: true,
                      likes_count: (event.likes_count || 0) + 1,
                    }
                  : event,
              ),
            })),
          };
        };

        // Optimistically update all relevant caches
        queryClient.setQueryData(['eventsAll'], updateEventInPages);
        queryClient.setQueryData(['eventsAllOrganise'], updateEventInPages);

        // Update individual event cache
        queryClient.setQueryData(['eventById', eventId], (oldData: Event | undefined) => {
          if (!oldData || oldData.event_id !== eventId) {
            return oldData;
          }

          return {
            ...oldData,
            user_liked: true,
            likes_count: (oldData.likes_count || 0) + 1,
          };
        });

        // Return a context object with the snapshotted values
        return {previousEventsData, previousOrganiserEventsData, previousEventData};
      },
      onError: (err, variables, context) => {
        // If the mutation fails, use the context returned from onMutate to roll back
        if (context?.previousEventsData) {
          queryClient.setQueryData(['eventsAll'], context.previousEventsData);
        }
        if (context?.previousOrganiserEventsData) {
          queryClient.setQueryData(['eventsAllOrganise'], context.previousOrganiserEventsData);
        }
        if (context?.previousEventData) {
          queryClient.setQueryData(['eventById', variables.eventId], context.previousEventData);
        }
      },
      onSettled: () => {
        // Only invalidate on error - success case is handled optimistically
        // This ensures we have the latest data without unnecessary refetches
      },
    },
  );
}
