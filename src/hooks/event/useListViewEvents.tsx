import {useCallback, useEffect, useRef, useState, useMemo} from 'react';
import {Event} from '~types/api/event';
import {useInMemoryEventCache} from '~hooks/performance/useInMemoryEventCache';
import {useOptimizedEvents} from '~hooks/performance/useOptimizedEvents';
import {TABS, ORDER_BY, ORDER_DIR, EVENT_AGE_GROUP} from '~types/events';
import { useAllEvents } from './useAllEvents';

interface UseListViewEventsProps {
  initialRadius: number;
  isForKids?: boolean;
  selectedTimeframe?: any;
  globalInputValue?: string;
  isNeighbourhood?: boolean;
  userLocation?: {latitude: number; longitude: number} | null;
  enabled?: boolean;
}

/**
 * List view events hook with dynamic radius expansion
 * Expands radius as user scrolls to load more events
 * Uses coordinate-based caching for performance
 */
export const useListViewEvents = ({
  initialRadius,
  isForKids,
  selectedTimeframe,
  globalInputValue,
  isNeighbourhood,
  userLocation,
  enabled = true,
}: UseListViewEventsProps) => {
  const [currentRadius, setCurrentRadius] = useState(initialRadius);
  const [isExpanding, setIsExpanding] = useState(false);
  const lastExpandRef = useRef<number>(Date.now());
  const maxRadius = useRef(1500); // Maximum radius of 1500km (increased for better coverage)

  // Initialize in-memory cache
  const eventCache = useInMemoryEventCache({
    maxCacheAge: 30, // 30 minutes
    maxCacheEntries: 150, // Higher limit for list view
    coordinatePrecision: 3, // ~100m precision
  });

  /**
   * Build filters object for caching
   */
  const buildFilters = useMemo(
    () => ({
      isForKids,
      selectedTimeframe: selectedTimeframe?.title,
      globalInputValue,
      isNeighbourhood,
    }),
    [isForKids, selectedTimeframe?.title, globalInputValue, isNeighbourhood],
  );

  /**
   * Fetch events from API with current radius
   */
  const {
    data: apiData,
    isLoading,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    remove,
    error,
  } = useAllEvents({
    distance_km: currentRadius,
    tab: TABS.DISCOVER,
    order_by: ORDER_BY.START_DATE,
    order_dir: ORDER_DIR.ASC,
    event_age_group: isForKids ? EVENT_AGE_GROUP.CHILDREN : null,
    filter_type: selectedTimeframe?.title === 'Upcoming Events' ? undefined : selectedTimeframe?.title,
    q: globalInputValue,
    isNeighbourhood: isNeighbourhood,
    enabled: enabled && !!userLocation, // Only fetch when user location is available
  });

  /**
   * Process and cache API data
   */
  useEffect(() => {
    if (apiData?.pages && userLocation) {
      const events = apiData.pages.flatMap(page => {
        if (page?.items && Array.isArray(page.items)) {
          return page.items;
        } else if (page?.data && Array.isArray(page.data)) {
          return page.data;
        } else if (Array.isArray(page)) {
          return page;
        }
        return [];
      });

      if (events.length > 0) {
        // Store events in cache with current radius
        console.log(events, 'eventseventsevents');
        
        eventCache.storeEvents(userLocation, currentRadius, events, buildFilters);
        console.log(`💾 [LIST VIEW] Cached ${events.length} events for radius ${currentRadius}km`);
      }
    }
  }, [apiData, userLocation, currentRadius, eventCache, buildFilters]);

  /**
   * Get all cached events within current radius
   */
  const getCachedEvents = useCallback((): Event[] => {
    if (!userLocation) {
      return [];
    }

    // Get events from all cached radii up to current radius
    const allEvents: Event[] = [];
    const seenEventIds = new Set<string>();

    // Try different radius levels to get maximum coverage
    for (let radius = initialRadius; radius <= currentRadius; radius += 10) {
      const cachedEvents = eventCache.getCachedEvents(userLocation, radius, buildFilters);
      if (cachedEvents) {
        cachedEvents.forEach(event => {
          if (!seenEventIds.has(event.event_id)) {
            allEvents.push(event);
            seenEventIds.add(event.event_id);
          }
        });
      }
    }

    // Also try to find nearby events
    const nearbyEvents = eventCache.findNearbyEvents(userLocation, currentRadius, buildFilters);
    nearbyEvents.forEach(event => {
      if (!seenEventIds.has(event.event_id)) {
        allEvents.push(event);
        seenEventIds.add(event.event_id);
      }
    });

    return allEvents;
  }, [userLocation, currentRadius, initialRadius, eventCache, buildFilters]);

  /**
   * Expand radius to load more events
   */
  const expandRadius = useCallback(() => {
    const now = Date.now();
    const timeSinceLastExpand = now - lastExpandRef.current;

    // Prevent too frequent expansions
    if (timeSinceLastExpand < 2000 || isExpanding) {
      console.log(`⏳ [LIST VIEW] Skipping radius expansion (too soon or already expanding)`);
      return;
    }

    // Don't expand beyond maximum radius
    if (currentRadius >= maxRadius.current) {
      console.log(`🚫 [LIST VIEW] Maximum radius reached (${maxRadius.current}km)`);
      return;
    }

    setIsExpanding(true);
    lastExpandRef.current = now;

    // More conservative expansion to avoid API limits
    let expansion;
    if (currentRadius <= 50) {
      expansion = 10; // 10km increments for small radius
    } else if (currentRadius <= 100) {
      expansion = 20; // 20km increments for medium radius
    } else if (currentRadius <= 200) {
      expansion = 30; // 30km increments for large radius
    } else if (currentRadius <= 500) {
      expansion = 50; // 50km increments for very large radius
    } else {
      expansion = 100; // 100km increments for extreme radius
    }

    const newRadius = Math.round(Math.min(currentRadius + expansion, maxRadius.current));

    console.log(`📏 [LIST VIEW] Expanding radius from ${currentRadius}km to ${newRadius}km (rounded to integer)`);
    setCurrentRadius(newRadius);

    // Reset expanding state after a delay
    setTimeout(() => {
      setIsExpanding(false);
    }, 1000);
  }, [currentRadius, isExpanding]);

  /**
   * Reset radius to initial value
   */
  const resetRadius = useCallback(() => {
    console.log(`🔄 [LIST VIEW] Resetting radius to ${initialRadius}km`);
    setCurrentRadius(initialRadius);
    setIsExpanding(false);
    lastExpandRef.current = Date.now();
  }, [initialRadius]);

  /**
   * Get combined events (cached + fresh)
   */
  const data = useMemo(() => {
    const cachedEvents = getCachedEvents();

    // If we have cached events, use them
    if (cachedEvents.length > 0) {
      console.log(`🎯 [LIST VIEW] Using ${cachedEvents.length} cached events (radius: ${currentRadius}km)`);

      // Sort events by start date
      const sortedEvents = cachedEvents.sort((a, b) => {
        const dateA = new Date(a.start_date).getTime();
        const dateB = new Date(b.start_date).getTime();
        return dateA - dateB;
      });

      return {
        pages: [
          {
            data: sortedEvents,
            meta: {
              current_page: 1,
              last_page: 1,
              per_page: sortedEvents.length,
              total: sortedEvents.length,
            },
          },
        ],
        pageParams: [undefined],
      };
    }

    // If no cached events and API data is available, use API data
    if (apiData?.pages?.length) {
      return apiData;
    }

    // No data available
    return null;
  }, [getCachedEvents, currentRadius, apiData]);

  /**
   * Enhanced load more function that expands radius when scrolling past last event
   */
  const handleLoadMore = useCallback(() => {
    console.log(`📄 [LIST VIEW] onEndReached triggered - checking if we should expand radius`);

    // First try to fetch next page if available
    if (hasNextPage && !isFetchingNextPage) {
      console.log(`📄 [LIST VIEW] Loading next page from API`);
      fetchNextPage();
      return;
    }

    // If no more pages available, expand radius to get more events
    if (!hasNextPage && !isExpanding) {
      console.log(`📏 [LIST VIEW] No more API pages available, expanding radius to load more events`);
      expandRadius();
    } else if (isExpanding) {
      console.log(`⏳ [LIST VIEW] Already expanding radius, skipping`);
    } else {
      console.log(`📄 [LIST VIEW] Still has pages available, not expanding radius yet`);
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage, isExpanding, expandRadius]);

  /**
   * Get current event count
   */
  const eventCount = useMemo(() => {
    if (data?.pages) {
      return data.pages.reduce((total, page) => {
        const items = page?.data || page?.items || [];
        return total + items.length;
      }, 0);
    }
    return 0;
  }, [data]);

  /**
   * Check if we can load more events
   */
  const canLoadMore = useMemo(() => {
    return hasNextPage || currentRadius < maxRadius.current;
  }, [hasNextPage, currentRadius]);

  return {
    data,
    isLoading,
    isFetching,
    fetchNextPage: handleLoadMore, // Use enhanced load more
    hasNextPage: canLoadMore,
    isFetchingNextPage: isFetchingNextPage || isExpanding,
    refetch,
    remove,
    error,

    // List view specific
    currentRadius,
    expandRadius,
    resetRadius,
    isExpanding,
    eventCount,
    canLoadMore,

    // Cache utilities
    clearCache: eventCache.clearCache,
    getCacheStats: eventCache.getCacheStats,
  };
};
