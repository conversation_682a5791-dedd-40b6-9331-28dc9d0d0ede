import {useTranslation} from 'react-i18next';
import {Alert} from 'react-native';
import {BASE_API_URL} from '@env';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {Event} from '~types/api/event';

export const useGetEventById = (event_id?: number) => {
  const {t} = useTranslation();

  return useQuery<Event, Error>(
    ['eventById', event_id],
    async () => {
      const token = await FirebaseAuth.getAuthToken();
      const baseURL = token ? `${BASE_API_URL}events/${event_id}` : `${BASE_API_URL}public-events/${event_id}`;
      const response = await fetch(baseURL, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      if (response.status === 404) {
        Alert.alert(t('events.event_has_been_deleted'));
      }
      if (!response.ok) {
        throw new Error('Failed to get the event by id');
      }

      const data = await response.json();

      return data as Event;
    },
    {
      // Optimized caching for event details - prevent loading circles when returning from chat
      cacheTime: 1000 * 60 * 60, // 1 hour cache time
      staleTime: 1000 * 60 * 1, // 1 minute stale time - fresh data for at least 1 minute
      enabled: !!event_id,
      keepPreviousData: true,
      refetchOnWindowFocus: false, // Don't refetch when returning from chat
      refetchOnMount: false, // Don't refetch on mount if data is still fresh
      refetchOnReconnect: true, // Only refetch when network reconnects
      retry: 2, // Retry failed requests
    },
  );
};
