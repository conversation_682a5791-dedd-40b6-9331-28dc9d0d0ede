import {useEffect, useState, useCallback} from 'react';
import auth from '@react-native-firebase/auth';
import {useQueryClient} from 'react-query';
import NetInfo from '@react-native-community/netinfo';
import {useOfflineUserData} from '~hooks/offline/useOfflineUserData';
import {useUserStore} from '~providers/userStore/zustand';
import OfflineUserStorage from '~services/OfflineUserStorage/OfflineUserStorage';
import {User} from '~types/api/user';
import {Business} from '~types/api/business';

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitializing: boolean;
  user: Omit<User, 'subcategories' | 'groups'> | null;
  business: Business | null;
  userType: 'personal' | 'business' | null;
  isOnline: boolean;
  hasOfflineData: boolean;
  lastSync: string | null;
  error: string | null;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<{success: boolean; error?: string}>;
  logout: () => Promise<void>;
  refreshUserData: () => Promise<void>;
  clearCache: () => Promise<void>;
  getAuthStats: () => Promise<any>;
}

/**
 * Enhanced authentication hook with intelligent caching and offline support
 */
export const useEnhancedAuth = (): AuthState & AuthActions => {
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    isInitializing: true,
    user: null,
    business: null,
    userType: null,
    isOnline: true,
    hasOfflineData: false,
    lastSync: null,
    error: null,
  });

  const authInstance = auth();
  const queryClient = useQueryClient();
  const {setUser, setBusiness, resetUser} = useUserStore();
  
  // Use offline user data hook
  const offlineUserData = useOfflineUserData();

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  // Monitor network state
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(networkState => {
      const isOnline = !!(networkState.isConnected && networkState.isInternetReachable);
      setState(prev => ({...prev, isOnline}));
    });

    return unsubscribe;
  }, []);

  // Sync with offline user data
  useEffect(() => {
    setState(prev => ({
      ...prev,
      user: offlineUserData.userData,
      business: offlineUserData.businessData,
      userType: offlineUserData.userType,
      hasOfflineData: offlineUserData.hasOfflineData,
      lastSync: offlineUserData.lastSync,
      isLoading: offlineUserData.isLoading,
    }));
  }, [offlineUserData]);

  /**
   * Initialize authentication state with cached data
   */
  const initializeAuth = useCallback(async () => {
    try {
      setState(prev => ({...prev, isInitializing: true}));

      // Check if user is already authenticated
      const currentUser = auth().currentUser;
      const isAuthenticated = !!(currentUser && currentUser.emailVerified);

      if (isAuthenticated) {
        console.log('🔐 User already authenticated, loading cached data...');
        
        // Try to load cached user data first for instant UI
        const cachedAuthState = await OfflineUserStorage.getStoredAuthState();
        const cachedUserData = await OfflineUserStorage.getStoredUserData();
        const cachedBusinessData = await OfflineUserStorage.getStoredBusinessData();
        const cachedUserType = await OfflineUserStorage.getStoredUserType();

        if (cachedUserData || cachedBusinessData) {
          console.log('📱 Found cached user data, updating UI immediately');
          
          setState(prev => ({
            ...prev,
            isAuthenticated: true,
            user: cachedUserData,
            business: cachedBusinessData,
            userType: cachedUserType,
            hasOfflineData: true,
            isLoading: false,
          }));

          // Update Zustand store
          if (cachedUserData) setUser(cachedUserData);
          if (cachedBusinessData) setBusiness(cachedBusinessData);
        }
      } else {
        console.log('🔐 User not authenticated');
        setState(prev => ({
          ...prev,
          isAuthenticated: false,
          isLoading: false,
        }));
      }

      setState(prev => ({...prev, isInitializing: false}));
    } catch (error) {
      console.error('❌ Error initializing auth:', error);
      setState(prev => ({
        ...prev,
        isInitializing: false,
        isLoading: false,
        error: 'Failed to initialize authentication',
      }));
    }
  }, [auth, setUser, setBusiness]);

  /**
   * Enhanced login with immediate caching
   */
  const login = useCallback(async (email: string, password: string): Promise<{success: boolean; error?: string}> => {
    try {
      setState(prev => ({...prev, isLoading: true, error: null}));

      // Import FirebaseAuth dynamically to avoid circular dependencies
      const FirebaseAuth = (await import('~services/FirebaseAuthService')).default;
      
      const result = await FirebaseAuth.signInEmailPassword(email, password, () => {});
      
      if (result?.error) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error,
        }));
        return {success: false, error: result.error};
      }

      if (result?.user) {
        console.log('✅ Login successful, caching auth state...');
        
        // Immediately cache auth state
        await OfflineUserStorage.storeAuthState({
          isAuthenticated: true,
          userId: result.user.uid,
          userType: null, // Will be updated when user data is fetched
        });

        setState(prev => ({
          ...prev,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        }));

        return {success: true};
      }

      return {success: false, error: 'Login failed'};
    } catch (error: any) {
      const errorMessage = error.message || 'Login failed';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return {success: false, error: errorMessage};
    }
  }, []);

  /**
   * Enhanced logout with cache cleanup
   */
  const logout = useCallback(async (): Promise<void> => {
    try {
      setState(prev => ({...prev, isLoading: true}));

      // Import FirebaseAuth dynamically
      const FirebaseAuth = (await import('~services/FirebaseAuthService')).default;
      await FirebaseAuth.logOut();

      // Clear all cached data
      await OfflineUserStorage.clearAllData();
      
      // Clear React Query cache
      queryClient.clear();
      
      // Reset Zustand store
      resetUser();

      setState({
        isAuthenticated: false,
        isLoading: false,
        isInitializing: false,
        user: null,
        business: null,
        userType: null,
        isOnline: state.isOnline,
        hasOfflineData: false,
        lastSync: null,
        error: null,
      });

      console.log('✅ Logout completed, all data cleared');
    } catch (error) {
      console.error('❌ Error during logout:', error);
      setState(prev => ({...prev, isLoading: false}));
    }
  }, [queryClient, resetUser, state.isOnline]);

  /**
   * Refresh user data from server
   */
  const refreshUserData = useCallback(async (): Promise<void> => {
    try {
      if (!state.isAuthenticated || !state.isOnline) {
        console.log('⚠️ Cannot refresh user data: not authenticated or offline');
        return;
      }

      setState(prev => ({...prev, isLoading: true}));

      // Force refresh offline user data
      if (offlineUserData.forceRefresh) {
        await offlineUserData.forceRefresh();
      }

      setState(prev => ({...prev, isLoading: false}));
      console.log('✅ User data refreshed');
    } catch (error) {
      console.error('❌ Error refreshing user data:', error);
      setState(prev => ({...prev, isLoading: false}));
    }
  }, [state.isAuthenticated, state.isOnline, offlineUserData]);

  /**
   * Clear all authentication cache
   */
  const clearCache = useCallback(async (): Promise<void> => {
    try {
      await OfflineUserStorage.clearAllData();
      
      // Clear relevant React Query cache
      queryClient.removeQueries(['userType']);
      queryClient.removeQueries(['userAccount']);
      queryClient.removeQueries(['businessAccount']);

      setState(prev => ({
        ...prev,
        hasOfflineData: false,
        lastSync: null,
      }));

      console.log('✅ Authentication cache cleared');
    } catch (error) {
      console.error('❌ Error clearing cache:', error);
    }
  }, [queryClient]);

  /**
   * Get authentication statistics
   */
  const getAuthStats = useCallback(async () => {
    try {
      const storageStats = await OfflineUserStorage.getStorageStats();
      const lastSync = await OfflineUserStorage.getLastSync();

      return {
        isAuthenticated: state.isAuthenticated,
        hasOfflineData: state.hasOfflineData,
        userType: state.userType,
        isOnline: state.isOnline,
        lastSync,
        storage: storageStats,
        cache: {
          reactQuery: {
            userQueries: queryClient.getQueryCache().getAll().filter(query =>
              query.queryKey.some(key => 
                typeof key === 'string' && 
                (key.includes('user') || key.includes('business'))
              )
            ).length,
          },
        },
      };
    } catch (error) {
      console.error('❌ Error getting auth stats:', error);
      return null;
    }
  }, [state, queryClient]);

  // Monitor authentication state changes
  useEffect(() => {
    const unsubscribe = auth().onAuthStateChanged(async (user) => {
      if (user && user.emailVerified) {
        if (!state.isAuthenticated) {
          console.log('🔐 User authenticated via Firebase listener');
          setState(prev => ({...prev, isAuthenticated: true}));
        }
      } else {
        if (state.isAuthenticated) {
          console.log('🔐 User unauthenticated via Firebase listener');
          setState(prev => ({
            ...prev,
            isAuthenticated: false,
            user: null,
            business: null,
            userType: null,
          }));
        }
      }
    });

    return unsubscribe;
  }, [auth, state.isAuthenticated]);

  return {
    // State
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    isInitializing: state.isInitializing,
    user: state.user,
    business: state.business,
    userType: state.userType,
    isOnline: state.isOnline,
    hasOfflineData: state.hasOfflineData,
    lastSync: state.lastSync,
    error: state.error,

    // Actions
    login,
    logout,
    refreshUserData,
    clearCache,
    getAuthStats,
  };
};
