import {useCallback, useRef, useMemo} from 'react';
import {Event} from '~types/api/event';
import haversine from 'haversine';
import {RectangularBounds} from '../../Utils/cache/rectangularBounds';
import analytics from '@react-native-firebase/analytics';

interface InMemoryEventCacheOptions {
  maxCacheAge?: number; // in minutes
  maxCacheEntries?: number;
  coordinatePrecision?: number; // decimal places for coordinate rounding
}

interface CacheEntry {
  events: Event[];
  timestamp: number;
  center: {latitude: number; longitude: number};
  radius: number;
  filters: any;
  bounds?: RectangularBounds; // Add rectangular bounds for smart caching
}

interface CacheStats {
  totalEntries: number;
  totalEvents: number;
  cacheHits: number;
  cacheMisses: number;
  oldestEntry: number;
  newestEntry: number;
}

/**
 * In-memory coordinate-based event cache for optimal map performance
 * Caches events by coordinate regions with configurable precision
 */
export const useInMemoryEventCache = (options: InMemoryEventCacheOptions = {}) => {
  const {
    maxCacheAge = 30, // 30 minutes default
    maxCacheEntries = 100,
    coordinatePrecision = 3, // ~100m precision
  } = options;

  // Cache storage - using Map for better performance
  const cache = useRef<Map<string, CacheEntry>>(new Map());
  const stats = useRef<CacheStats>({
    totalEntries: 0,
    totalEvents: 0,
    cacheHits: 0,
    cacheMisses: 0,
    oldestEntry: Date.now(),
    newestEntry: Date.now(),
  });

  /**
   * Generate cache key from coordinates and filters
   */
  const generateCacheKey = useCallback(
    (center: {latitude: number; longitude: number}, radius: number, filters?: any): string => {
      // Round coordinates to specified precision for consistent caching
      const lat = Number(center.latitude.toFixed(coordinatePrecision));
      const lng = Number(center.longitude.toFixed(coordinatePrecision));
      const roundedRadius = Math.round(radius);

      // Normalize filters to ensure consistent cache keys
      const normalizedFilters = filters
        ? {
            isForKids: filters.isForKids || false,
            selectedTimeframe: filters.selectedTimeframe || null,
            globalInputValue: filters.globalInputValue || '',
            isNeighbourhood: filters.isNeighbourhood || false,
          }
        : null;

      // Include filters in cache key for proper isolation
      const filterKey = normalizedFilters ? JSON.stringify(normalizedFilters) : 'no-filters';

      const cacheKey = `${lat},${lng},${roundedRadius},${filterKey}`;
      // Removed excessive logging to prevent performance issues
      return cacheKey;
    },
    [coordinatePrecision],
  );

  /**
   * Check if cache entry is still valid
   */
  const isCacheEntryValid = useCallback(
    (entry: CacheEntry): boolean => {
      const ageInMinutes = (Date.now() - entry.timestamp) / (1000 * 60);
      return ageInMinutes < maxCacheAge;
    },
    [maxCacheAge],
  );

  /**
   * Clean up expired cache entries
   */
  const cleanupExpiredEntries = useCallback(() => {
    const currentCache = cache.current;
    const keysToDelete: string[] = [];

    currentCache.forEach((entry, key) => {
      if (!isCacheEntryValid(entry)) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => {
      currentCache.delete(key);
    });

    if (keysToDelete.length > 0) {
      console.log(`🧹 [CACHE] Cleaned up ${keysToDelete.length} expired entries`);
    }
  }, [isCacheEntryValid]);

  /**
   * Enforce cache size limits
   */
  const enforceCacheLimits = useCallback(() => {
    const currentCache = cache.current;

    if (currentCache.size <= maxCacheEntries) {
      return;
    }

    // Remove oldest entries first
    const entries = Array.from(currentCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

    const entriesToRemove = entries.slice(0, currentCache.size - maxCacheEntries);
    entriesToRemove.forEach(([key]) => {
      currentCache.delete(key);
    });

    console.log(`📦 [CACHE] Removed ${entriesToRemove.length} entries to enforce size limit`);
  }, [maxCacheEntries]);

  /**
   * Store events in cache
   */
  const storeEvents = useCallback(
    (
      center: {latitude: number; longitude: number},
      radius: number,
      events: Event[],
      filters?: any,
      bounds?: RectangularBounds,
    ): void => {
      const cacheKey = generateCacheKey(center, radius, filters);

      // Deduplicate events before storing
      const seenEventIds = new Set<string>();
      const deduplicatedEvents = events.filter(event => {
        if (seenEventIds.has(event.event_id)) {
          return false; // Skip duplicate
        }
        seenEventIds.add(event.event_id);
        return true;
      });

      // Check if we already have this cache entry and decide whether to merge or replace
      const existingEntry = cache.current.get(cacheKey);
      let finalEvents = deduplicatedEvents;

      if (existingEntry && existingEntry.events.length > 0) {
        // Check if existing cache is too old (older than 10 minutes) - refresh completely
        const cacheAgeMinutes = (Date.now() - existingEntry.timestamp) / (1000 * 60);

        if (cacheAgeMinutes > 10) {
          console.log(`🔄 [CACHE] Cache is ${cacheAgeMinutes.toFixed(1)} minutes old, replacing with fresh events`);
          finalEvents = deduplicatedEvents; // Replace with fresh events
        } else {
          // Merge with existing events, avoiding duplicates
          const existingEventIds = new Set(existingEntry.events.map(e => e.event_id));
          const newEvents = deduplicatedEvents.filter(event => !existingEventIds.has(event.event_id));

          if (newEvents.length > 0) {
            finalEvents = [...existingEntry.events, ...newEvents];
            console.log(
              `🔄 [CACHE] Merged ${newEvents.length} new events with ${existingEntry.events.length} existing events`,
            );
          } else {
            console.log(`📋 [CACHE] No new events to add, keeping existing ${existingEntry.events.length} events`);
            // Update timestamp even if no new events to prevent unnecessary refetches
            existingEntry.timestamp = Date.now();
            return;
          }
        }
      }

      const entry: CacheEntry = {
        events: finalEvents, // Use deduplicated and merged events
        timestamp: Date.now(),
        center: {...center},
        radius,
        filters: filters ? {...filters} : null,
        bounds: bounds ? {...bounds} : undefined,
      };

      cache.current.set(cacheKey, entry);

      // Update stats
      stats.current.totalEntries = cache.current.size;
      stats.current.totalEvents = finalEvents.length;
      stats.current.newestEntry = entry.timestamp;

      // Cleanup and enforce limits
      cleanupExpiredEntries();
      enforceCacheLimits();

      const duplicatesRemoved = events.length - deduplicatedEvents.length;
      console.log(
        `💾 [CACHE] Stored ${finalEvents.length} events for key: ${cacheKey}${bounds ? ' with bounds' : ''}${duplicatesRemoved > 0 ? ` (removed ${duplicatesRemoved} duplicates)` : ''}`,
      );

      // Also log to native console for debugging
      console.warn(`CACHE STORE: ${finalEvents.length} events stored`);
    },
    [generateCacheKey, cleanupExpiredEntries, enforceCacheLimits],
  );

  /**
   * Get cached events for a coordinate region
   */
  const getCachedEvents = useCallback(
    (center: {latitude: number; longitude: number}, radius: number, filters?: any): Event[] | null => {
      const cacheKey = generateCacheKey(center, radius, filters);
      const entry = cache.current.get(cacheKey);

      if (!entry) {
        stats.current.cacheMisses++;
        console.log(`❌ [CACHE] Cache miss for key: ${cacheKey}`);
        console.warn(`CACHE MISS: No entry found for key`);

        // Track cache miss in Google Analytics
        analytics().logEvent('cache_miss', {
          cache_type: 'event_cache',
          radius: radius,
          has_filters: !!filters,
          cache_size: cache.current.size,
          miss_reason: 'no_entry',
        });

        return null;
      }

      if (!isCacheEntryValid(entry)) {
        cache.current.delete(cacheKey);
        stats.current.cacheMisses++;
        console.log(`⏰ [CACHE] Cache expired for key: ${cacheKey}`);

        // Track cache miss due to expiration in Google Analytics
        analytics().logEvent('cache_miss', {
          cache_type: 'event_cache',
          radius: radius,
          has_filters: !!filters,
          cache_size: cache.current.size,
          miss_reason: 'expired',
          cache_age_minutes: Math.round((Date.now() - entry.timestamp) / (1000 * 60)),
        });

        return null;
      }

      stats.current.cacheHits++;
      console.log(`✅ [CACHE] Cache hit for key: ${cacheKey} (${entry.events.length} events)`);
      console.warn(`CACHE HIT: ${entry.events.length} events found`);

      // Track cache hit in Google Analytics
      analytics().logEvent('cache_hit', {
        cache_type: 'event_cache',
        radius: radius,
        event_count: entry.events.length,
        has_filters: !!filters,
        cache_age_minutes: Math.round((Date.now() - entry.timestamp) / (1000 * 60)),
        cache_size: cache.current.size,
      });

      return entry.events;
    },
    [generateCacheKey, isCacheEntryValid],
  );

  /**
   * Find cached events within a larger radius (for viewport expansion)
   */
  const findNearbyEvents = useCallback(
    (center: {latitude: number; longitude: number}, radius: number, filters?: any): Event[] => {
      const allEvents: Event[] = [];
      const seenEventIds = new Set<string>();

      cache.current.forEach(entry => {
        // Check if entry is valid and filters match
        if (!isCacheEntryValid(entry)) {
          return;
        }

        // Simple filter matching (could be enhanced)
        if (filters && JSON.stringify(entry.filters) !== JSON.stringify(filters)) {
          return;
        }

        // Check if cached region overlaps with requested region
        const distance = haversine(center, entry.center, {unit: 'km'});
        const maxDistance = radius + entry.radius;

        if (distance <= maxDistance) {
          entry.events.forEach(event => {
            if (!seenEventIds.has(event.event_id)) {
              // Check if event is actually within the requested radius
              const eventDistance = haversine(
                center,
                {
                  latitude: event.coords.lat,
                  longitude: event.coords.long,
                },
                {unit: 'km'},
              );

              if (eventDistance <= radius) {
                allEvents.push(event);
                seenEventIds.add(event.event_id);
              }
            }
          });
        }
      });

      if (allEvents.length > 0) {
        console.log(`🔍 [CACHE] Found ${allEvents.length} nearby events from cache`);
      }

      return allEvents;
    },
    [isCacheEntryValid],
  );

  /**
   * Clear all cached events
   */
  const clearCache = useCallback(() => {
    cache.current.clear();
    stats.current = {
      totalEntries: 0,
      totalEvents: 0,
      cacheHits: 0,
      cacheMisses: 0,
      oldestEntry: Date.now(),
      newestEntry: Date.now(),
    };
    console.log('🗑️ [CACHE] Cache cleared');
  }, []);

  /**
   * Get cache statistics
   */
  const getCacheStats = useCallback((): CacheStats => {
    // Update current stats
    const currentStats = {...stats.current};
    currentStats.totalEntries = cache.current.size;

    if (cache.current.size > 0) {
      const timestamps = Array.from(cache.current.values()).map(entry => entry.timestamp);
      currentStats.oldestEntry = Math.min(...timestamps);
      currentStats.newestEntry = Math.max(...timestamps);
    }

    return currentStats;
  }, []);

  /**
   * Send cache performance summary to Google Analytics
   */
  const sendCachePerformanceToAnalytics = useCallback(() => {
    const stats = getCacheStats();
    const totalRequests = stats.cacheHits + stats.cacheMisses;
    const hitRate = totalRequests > 0 ? (stats.cacheHits / totalRequests) * 100 : 0;

    analytics().logEvent('cache_performance_summary', {
      cache_type: 'event_cache',
      total_entries: stats.totalEntries,
      total_events: stats.totalEvents,
      cache_hits: stats.cacheHits,
      cache_misses: stats.cacheMisses,
      hit_rate_percentage: Math.round(hitRate * 100) / 100, // Round to 2 decimal places
      total_requests: totalRequests,
      cache_age_hours: Math.round((Date.now() - stats.oldestEntry) / (1000 * 60 * 60)),
    });

    console.log(`📊 [CACHE ANALYTICS] Performance summary sent - Hit Rate: ${hitRate.toFixed(1)}%`);
  }, [getCacheStats]);

  /**
   * Refresh/invalidate cache entries for a specific location and radius
   */
  const refreshCacheEntry = useCallback(
    (center: {latitude: number; longitude: number}, radius: number, filters?: any): void => {
      const cacheKey = generateCacheKey(center, radius, filters);

      if (cache.current.has(cacheKey)) {
        cache.current.delete(cacheKey);
        console.log(`🔄 [CACHE] Refreshed cache entry for key: ${cacheKey}`);
      }
    },
    [generateCacheKey],
  );

  /**
   * Check if current location is within any cached rectangular bounds
   */
  const isWithinCachedBounds = useCallback(
    (
      currentLocation: {latitude: number; longitude: number},
      radius: number,
      filters?: any,
    ): {withinBounds: boolean; cachedEvents: Event[] | null} => {
      // Check all cache entries for matching bounds
      for (const [key, entry] of cache.current.entries()) {
        // Check if entry is valid
        if (!isCacheEntryValid(entry)) {
          continue;
        }

        // Check if filters match
        if (filters && JSON.stringify(entry.filters) !== JSON.stringify(filters)) {
          continue;
        }

        // Check if radius matches (allow small tolerance)
        const radiusMatches = Math.abs(entry.radius - radius) <= 5; // 5km tolerance
        if (!radiusMatches) {
          continue;
        }

        // Check if entry has bounds and current location is within them
        if (entry.bounds) {
          const withinBounds =
            currentLocation.latitude >= entry.bounds.south &&
            currentLocation.latitude <= entry.bounds.north &&
            currentLocation.longitude >= entry.bounds.west &&
            currentLocation.longitude <= entry.bounds.east;

          if (withinBounds) {
            console.log(
              `📍 [BOUNDS] Current location is within cached bounds, using cached events (${entry.events.length} events)`,
            );

            // Track smart cache hit in Google Analytics
            analytics().logEvent('smart_cache_hit', {
              cache_type: 'rectangular_bounds',
              radius: radius,
              event_count: entry.events.length,
              has_filters: !!filters,
              cache_age_minutes: Math.round((Date.now() - entry.timestamp) / (1000 * 60)),
            });

            return {withinBounds: true, cachedEvents: entry.events};
          }
        }
      }

      console.log(`📍 [BOUNDS] Current location is outside all cached bounds, need to fetch`);

      // Track smart cache miss in Google Analytics
      analytics().logEvent('smart_cache_miss', {
        cache_type: 'rectangular_bounds',
        radius: radius,
        has_filters: !!filters,
        cache_size: cache.current.size,
      });

      return {withinBounds: false, cachedEvents: null};
    },
    [isCacheEntryValid],
  );

  /**
   * Remove events from cache that are outside viewport
   */
  const cullEventsOutsideViewport = useCallback(
    (center: {latitude: number; longitude: number}, radius: number): void => {
      const keysToRemove: string[] = [];

      cache.current.forEach((entry, key) => {
        const distance = haversine(center, entry.center, {unit: 'km'});
        const maxDistance = radius * 2; // Keep cache for 2x the current radius

        if (distance > maxDistance) {
          keysToRemove.push(key);
        }
      });

      keysToRemove.forEach(key => {
        cache.current.delete(key);
      });

      if (keysToRemove.length > 0) {
        console.log(`✂️ [CACHE] Culled ${keysToRemove.length} entries outside viewport`);
      }
    },
    [],
  );

  return useMemo(
    () => ({
      storeEvents,
      getCachedEvents,
      findNearbyEvents,
      clearCache,
      getCacheStats,
      cullEventsOutsideViewport,
      isWithinCachedBounds,
      refreshCacheEntry,
      sendCachePerformanceToAnalytics,
    }),
    [
      storeEvents,
      getCachedEvents,
      findNearbyEvents,
      clearCache,
      getCacheStats,
      cullEventsOutsideViewport,
      isWithinCachedBounds,
      refreshCacheEntry,
      sendCachePerformanceToAnalytics,
    ],
  );
};
