import {useEffect, useRef} from 'react';
import {useQueryClient} from 'react-query';
import {Event} from '~types/api/event';
import {queryKeys} from '~Utils/performance/queryConfig';
import {performanceMonitor} from '~Utils/performance/performanceMonitor';
import FirebaseAuth from '~services/FirebaseAuthService';
import {BASE_API_URL} from '@env';
interface UseMapEventPrefetchParams {
  events: Event[];
  enabled?: boolean;
  maxEventsToPrefetch?: number;
  prefetchDelay?: number;
  prefetchStrategy?: 'immediate' | 'delayed' | 'onHover';
}

interface PrefetchedEvent {
  eventId: number;
  timestamp: number;
  data?: any;
}

export const useMapEventPrefetch = ({
  events,
  enabled = true,
  maxEventsToPrefetch = 10,
  prefetchDelay = 1000,
  prefetchStrategy = 'delayed',
}: UseMapEventPrefetchParams) => {
  const queryClient = useQueryClient();
  const prefetchedEvents = useRef<Map<number, PrefetchedEvent>>(new Map());
  const prefetchTimeouts = useRef<Map<number, NodeJS.Timeout>>(new Map());

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      prefetchTimeouts.current.forEach(timeout => clearTimeout(timeout));
      prefetchTimeouts.current.clear();
    };
  }, []);

  // Prefetch event details
  const prefetchEventDetails = async (eventId: number) => {
    try {
      const startTime = Date.now();

      // Check if already prefetched recently (within 5 minutes)
      const cached = prefetchedEvents.current.get(eventId);
      if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
        return cached.data;
      }

      // Check if already in React Query cache
      const existingData = queryClient.getQueryData(queryKeys.events.detail(eventId));
      if (existingData) {
        prefetchedEvents.current.set(eventId, {
          eventId,
          timestamp: Date.now(),
          data: existingData,
        });
        return existingData;
      }

      // Prefetch the event details
      const data = await queryClient.fetchQuery({
        queryKey: queryKeys.events.detail(eventId),
        queryFn: async () => {
          const token = await FirebaseAuth.getAuthToken();
          const baseURL = token ? `${BASE_API_URL}events/${eventId}` : `${BASE_API_URL}public-events/${eventId}`;

          const response = await fetch(baseURL, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              Authorization: token,
            },
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch event ${eventId}`);
          }

          return response.json();
        },
        staleTime: 1000 * 60 * 10, // 10 minutes
        cacheTime: 1000 * 60 * 30, // 30 minutes
      });

      // Store in our local cache
      prefetchedEvents.current.set(eventId, {
        eventId,
        timestamp: Date.now(),
        data,
      });

      const prefetchTime = Date.now() - startTime;
      performanceMonitor.recordPrefetchSuccess();

      return data;
    } catch (error) {
      console.warn(`Failed to prefetch event ${eventId}:`, error);
      performanceMonitor.recordError();
      return null;
    }
  };

  // Prefetch event comments and attendees
  const prefetchEventExtras = async (eventId: number) => {
    try {
      // Prefetch comments
      queryClient.prefetchQuery({
        queryKey: ['eventComments', eventId],
        queryFn: async () => {
          const token = await FirebaseAuth.getAuthToken();
          const response = await fetch(`${BASE_API_URL}events/${eventId}/comments`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              Authorization: token,
            },
          });
          return response.ok ? response.json() : [];
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
      });

      // Prefetch attendees
      queryClient.prefetchQuery({
        queryKey: ['eventAttendees', eventId],
        queryFn: async () => {
          const token = await FirebaseAuth.getAuthToken();
          const response = await fetch(`${BASE_API_URL}events/${eventId}/attendees`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              Authorization: token,
            },
          });
          return response.ok ? response.json() : [];
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
      });
    } catch (error) {
      console.warn(`Failed to prefetch extras for event ${eventId}:`, error);
    }
  };

  // Main prefetch logic
  useEffect(() => {
    if (!enabled || !events || events.length === 0) {
      return;
    }

    // Only prefetch if we have 10 or fewer events
    if (events.length > maxEventsToPrefetch) {
      console.log(`🚫 Skipping prefetch: ${events.length} events > ${maxEventsToPrefetch} limit`);
      return;
    }

    console.log(`🚀 Starting prefetch for ${events.length} map events`);

    const prefetchEvents = async () => {
      // Clear existing timeouts
      prefetchTimeouts.current.forEach(timeout => clearTimeout(timeout));
      prefetchTimeouts.current.clear();

      for (let i = 0; i < events.length; i++) {
        const event = events[i];
        if (!event.event_id) {
          continue;
        }

        const eventId = event.event_id;

        if (prefetchStrategy === 'immediate') {
          // Prefetch immediately
          prefetchEventDetails(eventId);

          // Prefetch extras with a small delay
          setTimeout(() => prefetchEventExtras(eventId), 500 + i * 100);
        } else if (prefetchStrategy === 'delayed') {
          // Stagger prefetching to avoid overwhelming the API
          const delay = prefetchDelay + i * 200; // 200ms between each prefetch

          const timeout = setTimeout(() => {
            prefetchEventDetails(eventId);

            // Prefetch extras after main details
            setTimeout(() => prefetchEventExtras(eventId), 300);
          }, delay);

          prefetchTimeouts.current.set(eventId, timeout);
        }
      }
    };

    prefetchEvents();

    // Cleanup function
    return () => {
      prefetchTimeouts.current.forEach(timeout => clearTimeout(timeout));
      prefetchTimeouts.current.clear();
    };
  }, [events, enabled, maxEventsToPrefetch, prefetchDelay, prefetchStrategy]);

  // Function to manually trigger prefetch for a specific event (for onHover strategy)
  const prefetchEvent = (eventId: number) => {
    if (prefetchStrategy === 'onHover') {
      prefetchEventDetails(eventId);
      setTimeout(() => prefetchEventExtras(eventId), 200);
    }
  };

  // Function to check if an event is prefetched
  const isPrefetched = (eventId: number): boolean => {
    const cached = prefetchedEvents.current.get(eventId);
    if (!cached) {
      return false;
    }

    // Check if cache is still valid (within 5 minutes)
    return Date.now() - cached.timestamp < 5 * 60 * 1000;
  };

  // Get prefetched data for an event
  const getPrefetchedData = (eventId: number) => {
    const cached = prefetchedEvents.current.get(eventId);
    return cached?.data || null;
  };

  // Get prefetch statistics
  const getPrefetchStats = () => {
    const totalPrefetched = prefetchedEvents.current.size;
    const validPrefetched = Array.from(prefetchedEvents.current.values()).filter(
      item => Date.now() - item.timestamp < 5 * 60 * 1000,
    ).length;

    return {
      totalPrefetched,
      validPrefetched,
      shouldPrefetch: events.length <= maxEventsToPrefetch,
      eventsCount: events.length,
    };
  };

  return {
    prefetchEvent,
    isPrefetched,
    getPrefetchedData,
    getPrefetchStats,
  };
};
