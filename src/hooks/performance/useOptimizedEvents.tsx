import {useInfiniteQuery, useQueryClient} from 'react-query';
import {useCallback, useEffect, useMemo} from 'react';
import axios from 'axios';
import {BASE_API_URL} from '@env';
import FirebaseAuth from '~services/FirebaseAuthService';
import {GetEventsParams} from '~types/events';
import {queryKeys, prefetchHelpers} from '~Utils/performance/queryConfig';
import {requestDeduplicator} from '~Utils/performance/searchOptimization';
import {optimizeEventParams, filterEvents, deduplicateEvents, performanceMonitor} from '~Utils/performance/eventFiltering';
import {useSmartEventCache} from './useSmartEventCache';
const NUMBER_OF_EVENTS_PER_PAGE = 100; // Increased limit to fetch more events

interface UseOptimizedEventsParams extends Omit<GetEventsParams, 'offset' | 'limit'> {
  enabled?: boolean;
  prefetchNext?: boolean; // Automatically prefetch next page
  backgroundRefetch?: boolean; // Enable background refetching
}

export const useOptimizedEvents = (params: UseOptimizedEventsParams) => {
  const {
    tab,
    order_by,
    order_dir,
    distance_km,
    event_age_group,
    enabled = true,
    q,
    filter_my_event_type,
    filter_type,
    user_id,
    timeframe,
    isNeighbourhood = false,
    prefetchNext = true,
    backgroundRefetch = true,
  } = params;

  const queryClient = useQueryClient();

  // DISABLED: Smart cache might be interfering with data flow
  // const smartCache = useSmartEventCache({
  //   excludePastEvents: true,
  //   maxCacheAge: 30,
  //   prefetchRelated: true,
  //   enableOfflineSync: true,
  // });
  const smartCache = {
    storeEvents: async () => {
      console.log('🚫 [SMART CACHE] Disabled - not storing events');
    },
  };

  // Create stable query key
  const queryKey = useMemo(() => {
    const filters = {
      tab: isNeighbourhood ? 'neighbourhood_event' : tab,
      order_by,
      order_dir,
      distance_km: isNeighbourhood ? Math.max(distance_km, 500) : distance_km, // Cap neighbourhood events to max 500km instead of unlimited
      event_age_group,
      q,
      filter_my_event_type,
      filter_type,
      user_id,
      timeframe,
    };
    return queryKeys.events.list(filters);
  }, [
    tab,
    order_by,
    order_dir,
    distance_km,
    event_age_group,
    q,
    filter_my_event_type,
    filter_type,
    user_id,
    timeframe,
    isNeighbourhood,
  ]);

  // Optimized fetch function with deduplication
  const fetchEvents = useCallback(
    async ({pageParam = 0}) => {
      const offset = pageParam * NUMBER_OF_EVENTS_PER_PAGE;
      const limit = NUMBER_OF_EVENTS_PER_PAGE;

      let requestParams: GetEventsParams = {
        limit,
        offset,
        tab: isNeighbourhood ? 'neighbourhood_event' : tab,
        order_by,
        order_dir,
        distance_km: isNeighbourhood ? Math.max(distance_km, 500) : distance_km, // Cap neighbourhood events to max 500km instead of unlimited
        event_age_group,
        q,
        filter_my_event_type,
        filter_type,
        user_id,
        timeframe,
      };

      // Apply backend filtering optimizations - SKIP for MY_EVENTS to show all events
      if (tab !== 'my_events') {
        requestParams = optimizeEventParams(requestParams);
      } else {
        console.log('🔍 [MY_EVENTS] Skipping optimizeEventParams to show all events (past and future)');
      }

      // Add cache buster to force fresh API call
      requestParams.cache_buster = Date.now();

      // Log detailed parameters before API call
      console.log(`🔍 [API] Fetching events with optimized params:`, JSON.stringify(requestParams, null, 2));

      // Create deduplication key - include timestamp to reduce aggressive deduplication
      const dedupeKey = `events-${JSON.stringify(requestParams)}-${Math.floor(Date.now() / 10000)}`;

      // Temporarily disable deduplication to debug the issue
      const executeRequest = async () => {
        
        const token = await FirebaseAuth.getAuthToken();
        console.log(BASE_API_URL, token, 'BASE_API_URLBASE_API_URL');
        const config = {
          headers: {Authorization: token, Accept: 'application/json'},
        };

        try {
          const response = await axios.get(BASE_API_URL + 'events/', {
            params: requestParams,
            ...config,
          });

          console.log(response.data, 'response.data');
          

          // DISABLED: Client-side filtering should not be needed since API already filters
          // The API should return only valid, non-expired events
          if (response.data.items) {
            const startFilterTime = Date.now();

            // Only deduplicate events, skip date filtering since API handles it
            const deduplicatedEvents = deduplicateEvents(response.data.items);

            const filterTime = Date.now() - startFilterTime;
            const duplicatesRemoved = response.data.items.length - deduplicatedEvents.length;

            // Record performance metrics
            performanceMonitor.recordEventFiltering(
              response.data.items.length,
              0, // No events filtered out by date
              duplicatesRemoved,
              filterTime,
            );

            response.data.items = deduplicatedEvents;

            // Store in smart cache
            await smartCache.storeEvents(
              queryKey,
              deduplicatedEvents,
              requestParams,
              deduplicatedEvents.length === NUMBER_OF_EVENTS_PER_PAGE,
            );
          }

          return response.data;
        } catch (error) {
          // Enhanced error logging for debugging
          console.error(`🚨 [API ERROR] Request failed with params:`, JSON.stringify(requestParams, null, 2));
          console.error(`🚨 [API ERROR] Error details:`, {
            status: error?.response?.status,
            statusText: error?.response?.statusText,
            data: error?.response?.data,
            message: error?.message,
            config: {
              url: error?.config?.url,
              method: error?.config?.method,
              params: error?.config?.params,
            },
          });
          console.error('API Error:', error.message);

          throw error;
        }
      };

      return executeRequest();
    },
    [
      tab,
      order_by,
      order_dir,
      distance_km,
      event_age_group,
      q,
      filter_my_event_type,
      filter_type,
      user_id,
      timeframe,
      isNeighbourhood,
    ],
  );

  // Enhanced infinite query with performance optimizations
  const query = useInfiniteQuery(queryKey, fetchEvents, {
    getNextPageParam: (lastPage, pages) => {
      const items = lastPage?.data || lastPage?.items || [];
      const nextPage = items.length === NUMBER_OF_EVENTS_PER_PAGE ? pages.length : undefined;
      return nextPage;
    },
    enabled,

    // Performance optimizations
    cacheTime: 1000 * 60 * 30, // 30 minutes cache
    staleTime: 1000 * 60 * 5, // 5 minutes stale time
    keepPreviousData: true,
    refetchOnWindowFocus: backgroundRefetch,
    refetchOnReconnect: true,

    // Optimize re-renders
    notifyOnChangeProps: 'tracked',

    // Background refetching for fresh data
    refetchInterval: backgroundRefetch ? 1000 * 60 * 5 : false, // 5 minutes
    refetchIntervalInBackground: false,
  });

  // Automatic next page prefetching
  useEffect(() => {
    if (prefetchNext && query.data && query.hasNextPage && !query.isFetchingNextPage) {
      const lastPage = query.data.pages[query.data.pages.length - 1];
      const items = lastPage?.data || lastPage?.items || [];
      if (items.length === NUMBER_OF_EVENTS_PER_PAGE) {
        // Prefetch next page after a short delay
        const timer = setTimeout(() => {
          query.fetchNextPage();
        }, 1000);
        return () => clearTimeout(timer);
      }
    }
  }, [query.data, query.hasNextPage, query.isFetchingNextPage, prefetchNext, query.fetchNextPage]);

  // Background prefetching of related data
  useEffect(() => {
    if (query.data && query.data.pages.length > 0) {
      const events = query.data.pages.flatMap(page => page.data || page.items || []);

      // Prefetch event details for first few events
      events.slice(0, 3).forEach(event => {
        if (event.event_id) {
          queryClient.prefetchQuery({
            queryKey: queryKeys.events.detail(event.event_id),
            queryFn: async () => {
              const token = await FirebaseAuth.getAuthToken();
              const response = await fetch(`${BASE_API_URL}events/${event.event_id}`, {
                headers: {Authorization: token, Accept: 'application/json'},
              });
              return response.json();
            },
            staleTime: 1000 * 60 * 10, // 10 minutes for event details
          });
        }
      });
    }
  }, [query.data, queryClient]);

  // Optimized refetch function
  const optimizedRefetch = useCallback(async () => {
    // Clear cache for this specific query
    await queryClient.invalidateQueries(queryKey);
    return query.refetch();
  }, [queryClient, queryKey, query.refetch]);

  // Optimized remove function
  const optimizedRemove = useCallback(() => {
    queryClient.removeQueries(queryKey);
  }, [queryClient, queryKey]);

  return {
    ...query,
    refetch: optimizedRefetch,
    remove: optimizedRemove,

    // Additional performance metrics
    cacheHit: query.isFetched && !query.isFetching,
    isStale: query.isStale,
  };
};
