import {useCallback, useEffect, useMemo} from 'react';
import {useQueryClient} from 'react-query';
import {Event} from '~types/api/event';
import {queryKeys} from '~Utils/performance/queryConfig';

interface SmartEventCacheOptions {
  excludePastEvents?: boolean;
  maxCacheAge?: number; // in minutes
  prefetchRelated?: boolean;
  enableOfflineSync?: boolean;
}

interface CachedEventData {
  events: Event[];
  timestamp: number;
  filters: any;
  hasMore: boolean;
}

/**
 * Smart event caching system that prevents unnecessary API calls
 * and implements intelligent filtering and prefetching
 */
export const useSmartEventCache = (options: SmartEventCacheOptions = {}) => {
  const {
    excludePastEvents = true,
    maxCacheAge = 30, // 30 minutes default
    prefetchRelated = true,
    enableOfflineSync = true,
  } = options;

  const queryClient = useQueryClient();

  /**
   * Filter out past events to prevent unnecessary data
   */
  const filterEvents = useCallback(
    (events: Event[]): Event[] => {
      if (!excludePastEvents) return events;

      const now = new Date();
      return events.filter(event => {
        if (!event.end_date) return true; // Keep events without end date

        const endDate = new Date(event.end_date);
        return endDate >= now; // Only keep future or ongoing events
      });
    },
    [excludePastEvents],
  );

  /**
   * Check if cached data is still valid
   */
  const isCacheValid = useCallback(
    (timestamp: number): boolean => {
      const now = Date.now();
      const maxAge = maxCacheAge * 60 * 1000; // Convert to milliseconds
      return now - timestamp < maxAge;
    },
    [maxCacheAge],
  );

  /**
   * Get cached events with smart filtering
   */
  const getCachedEvents = useCallback(
    async (queryKey: string[], filters?: any): Promise<Event[] | null> => {
      try {
        // First check React Query cache
        const cachedData = queryClient.getQueryData(queryKey);
        if (cachedData) {
          const events = extractEventsFromQueryData(cachedData);
          if (events.length > 0) {
            console.log(`📱 SmartEventCache: Found ${events.length} events in React Query cache`);
            return filterEvents(events);
          }
        }

        return null;
      } catch (error) {
        console.error('❌ SmartEventCache: Error getting cached events', error);
        return null;
      }
    },
    [queryClient, enableOfflineSync, filterEvents],
  );

  /**
   * Store events in cache with metadata
   */
  const storeEvents = useCallback(
    async (queryKey: string[], events: Event[], filters?: any, hasMore: boolean = false): Promise<void> => {
      try {
        // Filter events before storing
        const filteredEvents = filterEvents(events);

        // Prefetch related data if enabled
        if (prefetchRelated && filteredEvents.length > 0) {
          // Prefetch first 3 event details
          filteredEvents.slice(0, 3).forEach(event => {
            if (event.event_id) {
              queryClient.prefetchQuery({
                queryKey: queryKeys.events.detail(event.event_id),
                queryFn: async () => {
                  // This will be handled by the existing event detail hook
                  return null;
                },
                staleTime: 1000 * 60 * 10, // 10 minutes
              });
            }
          });
        }
      } catch (error) {
        console.error('SmartEventCache: Error storing events', error);
      }
    },
    [filterEvents, enableOfflineSync, prefetchRelated, queryClient],
  );

  /**
   * Invalidate cache for specific query
   */
  const invalidateCache = useCallback(
    async (queryKey: string[]): Promise<void> => {
      try {
        // Invalidate React Query cache
        await queryClient.invalidateQueries(queryKey);
      } catch (error) {
        console.error('SmartEventCache: Error invalidating cache', error);
      }
    },
    [queryClient, enableOfflineSync],
  );

  /**
   * Get cache statistics
   */
  const getCacheStats = useCallback(async () => {
    try {
      const reactQueryCache = queryClient.getQueryCache();
      const queries = reactQueryCache.getAll();
      const eventQueries = queries.filter(query =>
        query.queryKey.some(key => typeof key === 'string' && key.includes('event')),
      );

      return {
        reactQuery: {
          totalQueries: queries.length,
          eventQueries: eventQueries.length,
          cacheSize: eventQueries.reduce((size, query) => {
            const data = query.state.data;
            return size + (data ? JSON.stringify(data).length : 0);
          }, 0),
        },
      };
    } catch (error) {
      console.error('SmartEventCache: Error getting cache stats', error);
      return null;
    }
  }, [queryClient, enableOfflineSync]);

  /**
   * Cleanup old cache entries
   */
  const cleanupCache = useCallback(async (): Promise<void> => {
    try {
      // Cleanup React Query cache
      const reactQueryCache = queryClient.getQueryCache();
      const queries = reactQueryCache.getAll();

      queries.forEach(query => {
        const lastUpdated = query.state.dataUpdatedAt;
        if (lastUpdated && !isCacheValid(lastUpdated)) {
          queryClient.removeQueries(query.queryKey);
        }
      });
    } catch (error) {
      console.error('SmartEventCache: Error during cache cleanup', error);
    }
  }, [queryClient, enableOfflineSync, isCacheValid]);

  // Auto-cleanup on mount and periodically
  useEffect(() => {
    cleanupCache();

    // Cleanup every 30 minutes
    const interval = setInterval(cleanupCache, 30 * 60 * 1000);
    return () => clearInterval(interval);
  }, [cleanupCache]);

  // Helper functions
  const extractEventsFromQueryData = (queryData: any): Event[] => {
    if (!queryData) return [];

    // Handle infinite query data structure
    if (queryData.pages) {
      return queryData.pages.flatMap((page: any) => page.items || page.data || []);
    }

    // Handle regular query data structure
    if (Array.isArray(queryData)) {
      return queryData;
    }

    if (queryData.items) {
      return queryData.items;
    }

    if (queryData.data) {
      return queryData.data;
    }

    return [];
  };

  const getListTypeFromQueryKey = (queryKey: string[]): 'discover' | 'liked' | 'user' | 'nearby' | null => {
    const key = queryKey[0];
    if (typeof key === 'string') {
      if (key.includes('liked')) return 'liked';
      if (key.includes('Organise') || key.includes('user')) return 'user';
      if (key.includes('nearby')) return 'nearby';
      if (key.includes('events')) return 'discover';
    }
    return null;
  };

  return {
    // Core functions
    getCachedEvents,
    storeEvents,
    invalidateCache,

    // Utilities
    filterEvents,
    isCacheValid,
    getCacheStats,
    cleanupCache,

    // Configuration
    options: {
      excludePastEvents,
      maxCacheAge,
      prefetchRelated,
      enableOfflineSync,
    },
  };
};
