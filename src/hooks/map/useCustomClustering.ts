import {useMemo} from 'react';
import {Event} from '~types/api/event';

interface ClusterItem {
  id: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  events: Event[];
  isCluster: boolean;
  count: number;
}

interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

interface UseCustomClusteringProps {
  events: Event[];
  mapRegion: MapRegion;
  clusterRadius?: number; // in pixels
  minZoomForClustering?: number;
  maxClusterZoom?: number;
}

// Calculate distance between two coordinates in kilometers
const calculateDistance = (
  coord1: {latitude: number; longitude: number},
  coord2: {latitude: number; longitude: number},
): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = ((coord2.latitude - coord1.latitude) * Math.PI) / 180;
  const dLon = ((coord2.longitude - coord1.longitude) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((coord1.latitude * Math.PI) / 180) *
      Math.cos((coord2.latitude * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

// Calculate zoom level from latitude delta
const getZoomLevel = (latitudeDelta: number): number => {
  return Math.log2(360 / latitudeDelta);
};

// Convert pixel distance to coordinate distance (approximate)
const pixelToCoordinateDistance = (pixels: number, latitudeDelta: number): number => {
  // Convert pixel radius to coordinate distance based on current zoom
  const zoomLevel = getZoomLevel(latitudeDelta);
  const baseDistance = 0.001; // Base distance in degrees
  const zoomFactor = Math.pow(2, 14 - zoomLevel); // Adjust based on zoom
  return (baseDistance * pixels * zoomFactor) / 50;
};

export const useCustomClustering = ({
  events,
  mapRegion,
  clusterRadius = 50,
  minZoomForClustering = 10,
  maxClusterZoom = 14,
}: UseCustomClusteringProps): ClusterItem[] => {
  return useMemo(() => {
    if (!events || events.length === 0) {
      return [];
    }

    // Calculate current zoom level
    const currentZoom = getZoomLevel(mapRegion.latitudeDelta);

    // If zoom level is too high, don't cluster
    if (currentZoom > maxClusterZoom) {
      return events.map(event => ({
        id: `single-${event.event_id}`,
        coordinate: {
          latitude: event.coords.lat,
          longitude: event.coords.long,
        },
        events: [event],
        isCluster: false,
        count: 1,
      }));
    }

    const clusters: ClusterItem[] = [];
    const processed = new Set<number>();
    const clusterDistance = pixelToCoordinateDistance(clusterRadius, mapRegion.latitudeDelta);

    console.log('🗺️ [CLUSTER] Zoom:', currentZoom.toFixed(1), 'Distance:', clusterDistance.toFixed(6));

    events.forEach(event => {
      if (processed.has(event.event_id)) {
        return;
      }

      const eventCoord = {
        latitude: event.coords.lat,
        longitude: event.coords.long,
      };

      // Find nearby events
      const nearbyEvents = events.filter(otherEvent => {
        if (processed.has(otherEvent.event_id) || otherEvent.event_id === event.event_id) {
          return false;
        }

        const otherCoord = {
          latitude: otherEvent.coords.lat,
          longitude: otherEvent.coords.long,
        };

        // Use simple coordinate distance for clustering (faster)
        const distance = Math.sqrt(
          Math.pow(eventCoord.latitude - otherCoord.latitude, 2) +
            Math.pow(eventCoord.longitude - otherCoord.longitude, 2),
        );
        return distance <= clusterDistance;
      });

      if (nearbyEvents.length > 0) {
        // Create cluster
        const allEvents = [event, ...nearbyEvents];

        // Calculate cluster center (average of all coordinates)
        const centerLat = allEvents.reduce((sum, e) => sum + e.coords.lat, 0) / allEvents.length;
        const centerLng = allEvents.reduce((sum, e) => sum + e.coords.long, 0) / allEvents.length;

        clusters.push({
          id: `cluster-${event.event_id}`,
          coordinate: {
            latitude: centerLat,
            longitude: centerLng,
          },
          events: allEvents,
          isCluster: true,
          count: allEvents.length,
        });

        // Mark all events as processed
        allEvents.forEach(e => processed.add(e.event_id));
      } else {
        // Single event
        clusters.push({
          id: `single-${event.event_id}`,
          coordinate: eventCoord,
          events: [event],
          isCluster: false,
          count: 1,
        });
        processed.add(event.event_id);
      }
    });

    console.log(
      '🗺️ [CLUSTER] Result:',
      clusters.length,
      'items (',
      clusters.filter(c => c.isCluster).length,
      'clusters,',
      clusters.filter(c => !c.isCluster).length,
      'individual)',
    );

    return clusters;
  }, [events, mapRegion, clusterRadius, minZoomForClustering, maxClusterZoom]);
};
