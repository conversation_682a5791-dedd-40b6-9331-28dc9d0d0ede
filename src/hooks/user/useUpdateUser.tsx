import {useMutation, useQueryClient} from 'react-query';
import {User} from '~types/api/user';
import FirebaseAuth from '~services/FirebaseAuthService';
import {BASE_API_URL} from '@env';
import auth from '@react-native-firebase/auth';
export function useUpdateUser() {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (userData: Partial<User>) => {
      const userId = auth().currentUser!.uid;

      const token = await FirebaseAuth.getAuthToken();

      const response = await fetch(`${BASE_API_URL}users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify({...userData, uid: auth().currentUser!.uid}),
      });

      if (!response.ok) {
        throw new Error('Failed to update user');
      }

      return response.json() as Promise<User>;
    },
    {onSuccess: () => queryClient.refetchQueries(['userAccount'])},
  );

  return mutation;
}
