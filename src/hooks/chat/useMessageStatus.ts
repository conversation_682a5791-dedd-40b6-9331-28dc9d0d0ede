import {useState, useCallback, useRef} from 'react';
import {MESSAGE_STATUS, MessageStatus} from '~constants/messageStatus';

interface PendingMessage {
  tempId: string;
  text: string;
  timestamp: string;
  status: MessageStatus;
  chatId?: string;
}

interface MessageStatusHook {
  pendingMessages: PendingMessage[];
  addPendingMessage: (tempId: string, text: string, chatId?: string) => void;
  updateMessageStatus: (tempId: string, status: MessageStatus, realMessageId?: string) => void;
  removePendingMessage: (tempId: string) => void;
  getMessageStatus: (messageId: string) => MessageStatus | undefined;
}

export const useMessageStatus = (): MessageStatusHook => {
  const [pendingMessages, setPendingMessages] = useState<PendingMessage[]>([]);
  const messageStatusMap = useRef<Map<string, MessageStatus>>(new Map());

  const addPendingMessage = useCallback((tempId: string, text: string, chatId?: string) => {
    const newMessage: PendingMessage = {
      tempId,
      text,
      timestamp: new Date().toISOString(),
      status: MESSAGE_STATUS.SENDING,
      chatId,
    };

    setPendingMessages(prev => [...prev, newMessage]);
    messageStatusMap.current.set(tempId, MESSAGE_STATUS.SENDING);
  }, []);

  const updateMessageStatus = useCallback((tempId: string, status: MessageStatus, realMessageId?: string) => {
    // Update the status in our map
    messageStatusMap.current.set(tempId, status);

    // If we have a real message ID, also map it
    if (realMessageId) {
      messageStatusMap.current.set(realMessageId, status);
    }

    // Update pending messages
    setPendingMessages(prev => prev.map(msg => (msg.tempId === tempId ? {...msg, status} : msg)));

    // If message is sent successfully, we can remove it from pending after a delay
    if (status === MESSAGE_STATUS.SENT && realMessageId) {
      setTimeout(() => {
        setPendingMessages(prev => prev.filter(msg => msg.tempId !== tempId));
      }, 1000);
    }
  }, []);

  const removePendingMessage = useCallback((tempId: string) => {
    setPendingMessages(prev => prev.filter(msg => msg.tempId !== tempId));
    messageStatusMap.current.delete(tempId);
  }, []);

  const getMessageStatus = useCallback((messageId: string): MessageStatus | undefined => {
    return messageStatusMap.current.get(messageId);
  }, []);

  return {
    pendingMessages,
    addPendingMessage,
    updateMessageStatus,
    removePendingMessage,
    getMessageStatus,
  };
};
