import {BASE_API_URL} from '@env';
import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {Chat, CreateChatPayload} from '~types/chat';

const useCreateChat = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation(
    async (userData: CreateChatPayload) => {
      const token = await FirebaseAuth.getAuthToken();

      const response = await fetch(`${BASE_API_URL}chats/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        throw new Error('Failed to create chat');
      }

      const data = (await response.json()) as Chat;
      return data;
    },
    {
      onSuccess: () => {
        queryClient.refetchQueries(['userAccount']);
      },
    },
  );

  return mutation;
};

export default useCreateChat;
