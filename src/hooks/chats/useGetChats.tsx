import {useInfiniteQuery} from 'react-query';
import axios from 'axios';
import FirebaseAuth from '~services/FirebaseAuthService';
import {BASE_API_URL} from '@env';
import {GetChatsListPayload, GetChatsResponse} from '~types/chat';
import {useChatStore} from '~providers/chats/zustand';

const NUMBER_OF_CHATS_PER_PAGE = 10;

const useGetChats = (user_id: string) => {
  const {paginationUpdateChatsList} = useChatStore();

  const fetchChats = async ({pageParam = 0}) => {
    const token = await FirebaseAuth.getAuthToken();

    const offset = pageParam * NUMBER_OF_CHATS_PER_PAGE;
    const limit = NUMBER_OF_CHATS_PER_PAGE;

    const params: GetChatsListPayload = {
      limit,
      offset,
      user_id,
    };

    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };

    const response = await axios.get(BASE_API_URL + `chats/user/${user_id}`, {params, ...config});
    return response.data;
  };

  return useInfiniteQuery('chats', fetchChats, {
    getNextPageParam: (lastPage, pages) => {
      const nextPage = lastPage.length === 10 ? pages.length : undefined;
      return nextPage;
    },
    onSuccess: data => {
      const lastPage = data.pages[data.pages.length - 1] as GetChatsResponse;

      const items = lastPage.data || lastPage.items || [];
      if (!items[0]?.chat_id) {
        return;
      }

      paginationUpdateChatsList(lastPage);
    },
  });
};

export default useGetChats;
