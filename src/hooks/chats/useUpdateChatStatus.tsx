import {BASE_API_URL} from '@env';
import {useMutation} from 'react-query';
import {useChatStore} from '~providers/chats/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';
import {UPDATE_CHAT_STATUS, USER_CHAT_STATUS, UpdateChatStatusPayload} from '~types/chat';

const useUpdateChatStatus = () => {
  const {updateChatStatus} = useChatStore();

  const mutation = useMutation(
    async (userData: UpdateChatStatusPayload) => {
      const token = await FirebaseAuth.getAuthToken();

      const response = await fetch(`${BASE_API_URL}/chats/${userData.chatId}/users/${userData.userId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify({
          status: userData.status,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update chat status');
      }
    },
    {
      onSuccess: (_: void, variables: UpdateChatStatusPayload) => {
        updateChatStatus({
          chatId: variables.chatId,
          userId: variables.userId,
          status:
            variables.status === UPDATE_CHAT_STATUS.ACCEPTED ? USER_CHAT_STATUS.ACCEPTED : USER_CHAT_STATUS.REJECTED,
        });
      },
    },
  );

  return mutation;
};

export default useUpdateChatStatus;
