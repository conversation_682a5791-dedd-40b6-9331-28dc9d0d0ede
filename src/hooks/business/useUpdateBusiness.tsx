import {useMutation, useQueryClient} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {BASE_API_URL} from '@env';
import auth from '@react-native-firebase/auth';
import {Business} from '~types/api/business';

const useUpdateBusiness = () => {
  const queryClient = useQueryClient();
  const mutation = useMutation(
    async (userData: Partial<Omit<Business, 'created_at' | 'updated_at' | 'target_audiences'>>) => {
      const token = await FirebaseAuth.getAuthToken();
      const response = await fetch(`${BASE_API_URL}businesses/${userData.uid}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
        body: JSON.stringify({...userData, uid: auth().currentUser!.uid}),
      });
      if (!response.ok) {
        throw new Error('Failed to update business user');
      }

      return response.json();
    },
    {onSuccess: () => queryClient.refetchQueries(['businessAccount'])},
  );

  return mutation;
};

export default useUpdateBusiness;
