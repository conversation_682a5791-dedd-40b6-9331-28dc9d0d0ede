import {BASE_API_URL} from '@env';
import {useQuery} from 'react-query';
import FirebaseAuth from '~services/FirebaseAuthService';
import {Business} from '~types/api/business';
import {api, timing, error} from '~Utils/debugLogger';

export function useGetBusinessAccount(user_id: string, shouldFetch = true) {
  return useQuery<Business | undefined, Error>(
    ['businessAccount', user_id],
    async () => {
      api('Starting getBusinessAccount API call');
      const apiStartTime = Date.now();

      if (!user_id) {
        api('getBusinessAccount: No user_id provided');
        return undefined;
      }

      api('getBusinessAccount: Getting auth token');
      const tokenStart = Date.now();
      const token = await FirebaseAuth.getAuthToken();
      timing('getBusinessAccount: Auth token obtained', tokenStart);

      api('getBusinessAccount: Making API request');
      const requestStart = Date.now();
      const response = await fetch(`${BASE_API_URL}businesses/${user_id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token,
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          api(`getBusinessAccount: Business account not found for user ${user_id}`);
          return undefined; // Return undefined instead of throwing for 404
        }
        error(`getBusinessAccount: Request failed with status ${response.status}`);
        throw new Error(`Failed to get business account: ${response.status}`);
      }

      timing('getBusinessAccount: Request completed', requestStart);
      const data = await response.json();
      timing('getBusinessAccount: Total time', apiStartTime);

      return data as Business;
    },
    {
      enabled: shouldFetch,
    },
  );
}
