import {useEffect, useState} from 'react';
import {useQuery, useQueryClient} from 'react-query';
import NetInfo from '@react-native-community/netinfo';
import auth from '@react-native-firebase/auth';
import OfflineUserStorage from '~services/OfflineUserStorage/OfflineUserStorage';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useUserStore} from '~providers/userStore/zustand';
import {User} from '~types/api/user';
import {Business} from '~types/api/business';

interface OfflineUserDataState {
  userData: Omit<User, 'subcategories' | 'groups'> | null;
  businessData: Business | null;
  userType: 'personal' | 'business' | null;
  isLoading: boolean;
  isOffline: boolean;
  hasOfflineData: boolean;
  lastSync: string | null;
  syncStatus: 'synced' | 'pending' | 'failed' | 'syncing';
}

/**
 * Hook for managing offline user data with automatic sync
 */
export const useOfflineUserData = () => {
  const [state, setState] = useState<OfflineUserDataState>({
    userData: null,
    businessData: null,
    userType: null,
    isLoading: true,
    isOffline: false,
    hasOfflineData: false,
    lastSync: null,
    syncStatus: 'synced',
  });

  const userId = auth().currentUser?.uid || '';
  const queryClient = useQueryClient();
  const {setUser} = useUserStore();

  // Online data fetching hooks
  const {data: onlineUserType, isLoading: userTypeLoading} = useGetUserType(userId);
  const {data: onlineUserData, isLoading: userDataLoading} = useGetUserAccount(userId);
  const {data: onlineBusinessData, isLoading: businessDataLoading} = useGetBusinessAccount(userId);

  // Load offline data on mount
  useEffect(() => {
    loadOfflineData();
  }, []);

  // Monitor network state
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(networkState => {
      const isOffline = !(networkState.isConnected && networkState.isInternetReachable);

      setState(prev => {
        // Sync when coming back online
        if (!isOffline && prev.isOffline) {
          setTimeout(() => syncWithOnlineData(), 100);
        }

        return {...prev, isOffline};
      });
    });

    return unsubscribe;
  }, []);

  // Sync with online data when available
  useEffect(() => {
    if (!state.isOffline && (onlineUserType || onlineUserData || onlineBusinessData)) {
      syncWithOnlineData();
    }
  }, [onlineUserType, onlineUserData, onlineBusinessData, state.isOffline]);

  /**
   * Load offline data from storage
   */
  const loadOfflineData = async () => {
    try {
      setState(prev => ({...prev, isLoading: true}));

      const [storedUserData, storedBusinessData, storedUserType, storedAuthState, lastSync] = await Promise.all([
        OfflineUserStorage.getStoredUserData(),
        OfflineUserStorage.getStoredBusinessData(),
        OfflineUserStorage.getStoredUserType(),
        OfflineUserStorage.getStoredAuthState(),
        OfflineUserStorage.getLastSync(),
      ]);

      const hasOfflineData = !!(storedUserData || storedBusinessData || storedUserType);

      setState(prev => ({
        ...prev,
        userData: storedUserData,
        businessData: storedBusinessData,
        userType: storedUserType,
        hasOfflineData,
        lastSync,
        isLoading: false,
      }));

      // Set user in store if available
      if (storedUserData) {
        setUser(storedUserData);
      }

      console.log('📱 OfflineUserData: Loaded offline data', {
        hasUserData: !!storedUserData,
        hasBusinessData: !!storedBusinessData,
        userType: storedUserType,
        lastSync,
      });
    } catch (error) {
      console.error('❌ OfflineUserData: Failed to load offline data', error);
      setState(prev => ({...prev, isLoading: false}));
    }
  };

  /**
   * Sync offline data with online data
   */
  const syncWithOnlineData = async () => {
    try {
      setState(prev => ({...prev, syncStatus: 'syncing'}));

      let hasUpdates = false;

      // Sync user type
      if (onlineUserType && onlineUserType !== state.userType) {
        await OfflineUserStorage.storeUserType(onlineUserType);
        setState(prev => ({...prev, userType: onlineUserType}));
        hasUpdates = true;
      }

      // Sync user data
      if (onlineUserData && onlineUserType === 'personal') {
        const isDataDifferent = !state.userData || JSON.stringify(state.userData) !== JSON.stringify(onlineUserData);

        if (isDataDifferent) {
          await OfflineUserStorage.storeUserData(onlineUserData);
          setState(prev => ({...prev, userData: onlineUserData}));
          setUser(onlineUserData);
          hasUpdates = true;
        }
      }

      // Sync business data
      if (onlineBusinessData && onlineUserType === 'business') {
        const isDataDifferent =
          !state.businessData || JSON.stringify(state.businessData) !== JSON.stringify(onlineBusinessData);

        if (isDataDifferent) {
          await OfflineUserStorage.storeBusinessData(onlineBusinessData);
          setState(prev => ({...prev, businessData: onlineBusinessData}));
          hasUpdates = true;
        }
      }

      // Update auth state
      if (userId) {
        await OfflineUserStorage.storeAuthState({
          isAuthenticated: true,
          userId,
          userType: onlineUserType,
        });
      }

      if (hasUpdates) {
        await OfflineUserStorage.updateLastSync();
        const newLastSync = await OfflineUserStorage.getLastSync();
        setState(prev => ({
          ...prev,
          lastSync: newLastSync,
          syncStatus: 'synced',
          hasOfflineData: true,
        }));

        console.log('✅ OfflineUserData: Synced with online data');
      } else {
        setState(prev => ({...prev, syncStatus: 'synced'}));
      }
    } catch (error) {
      console.error('❌ OfflineUserData: Failed to sync with online data', error);
      setState(prev => ({...prev, syncStatus: 'failed'}));
    }
  };

  /**
   * Force refresh data from online sources
   */
  const forceRefresh = async () => {
    try {
      setState(prev => ({...prev, syncStatus: 'syncing'}));

      // Invalidate and refetch online queries
      await Promise.all([
        queryClient.invalidateQueries(['userType', userId]),
        queryClient.invalidateQueries(['userAccount', userId]),
        queryClient.invalidateQueries(['businessAccount', userId]),
      ]);

      // Wait for new data to be fetched
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Sync will happen automatically via useEffect
    } catch (error) {
      console.error('❌ OfflineUserData: Failed to force refresh', error);
      setState(prev => ({...prev, syncStatus: 'failed'}));
    }
  };

  /**
   * Clear all offline data
   */
  const clearOfflineData = async () => {
    try {
      await OfflineUserStorage.clearAllData();
      setState({
        userData: null,
        businessData: null,
        userType: null,
        isLoading: false,
        isOffline: state.isOffline,
        hasOfflineData: false,
        lastSync: null,
        syncStatus: 'synced',
      });
      console.log('🗑️ OfflineUserData: Cleared all offline data');
    } catch (error) {
      console.error('❌ OfflineUserData: Failed to clear offline data', error);
    }
  };

  /**
   * Get current user data (prioritizes online data when available)
   */
  const getCurrentUserData = (): Omit<User, 'subcategories' | 'groups'> | null => {
    if (!state.isOffline && onlineUserData) {
      return onlineUserData;
    }
    return state.userData;
  };

  /**
   * Get current business data (prioritizes online data when available)
   */
  const getCurrentBusinessData = (): Business | null => {
    if (!state.isOffline && onlineBusinessData) {
      return onlineBusinessData;
    }
    return state.businessData;
  };

  /**
   * Get current user type (prioritizes online data when available)
   */
  const getCurrentUserType = (): 'personal' | 'business' | null => {
    if (!state.isOffline && onlineUserType) {
      return onlineUserType;
    }
    return state.userType;
  };

  /**
   * Check if we're still loading initial data
   */
  const isInitialLoading = (): boolean => {
    if (state.isOffline) {
      return state.isLoading;
    }
    return state.isLoading || userTypeLoading || userDataLoading || businessDataLoading;
  };

  return {
    // Data
    userData: getCurrentUserData(),
    businessData: getCurrentBusinessData(),
    userType: getCurrentUserType(),

    // State
    isLoading: isInitialLoading(),
    isOffline: state.isOffline,
    hasOfflineData: state.hasOfflineData,
    lastSync: state.lastSync,
    syncStatus: state.syncStatus,

    // Actions
    forceRefresh,
    clearOfflineData,
    syncWithOnlineData,

    // Raw offline data (for debugging)
    offlineData: {
      userData: state.userData,
      businessData: state.businessData,
      userType: state.userType,
    },

    // Raw online data (for debugging)
    onlineData: {
      userData: onlineUserData,
      businessData: onlineBusinessData,
      userType: onlineUserType,
    },
  };
};
