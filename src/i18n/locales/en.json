{"greeting": "Hello {{user_name1}}, I am facing a event issue.", "technical_issue": "Hello {{name}}, I am facing a technical issue.", "technicalissue": "Technical Issue", "eventissue": "Event Issue", "ticket_type": "Ticket Type", "quantity": "Quantity", "decline": "Decline", "cost": "Cost", "status": "Status", "accept": "Accept", "invite_message": "Your friend {{userName}} is inviting you to join {{itemName}} on Pyxi! \n {{url}}", "signin": {"send_message": "Send email recovery", "welcome": "Welcome to Pyxi!", "welcome_back": "Welcome back", "continue_journey": "Ready to Continue Your Journey?", "welcome_back_to_pyxi": "Welcome back to Pyxi", "discover_connect_experience": "Discover • Connect • Experience", "sign_in_title": "Sign In", "continue_event_journey": "Continue your event discovery journey", "forgot_pwd": "Forgot password", "create_pwd": "Create password", "create_pwd_desc": "Your new password must be different from previous used passwords and must be of at least 8 characters.", "create_new_pwd": "Create new password", "pwds_match": "Both passwords must match.", "at_least_chars": "Must be at least 8 characters", "update_pwd": "Update Password", "valid_email": "Please enter a valid email", "forgot_pwd_header": "Forgot password?", "forgot_pwd_msg": "Not to worry! Enter email address associated with your account and we'll send a link to reset your password.", "check_email_header": "Email link sent!", "check_email_msg": "We've sent a link to reset your password to your email address. Please tap the link in your email to reset your password.", "check_email_btn": "Return to log in", "sign_up_to_continue": "Sign up to continue", "sign_in_to_continue": "Sign in to continue", "existing_account": "Already have an account?", "email_in_use_alert": "That email address is already in use. Please try to sign in with the correct password.", "email_invalid_alert": "That email address is invalid.", "password_length_alert": "Password must be greater than 6 characters!", "password_match_alert": "Passwords must match!", "incorrect_email_alert": "Please check if the email address is in the correct format!", "invalid_pwd": "Invalid password", "i_accept_the": "I accept the ", "terms_of_use": "Terms of Use", "privacy_policy": "Privacy Policy", "sign_in": "Sign in", "sign_up": "Sign up", "login": "Log in", "email": "Email", "sign_with_google": "or sign up with", "login_with_google": "or log in with", "password": "Password", "no_acc_question": "Don't have an account?", "register": "Register", "confirm_password": "Confirm password", "create_account": "Create Account", "choosing_category": "Choosing Category", "please_select_at_least_3_categories": "Please select at least 3 categories", "tell_us_what_you_like": "Tell us what you like.", "i_will_do_later": "I will add later"}, "onboarding": {"image_required": "Image is required", "company_name": "Company Name*", "email": "Email*", "email_error_required": "Email is required", "email_error_invalid": "Invalid email address", "about_header": "Tell us a little about yourself", "about_body": "What you're looking for, your current job, your view of the world - anything that would be relevant to find people with similar interests or ways of thinking!", "birthday_header": "When is your birthday?", "birthday_content": "This will allow us to match age-appropriate events to you!", "gender": "Which gender do you identify with?", "man": "Male", "woman": "Female", "prefer": "Prefer not to say", "other": "Other", "enter_address": "Please enter your address", "grouping_header": "Which group do you identify with?", "how_many_kids": "How many kids do you have?", "choose_number": "Choose Number", "account_type_header": "Is this a business or a personal account?", "couples": "<PERSON><PERSON><PERSON>", "upload": "Upload", "personal": "Personal", "business": "Business", "single_parents": "Single Parents", "hetero_parents": "Hetero Parents", "lgbtq_parents": "LGBTQ+ Parents", "organisations": "Organisations", "all": "All", "interests_current": "What are your current interests?", "interests_new": "What interests would you like to explore?", "show_subinterests": "Show subinterests", "load_photos_header": "Tap to add a picture!", "set_account": "Password reset successful!", "set_location": "", "favourite_name": "Your favourite name", "set_name": "What's your name?", "splash_1": "Great, you're all signed up! Let's get to know you!", "splash_2_no_name": "Looking good! Let's get to know you!", "splash_2_name_1": "Looking good, ", "splash_2_name_2": "! Let's get to know you!", "splash_3": "All done! Bear with us for a couple seconds while we find you some exciting events...", "preferences": "Preferences", "preferences_title": "Your Preferences", "preferences_description": "Help us personalize your experience with a few questions.", "question": "Question", "of": "of", "continue": "Continue", "finish": "Finish", "UsingFor_header": "What will you be using Pyxi for?", "find_people": "Find interesting people", "find_things": "Find fun things to do", "both": "Both", "personal_info": "Personal Information", "personal_info_subtitle": "Tell us a bit about yourself to get started", "photo_hint": "Show us your cute face! If you're shy, just upload a picture of a landscape", "first_name_placeholder": "Enter your first name", "last_name_placeholder": "Enter your last name (optional)", "select_birthday": "Select your birthday", "age_requirement_hint": "You must be at least 13 years old to create an account", "select_gender": "Select your gender", "select_location": "Select your location", "join_group_title": "Join a Group (Optional)", "group_code_placeholder": "Enter group invitation code", "group_description": "Connect with friends by entering their group invitation code", "location_search_placeholder": "Search for your location...", "location_search_hint": "Start typing to search for addresses", "choose_account": "Choose an account type \n You can't change it later.", "account_type_subtitle": "Select the type of account that best fits your needs", "business_registration": "Business Registration", "business_redirect_message": "You will be redirected to our website to complete your business account registration.", "personal_desc": "Discover, create events and meet new friends.", "business_desc": "Host regular events and reach target audience.", "groups_4": "Young at heart", "groups_5": "<PERSON><PERSON><PERSON>", "select_all": "Select all", "related_groups": "What do you relate with?", "groups_2": "Students", "groups_3": "Professionals", "groups_1": "Families", "how_many_kids_desc": "How many kids do you have \n and how old are they?", "we_are_asking_for_the_kids_age": "We are asking for the kids age so that we can make better recommendations about events?", "select_or_pass": "Select or pass", "select_age": "Select age", "child": "Child", "children": "Children", "select_ages": "Select ages for your children", "age": "Age", "no_interests_title": "No interests selected", "no_interests_message": "You can modify your selections later in settings", "first_name": "First Name*", "last_name": "Last Name (optional)", "description1": "Tell us a little bit about yourself (optional)", "description2": "Tell us what makes you interesting!", "date_of_birth": "Date of Birth*", "dob_required": "Date of Birth is required", "gender1": "Gender*", "location1": "Location*", "fill_your_profile": "Fill your profile", "first_name_error": "Name is required", "date_error": "Date is required", "selection_error": "Selection is required", "description_error": "Description is required", "showcase_error": "Showcase Yourself is required", "location_error": "An approximate location is required", "choose_interests": "Please choose your interests", "interests_desc": "By selecting your favorite topics and \n area of interests you get the best \n selection of events", "interests_business_desc": "Choose specific topics within your interest area. This fine-tunes your business's engagement with events that are most likely to appeal to your audience.", "business_location": "Where is your business based?*", "description_business": "Describe your events or type of business*", "min_length_error": "Description should be more than 40 characters", "create_business_profile": "Create your business profile"}, "preferences": {"question_1": "Do you consider yourself an introvert, an extrovert or a bit of both?", "question_2": "Do you have any strong religious views?", "question_3": "Do you have any strong political views?", "introvert": "Introvert", "extrovert": "Extrovert", "bit_of_both": "A bit of both", "yes": "Yes", "no": "No", "christian": "<PERSON>", "muslim": "Muslim", "hindu": "Hindu", "jewish": "Jewish", "other": "Other", "very_left": "Very left", "very_right": "Very right", "passionate_about_politics": "I just get passionate about politics"}, "home": {"children_events": "Children events", "radius": "<PERSON><PERSON>", "km": "km", "distance_adjuster": "Distance Adjuster", "useCurrent": "Use current", "create_event": "Create your own event", "home_search_placeholder": "Events, webinars or music", "matches": "Discover Matches", "request_to_chat": "Request to chat", "chat_exists": "Chat already exists", "check_email": "Please check your email", "check_email_msg": "We've sent a password to your registered email", "forgot_pwd": "Forgot password", "discover": "Discover", "discover_desc": "Events matched to interests you want to explore", "user_events": "User Events", "my_events": "My Events", "user_events_desc": "Events created by Pyxi users", "suggestions": "Suggestions", "suggestions_desc": "Events matched to your current interests", "date": "Date", "today": "Today", "tomorrow": "Tomorrow", "this_weekend": "This Weekend", "specific_date": "Specific Date", "time_of_day": "Time of day", "day": "Day", "night": "Night", "any_time": "Any time", "indoor": "Indoors", "outdoor": "Outdoors", "venue": "Venue", "location": "Location", "closer_to_me": "Closer to me", "doesnt_matter": "Doesn't matter", "other": "Other", "easy_parking": "Easy Parking", "show_map": "Show Map", "apply_filters": "Apply filters", "cancel_filters": "Cancel filters", "congrats_with_name": "Congrats, you're going to ", "congrats_without_name": "Congrats, you're going to a new event!", "matched_with": "You matched with ", "people_have_a_look": "people, have a look at your best matches!", "match_without_number": "You matched with many people, have a look at their profile!", "no_match_without_number": "Unfortunately, we didn't find anyone to match you right now. Please try again later or create this event from your account.", "view_profile": "View profile", "find_someone": "Find me someone to go with", "your_friend": "Your friend ", "invitation_with_name": " is inviting you to join their event on Pyxi!", "invitation_without_name": "Your friend is inviting you to join their event on Pyxi!", "share": "Share with friends", "welcome": "Welcome to PYXI", "todays": "Today's {{date}}", "welcome_name": "Welcome back, {{name}}!", "list": "List", "map": "Map", "filters": "Filters", "upcoming_events": "Upcoming Events", "all_events": "All Events", "free_events": "Free", "nearby": "Nearby", "dates": "Dates", "more": "More", "empty_list_events": "There are no events near you. Try to increase your radius or click on discover", "empty_list_events_discover": "There are no events near you. Try to increase your radius", "empty_list_personal_events": "You do not have events yet", "empty_list_upcoming_events": "You do not have upcoming events yet", "empty_list_past_events": "You do not have past events yet"}, "calendar": {"header": "Calendar", "clear_date": "Clear date", "please_select_date": "Please select a date", "upcoming_events": "Upcoming Events", "past_events": "Past Events", "past_empty_string": "You have no past events yet. Try searching for a new one and joining the event!", "upcoming_empty_string": "You have no upcoming events yet. Try searching for a new one and joining the event!", "confirmed_events": "Events which you have been confirmed for"}, "events": {"cancel_event": "Cancel Event", "revive_event": "Revive Event", "find_someone": "Find me someone to go with", "no_matches": "No matches", "date_before_error": "- End date must be after start date", "event_info_header": "Event Info", "start_date": "Start date", "end_date": "End date", "header": "Events", "event_name": "Event Name", "event_description": "Event Description", "category": "Category", "categories": "Categories", "sub_categories": "Sub Categories", "empty_string_title": "No personal events", "empty_string_description": "Please click the Add button in the top right corner to make your first event", "tags": "Tags", "location": "Location", "visibility": "Visibility", "publish_event": "Publish Event...", "update_event": "Update Event", "save_changes": "Save Changes", "delete_event": "Delete Event", "attendees_pending": "Attendees Pending", "no_attendees": "There are no pending attendees yet", "create_event_date": "When is it happening?", "create_event_end_date": "When does the event end?", "create_event_desc_header": "Write a short description describing your event", "create_event_desc_body": "Describe your event with a text description and tags to help people to find it.", "create_event_publishing": "Who should be able to see the event?", "create_event_desc_placeholder": "An exciting music event featuring...", "create_event_name": "What would you like to call your event?", "add_to_calendar": "Add to calendar", "added_to_calendar": "Added to calendar", "find_me_someone": "Find me someone to go with", "look_through": "Look through", "requested": "Requested", "attending": "Attending", "requiring_confirmation": "Requiring Confirmation", "view": "View", "host": "Host", "chat": "Cha<PERSON>", "join": "Join", "name": "Name", "description": "Description", "friends": "Friends", "students": "Students", "professionals": "Professionals", "families": "Families", "all": "All", "create_event_splash": "", "capacity": "What is the maximum capacity, if any?", "choose_template": "Choose your event template", "start_by_choosing_category": "Start by choosing a category that fits your event the best", "choose_the_subcategories": "Choose the subcategory", "event_info": "Tell us about your event", "payment_url": "Payment Url", "pay_here": "Pay Here", "event_groups": "Who should see your event?", "event_name_error": "Event name is required", "event_description_error": "Event description is required", "event_name_placeholder": "Event Name", "event_description_placeholder": "Description", "choose_category": "Choose category", "choose_subcategory": "Choose subCategory", "create_event_type_name": "What type of event is it?", "create_event_type_desc": "Select Public if you would like your event's location to be shown to everyone. Select Private to only show it to confirmed attendees.", "public": "Public", "private": "Private", "paidevent": "Paid Event", "free": "Free", "neighbourhood_gropus": "Neighbourhoods", "paid": "Paid", "business": "Business", "user": "User", "attendees": "Attendees", "summary": "Summary", "date_range": "Date Range", "start_time": "Start time", "end_time": "End time", "nameError": "Please enter event name", "descriptionError": "Please enter event description", "eventAddressError": "'Address is required'", "locationError": "Please select a location", "categoriesError": "Please select a category", "startDateError": "Please select a start date", "endDateError": "Please select an end date", "flexible_date": "Flexible Date", "exact_date": "Exact Date", "add_end_date": "+ Add End Date", "remove_end_date": "- Remove End Date", "group_capacity": "Capacity", "events_private": "Private", "events_public": "Public", "enter_capacity": "Enter Capacity", "location_error": "An approximate location is required", "date_and_time": "Date & Time", "who_is_this_for": "Who is this for?", "ticket_info": "Ticket Information", "promo_info": "Promocode Information", "maximum_people": "Maximum {{capacity}} people", "price": "Price", "quantity": "Quantity", "discount": "Discount", "date_error": "Date is required", "event_address": "Add your exact location including, floor or room number", "address_error": "Address is required", "payment_url_error": "Payment URL is required", "payment_url_error2": "Payment URL must be a valid url", "children_event": "Children's event", "event_has_been_deleted": "This event has been deleted", "see_my_match": "See my matches", "refresh": "Refresh", "time": "Time", "today": "Today", "tomorrow": "Tomorrow", "this_week": "This week", "weekend": "Weekend", "next_week": "Next week", "past": "Past", "thisweekend": "This weekend", "now": "Now", "lookingforevents": " When are you looking for events?", "anytime": "Anytime", "dateselection": "Date selection", "startdate": "Start date", "enddate": "End date", "apply": "Apply", "eventtype": "Event type", "type": "Type", "users": "Users", "suggestions": "Suggestions", "liked": "Liked", "attended": "Attended", "created": "Created", "rate_experience": "How was your experience?", "submit_rating": "Submit Rating", "rating_submitted": "Thank you for your feedback!", "event_updated": "Event updated successfully"}, "chat": {"header": "Cha<PERSON>", "my_conversations": "Conversations", "groups": "Groups", "chat_with": "Chat with ", "chat_with_group": "Chat with group", "group_chat": "Group chat", "empty_string": "You have no conversations at the moment!", "meet_users": "Meet Pyxi Users", "invite": "Invite", "contacts": "Contacts", "invite_contacts": "Invite Your Contacts", "chat": "Cha<PERSON>", "chat_all": "Chat All", "reset_pwd_msg": "Please ensure your new password has not been used previously", "reset_success": "Password reset successful!", "reset_success_msg": "Awesome! Your password has been updated", "reset_success_btn": "Return to sign in ", "sign_in": "Sign in", "group_members": "Group Members", "rejected_chat": "You have rejected this chat", "reject_btn": "Reject", "accept_btn": "Accept", "empty_chat": "Send your first message to start a chat", "conversations": "Conversations", "no_conversations": "No conversations yet", "start_conversation": "Start a conversation by messaging someone", "support_chat": "Chat with Support", "get_help_instantly": "Get help instantly from our team", "no_internet_title": "No Internet Connection", "no_internet_message": "Please check your internet connection and try again. Cha<PERSON> requires an active internet connection.", "network_error_title": "Connection Error", "network_error_message": "Unable to connect to chat servers. Please check your internet connection and try again.", "error_title": "<PERSON><PERSON>", "error_message": "Something went wrong with the chat. Please try again.", "retrying": "Retrying...", "try_again": "Try Again", "loading_conversations": "Loading conversations...", "offline_mode": "Offline - Messages will be sent when connected", "offline_viewing_cached": "Offline - Viewing cached conversations", "offline_no_data": "Offline - No cached data available", "offline_message_sent": "1 offline message sent successfully!", "offline_messages_sent": "{{count}} offline messages sent successfully!", "offline_messages_partial": "{{success}} messages sent, {{failed}} failed", "offline_messages_failed": "{{count}} offline messages failed to send"}, "settings": {"deleteAccountAlert": {"title": "Delete Account", "description": "Are you sure you want to permanently delete your account? This action cannot be undone.", "delete": "Delete", "cancel": "Cancel"}, "deleteAccountErrorAlert": {"title": "Error <PERSON> Account", "description": "An error occurred while attempting to delete your account. Please contact our support team for assistance.", "contact": "Contact Support", "cancel": "Cancel"}, "title": "Settings", "email": "Email", "header": "Cha<PERSON>", "personal_info": "Personal Info", "purchase_history": "Purchase History", "name": "Name", "name_placeholder": "Your name", "last_name": "Last Name (optional)", "last_name_placeholder": "Your last name", "gender1": "Gender*", "location_error": "An approximate location is required", "group": "Which demographic(s) do you relate with?", "type_of_profile_error": "Please select at least one group", "categories": "Categories", "kids": "Kids", "children_error": "Please enter how many kids do you have and how old they are", "categories_error": "Please enter at least 3 category", "first_name": "First Name*", "business_location": "Business Location", "description_business": "Describe your events or type of business*", "target_audience": "Which target audience are you interest in most?", "categories_business": "Business type", "categories_error_business": "Please enter at least 1 category", "about_me": "About me", "birthday": "Date of Birth", "location": "Location", "gender": "Gender", "update_details": "Update Details", "save_changes": "Save Changes", "change_pwd": "Change Password", "pwd_header": "Reset Password", "pwd_requirements": "Please enter email address associated with your account and we'll send a link to reset your password.", "notifications": "Notifications", "update_notifications": "Update Notifications", "message_notifications": "Message Notifications", "event_notifications": "Event Notifications", "help_center": "Help Center", "csv_picker": "Csv Picker", "contact_us": "Contact us", "contact_us_body": "If you have any questions or you have any problem with the app, then please write to our support. We would be happy to help you!", "sign_out": "Log Out", "sign_out_body": "Are you sure you want to log out? We wouldn't be able to notify you of any new matches!", "personalInfo": "Personal Info", "nameError": "Please enter your name", "descriptionError": "Please enter description", "addressError": "Please select a location", "birthdayError": "Please select your birthday", "delete_account": "Delete account", "update_your_Pyxi": "Update your Pyxi", "edit_interests": "Edit interests", "edit_preferance": "Edit personality questions", "groups": "Groups", "groups_management": "Groups Management", "joined_groups": "Joined Groups", "join_group": "Join Group", "join": "Join", "join_group_info": "If you have an invitation code and want to join a group, enter it here to to join events and match only with friends within the group (examples of groups ‘neighbourhood groups’, ‘collegues groups’ etc)", "leave_group": "Leave Group", "leave": "Leave", "join_new_group_info": "Join the group to connect with friends, discover new members, and engage in exciting discussions. Unlock exclusive content and be part of a thriving community!", "enter_invitation_code": "Enter invitation code", "join_success": "Successfully joined group", "join_error": "Invalid invitation code", "leave_success": "Successfully left group", "leave_error": "Error leaving group", "no_groups": "You haven't joined any groups yet", "appearance": "Appearance", "theme_light": "Light", "theme_dark": "Dark", "theme_system": "System", "theme_light_desc": "Use light theme for a bright, clean interface", "theme_dark_desc": "Use dark theme for a comfortable viewing experience in low light", "theme_system_desc": "Automatically match your device's system theme setting", "theme_selection": "Theme Selection", "support_actions": "Support Actions", "additional_help": "Additional Help", "help_description": "Need more help? Check out our FAQ section or reach out to our support team directly.", "view_faq": "View FAQ", "account": "Account", "preferences": "Preferences", "support": "Support & Legal", "basic_info": "Basic Information", "personal_details": "Personal Details", "profile_settings": "Profile Settings", "reset_password": "Reset Password", "email_required": "Please enter your email", "email_invalid": "Enter valid email", "email_placeholder": "Enter your email address", "send_reset_email": "Send Reset Email", "reset_email_sent": "Reset Email <PERSON>", "reset_email_message": "Please check your email for password reset instructions.", "interests_description": "Select your interests to get personalized event recommendations.", "search_interests": "Search interests...", "change_password": "Change Password", "change_password_subtitle": "Reset your account password", "password_security": "Password Security", "password_security_description": "Keep your account secure with a strong password.", "current_password": "Current Password", "new_password": "New Password", "confirm_new_password": "Confirm New Password", "password_updated": "Password Updated", "password_updated_message": "Your password has been updated successfully.", "orders_found": "orders found", "your_orders": "Your orders", "loading_purchases": "Loading your purchase history...", "no_purchases": "No Purchases Yet", "no_purchases_description": "You haven't made any purchases yet. When you buy event tickets, they'll appear here.", "edit_preferences": "Edit Preferences", "edit_preferences_subtitle": "Update your preferences to get better recommendations", "preferences_description": "Update your preferences to get better recommendations.", "business_info": "Business Information", "business_info_subtitle": "Update your business details", "business_info_description": "Keep your business information up to date for better visibility.", "business_details": "Business Details", "business_name": "Business Name", "business_name_placeholder": "Enter your business name", "business_description": "Business Description", "business_description_placeholder": "Describe your business", "groups_joined": "groups joined", "manage_your_groups": "Manage your groups", "join_group_description": "Enter an invitation code to join a community group.", "invitation_code": "Invitation Code", "loading_groups": "Loading your groups...", "community_group": "Community Group", "no_groups_description": "You haven't joined any groups yet. Use an invitation code to join your first group.", "delete_account_subtitle": "Confirm your password to proceed", "delete_account_warning": "Please enter your password to delete the account. This action cannot be undone.", "confirm_password": "Confirm Password", "enter_password": "Enter your password", "help_center_subtitle": "Get support and manage your account", "sign_out_title": "Sign Out", "join_new_group": "Join a New Group", "select_interests": "Select Interests", "no_categories": "No categories available", "saving": "Saving...", "interests_selected": "interests selected", "select_your_interests": "Select your interests"}, "payment": {"free": "Free", "quantity": "Quantity", "summary": "Payment Summary", "subtotal": "Subtotal", "discount": "Discount", "total": "Total", "processing": "Processing Payment...", "success": "Payment Successful!", "failed": "Payment Failed", "retry": "Retry Payment", "ticket_type": "Ticket Type", "price_per_ticket": "Price per ticket", "total_tickets": "Total tickets", "promo_code": "Promo Code", "apply_promo": "Apply", "promo_applied": "Promo code applied", "invalid_promo": "Invalid promo code"}, "generic": {"continue": "Continue", "submitting": "Submitting", "cancel": "Cancel", "search": "Search", "go": "Go", "or": "or", "read_more": "Read More", "done": "Done", "close": "Close", "submit": "Submit", "back": "Back", "time": "Time", "date": "Date", "location": "Location", "start": "Start", "end": "End", "publish": "Publish", "home": "Home", "calendar": "Calendar", "events": "Events", "chat": "Cha<PERSON>", "settings": "Settings", "language": "Language", "select_range": "Select range", "invalid_date_format": "Invalid date format", "invalid_time_format": "Invalid time format", "enter_date_format": "Please enter date in format DD/MM/YYYY", "enter_time_format": "Please enter time in 12h format", "accept": "Accept", "reject": "Reject", "remove": "Remove", "loading": "Loading...", "error": "Error", "try_again": "Try Again", "something_went_wrong": "Something went wrong", "no_data": "No data available", "suggested_categories": "Suggested Categories", "more_categories": "More categories", "show_less": "Show less", "oops_something_went_wrong": "Oops! Something went wrong", "unexpected_error": "We encountered an unexpected error. Please try again.", "report_issue": "Report Issue", "filters": "Filters", "reset": "Reset", "confirm": "Confirm", "all": "All", "skip": "<PERSON><PERSON>", "next": "Next", "previous": "Previous", "ok": "OK", "save": "Save", "edit": "Edit", "delete": "Delete", "success": "Success", "warning": "Warning", "info": "Info"}, "categories": {"Sports---Fitness": "Sports & Fitness", "Food--Drinks---Dining": "Food, Drinks & Dining", "Arts---culture": "Arts & Culture", "Music": "Music", "Gaming": "Gaming", "Outdoor-Activities": "Outdoor Activities", "Fashion---Style": "Fashion & Style", "Travel-and-Exploration": "Travel & Exploration", "Party": "Party", "Business---Education": "Business & Education", "Social-Causes---Activism": "Social Causes & Activism", "Family---Parenting": "Family & Parenting", "Literature": "Literature", "Animals": "Animals", "Romance": "Romance", "Extreme": "Extreme", "festive": "Festive", "dancing": "Dancing"}, "subcategories": {"Animal-Adoption-Events": "Animal Adoption Events", "Aquarium-Enthusiasts": "Aquarium Enthusiasts", "Cat-Lovers-Meetups": "Cat Lovers Meetups", "Dog-Park-Gatherings": "Dog Park Gatherings", "Equine-Enthusiasts": "Equine Enthusiasts", "Exotic-Pet-Owners": "Exotic Pet Owners", "Horseback-Riding-Clubs": "Horseback Riding Clubs", "Nature-Photography-Walks": "Nature Photography Walks", "Pet-Training-Workshops": "Pet Training Workshops", "Reptile-Enthusiasts": "Reptile Enthusiasts", "Wildlife-Preservation-Activities": "Wildlife Preservation Activities", "Abstract-Art": "Abstract Art", "Art-Galleries": "Art Galleries", "Art-History": "Art History", "Ballet": "Ballet", "Calligraphy": "Calligraphy", "Comedy-Shows": "Comedy Shows", "Concerts": "Concerts", "Contemporary-Art": "Contemporary Art", "Dance-Performances": "Dance Performances", "Drama": "Drama", "Film-Festivals": "Film Festivals", "Fine-Arts": "Fine Arts", "Graffiti-Art": "Graffiti Art", "Historic-Sites": "Historic Sites", "Improv-Theater": "Improv Theater", "Jazz-Music": "Jazz Music", "Literary-Events": "Literary Events", "Live-Music": "Live Music", "Museums": "Museums", "Opera": "Opera", "Orchestra": "Orchestra", "Painting": "Painting", "Performing-Arts": "Performing Arts", "Photography": "Photography", "Poetry-Readings": "Poetry Readings", "Pottery": "Pottery", "Sculpture": "Sculpture", "Stand-up-Comedy": "Stand-up Comedy", "Street-Art": "Street Art", "Theater": "Theater", "Traditional-Music": "Traditional Music", "Visual-Arts": "Visual Arts", "Watercolor-Painting": "Watercolor Painting", "Business-Networking": "Business Networking", "Career-Development": "Career Development", "Coding-Workshops": "Coding Workshops", "Digital-Marketing": "Digital Marketing", "Entrepreneurship": "Entrepreneurship", "Finance-Workshops": "Finance Workshops", "IOT": "Internet of Things (IoT)", "Leadership-Seminars": "Leadership Seminars", "Online-Courses": "Online Courses", "Personal-Branding": "Personal Branding", "Public-Speaking": "Public Speaking", "Skill-Development": "Skill Development", "Startup-Incubators": "Startup Incubators", "Technology-Workshops": "Technology Workshops", "Webinars": "Webinars", "Adventure-Sports": "Adventure Sports", "Bungee-Jumping": "Bungee Jumping", "Cliff-Diving": "Cliff Diving", "Extreme-Hiking": "Extreme Hiking", "Extreme-Mountain-Biking": "Extreme Mountain Biking", "Indoor-Skydiving": "Indoor Skydiving", "Off-Road-Racing": "Off-Road Racing", "Paragliding": "Paragliding", "Shark-Cage-Diving": "Shark Cage Diving", "Skydiving": "Skydiving", "Spelunking": "Spelunking", "White-Water-Rafting": "White Water Rafting", "Adoption-Support": "Adoption Support", "Baby-Playgroups": "Baby Playgroups", "Childcare-Resources": "Childcare Resources", "Co-Parenting-Groups": "Co-Parenting Groups", "Family-Fitness-Activities": "Family Fitness Activities", "New-Parents-Meetups": "New Parents Meetups", "Parenting-Workshops": "Parenting Workshops", "Single-Parenting": "Single Parenting", "Stay-at-Home-Parenting": "Stay-at-Home Parenting", "Toddler-Playdates": "<PERSON>ler Playdates", "Work-Life-Balance": "Work-Life Balance", "Accessories": "Accessories", "Casual-Wear": "<PERSON><PERSON>ual Wear", "Couture": "Couture", "Designer-Brands": "Designer Brands", "Ethical-Fashion": "Ethical Fashion", "Formal-Wear": "Formal Wear", "Handbags": "Handbags", "High-Heels": "High Heels", "Jewelry": "Jewelry", "Lingerie": "Lingerie", "Luxury-Fashion": "Luxury Fashion", "Men-s-Fashion": "Men's Fashion", "Plus-Size-Fashion": "Plus-Size Fashion", "Sneakers": "Sneakers", "Street-Style": "Street Style", "Sustainable-Fashion": "Sustainable Fashion", "Swimwear": "Swimwear", "Vintage-Fashion": "Vintage Fashion", "Watches": "Watches", "Women-s-Fashion": "Women's Fashion", "Afternoon-Tea": "Afternoon Tea", "Bakeries": "Bakeries", "Barbecue-Joints": "Barbecue Joints", "Breweries": "Breweries", "Brunch-Spots": "Brunch Spots", "Buffets": "Buffets", "Cafes": "Cafes", "Casual-Dining": "Casual Dining", "Coffee-Shops": "Coffee Shops", "Cocktail-bars": "Cocktail Bars", "Cooking-classes": "Cooking Classes", "Date-Night-Dinners": "Date Night Dinners", "Dessert-Shops": "Dessert Shops", "Diners": "Diners", "Ethnic-Cuisine": "Ethnic Cuisine", "Fast-Food": "Fast Food", "Fine-Dining": "Fine Dining", "Food-Challenges": "Food Challenges", "Food-Festivals": "Food Festivals", "Food-Markets": "Food Markets", "Food-Tours": "Food Tours", "Food-Trucks": "Food Trucks", "Healthy-Eateries": "Healthy Eateries", "Ice-Cream-Parlors": "Ice Cream Parlors", "International-cuisine": "International Cuisine", "Juice-Bars": "Juice Bars", "Noodle-Houses": "Noodle Houses", "Pizzerias": "Pizzerias", "Restaurants": "Restaurants", "Rooftop-Dining": "Rooftop Dining", "Sandwich-Shops": "Sandwich Shops", "Seafood": "Seafood", "Steakhouses": "Steakhouses", "Street-Food": "Street Food", "Sushi-Bars": "Sushi Bars", "Tea-Houses": "Tea Houses", "Vegan---Vegetarian": "Vegan & Vegetarian", "Whiskey-Bars": "Whiskey Bars", "Wine-bars": "Wine Bars", "Wine-Taistings": "Wine Tastings", "Wineries": "Wineries", "Board-Games": "Board Games", "Card-Games": "Card Games", "Console-Gaming": "Console Gaming", "eSports": "eSports", "Game-Development": "Game Development", "Mobile-Gaming": "Mobile Gaming", "Online-Gaming": "Online Gaming", "PC-Gaming": "PC Gaming", "Role-Playing-Games--RPG-": "Role-Playing Games (RPG)", "Strategy-Games": "Strategy Games", "Tabletop-RPG": "Tabletop RPG", "Virtual-Reality--VR--Gaming": "Virtual Reality (VR) Gaming", "Book-Clubs": "Book Clubs", "Bookstores": "Bookstores", "Creative-Writing-Workshops": "Creative Writing Workshops", "Fiction-Reading-Groups": "Fiction Reading Groups", "Sci-Fi---Fantasy-Discussions": "Sci-Fi & Fantasy Discussions", "Storytelling-Nights": "Storytelling Nights", "Writing-Retreats": "Writing Retreats", "Audiobook-Clubs": "Audiobook Clubs", "Book-Launch-Parties": "Book Launch Parties", "Bookstore-Crawls": "Bookstore Crawls", "Classic-Literature-Discussions": "Classic Literature Discussions", "Historical-Fiction-Book-Clubs": "Historical Fiction Book Clubs", "Literary-Workshops": "Literary Workshops", "Mystery---Thriller-Reading-Groups": "Mystery & Thriller Reading Groups", "Non-Fiction-Book-Discussions": "Non-Fiction Book Discussions", "Poetry-Slam-Competitions": "Poetry Slam Competitions", "Women-s-Writing-Circles": "Women's Writing Circles", "Acoustic": "Acoustic", "Blues": "Blues", "Classical": "Classical", "Country": "Country", "EDM": "EDM", "Folk": "Folk", "Hip-Hop": "Hip-Hop", "Indie": "Indie", "Jazz": "Jazz", "Karaoke": "Karaoke", "Metal": "Metal", "Pop": "Pop", "R-B": "R&B", "Rap": "Rap", "Reggae": "Reggae", "Rock": "Rock", "Singer-Songwriter": "Singer-Songwriter", "Soul": "Soul", "Swing": "Swing", "Techno": "Techno", "Adventure-Travel": "Adventure Travel", "Beach-Activities": "Beach Activities", "Bird-Watching": "Bird Watching", "Camping": "Camping", "Canoeing": "Canoeing", "Fishing": "Fishing", "Horseback-Riding": "Horseback Riding", "Kayaking": "Kayaking", "Mountain-Biking": "Mountain Biking", "Nature-Walks": "Nature Walks", "Outdoor-Photography": "Outdoor Photography", "Picnics": "Picnics", "Rafting": "Rafting", "Rock-Climbing": "Rock Climbing", "Skiing": "Skiing", "Snowboarding": "Snowboarding", "Stargazing": "Stargazing", "Wildlife-Viewing": "Wildlife Viewing", "Beach-Parties": "Beach Parties", "Costume-Parties": "Costume Parties", "Dance-Parties": "Dance Parties", "Disco-Nights": "Disco Nights", "Firework-Displays": "Firework Displays", "Glow-in-the-Dark-Parties": "Glow-in-the-Dark Parties", "House-Parties": "House Parties", "Ice-Cream-Socials": "Ice Cream Socials", "Jungle-Parties": "Jungle Parties", "Karaoke-Nights": "Karaoke Nights", "Lantern-Festivals": "Lantern Festivals", "Masquerade-Balls": "Masquerade Balls", "Masquerade-Soirees": "Masquerade Soirees", "Neon-Parties": "Neon Parties", "Outdoor-Raves": "Outdoor Raves", "Pool-Parties": "Pool Parties", "Rock-Concerts": "Rock Concerts", "Rooftop-Parties": "Rooftop Parties", "Silent-Discos": "Silent Discos", "Swing-Dance-Nights": "Swing Dance Nights", "Theme-Parties": "Theme Parties", "Trivia-Nights": "Trivia Nights", "VIP-Galas": "VIP Galas", "Yacht-Parties": "Yacht Parties", "Blind-Date-Events": "Blind Date Events", "Candlelit-Dinners": "Candlelit Dinners", "Couples--Retreats": "<PERSON><PERSON><PERSON>' Retreats", "Dance-Classes-for-Couples": "Dance Classes for <PERSON><PERSON><PERSON>", "Love-Story-Writing": "Love Story Writing", "Romantic-Getaways": "Romantic Getaways", "Romantic-Movie-Nights": "Romantic Movie Nights", "Speed-Dating": "Speed Dating", "Sunset-Dinners": "Sunset Dinners", "Surprise-Proposal-Planning": "Surprise Proposal Planning", "Wine---Romance-Tastings": "Wine & Romance Tastings", "Climate-Change-Advocacy": "Climate Change Advocacy", "Community-Outreach": "Community Outreach", "Diversity---Inclusion": "Diversity & Inclusion", "Environmental-Cleanups": "Environmental Cleanups", "Fundraising-Events": "Fundraising Events", "Human-Rights-Activism": "Human Rights Activism", "LGBTQ--Advocacy": "LGBTQ+ Advocacy", "Mental-Health-Awareness": "Mental Health Awareness", "Political-Activism": "Political Activism", "Social-Justice-Initiatives": "Social Justice Initiatives", "Volunteering": "Volunteering", "Women-Empowerment": "Women Empowerment", "Aerobics": "Aerobics", "Aerial-Arts": "Aerial Arts", "Archery": "Archery", "Badminton": "Bad<PERSON>ton", "Auto-sport": "Auto-sport", "Basketball": "Basketball", "Beach-Volleyball": "Beach Volleyball", "Boxing": "Boxing", "Climbing": "Climbing", "CrossFit": "CrossFit", "Cycling": "Cycling", "Dance": "Dance", "Disc-Golf": "Disc Golf", "Equestrian": "Equestrian", "Fencing": "Fencing", "Football": "Football", "Golf": "Golf", "Hiking": "Hiking", "HIIT": "HIIT", "Jump-Rope": "Jump Rope", "Karate": "<PERSON><PERSON>", "Kickboxing": "Kickboxing", "Martial-Arts": "Martial Arts", "Outdoor-Bootcamps": "Outdoor Bootcamps", "Parkour": "<PERSON>our", "Pilates": "Pilates", "Roller-Skating": "Roller Skating", "Rowing": "Rowing", "Running": "Running", "Skateboarding": "Skateboarding", "Skiing-Snowboarding": "Skiing & Snowboarding", "Spinning": "Spinning", "Stand-up-Paddleboarding": "Stand-up Paddleboarding", "Strength-Training": "Strength Training", "Surfing": "Surfing", "Swimming": "Swimming", "Synchronized-Swimming": "Synchronized Swimming", "Table-Tennis": "Table Tennis", "Tennis": "Tennis", "Trail-Running": "Trail Running", "Trampoline": "Trampoline", "Ultimate-Frisbee": "Ultimate Frisbee", "Volleyball": "Volleyball", "Water-Polo": "Water Polo", "Yoga": "Yoga", "Zumba": "<PERSON><PERSON>", "Backpacking": "Backpacking", "Backcountry-Camping": "Backcountry Camping", "Beach-Getaways": "Beach Getaways", "City-Tours": "City Tours", "Cruise-Vacations": "Cruise Vacations", "Cultural-Immersion": "Cultural Immersion", "Caving": "Caving", "Culinary-Travel": "Culinary Travel", "Desert-Adventures": "Desert Adventures", "Eco-Tourism": "Eco-Tourism", "Glamping": "Glamping", "Hiking-Trails": "Hiking Trails", "Historical-Sites": "Historical Sites", "Island-Hopping": "Island Hopping", "Mountain-Escapes": "Mountain Escapes", "National-Park-Visits": "National Park Visits", "River-Cruises": "River Cruises", "Road-Trips": "Road Trips", "Safari-Adventures": "Safari Adventures", "Scenic-Drives": "Scenic Drives", "Solo-Travel": "Solo Travel", "Spa-Retreats": "Spa Retreats", "Sustainable-Travel": "Sustainable Travel", "Trekking-Expeditions": "Trekking Expeditions", "Underwater-Exploration": "Underwater Exploration", "Urban-Exploration": "Urban Exploration", "Volcanic-Tours": "Volcanic Tours", "Volunteering-Abroad": "Volunteering Abroad", "Wildlife-Encounters": "Wildlife Encounters", "Musical": "Musical", "Exhibition": "Exhibition", "Rugby": "Rugby", "Latin": "Latin", "Children-s-theatre": "Children's theatre", "Recycling-workshops": "Recycling workshops", "Christmas": "Christmas", "New-Year": "New Year", "musical": "Musical", "exhibition": "Exhibition", "rugby": "Rugby", "latin": "Latin", "children_theatre": "Children's theatre", "recycling_workshops": "Recycling Workshops", "christmas": "Christmas", "new_year": "New Year's", "diner_at_home": "Diner At Home", "indian": "Indian", "mediterranean": "Mediterranean", "thai": "Thai", "mexican": "Mexican", "vegetarian": "Vegetarian", "vegan": "Vegan", "blues": "Blues", "fairy_tales_reading": "Fairy Tales Reading", "sailing": "Sailing", "squash": "Squash", "religious": "Religious", "easter": "Easter", "thanksgiving": "Thanksgiving", "panygyri": "Panygyri", "bazaar": "<PERSON><PERSON><PERSON>", "chinese_new_year": "Chinese New Year", "tango": "Tango", "latin_dance": "Latin"}}