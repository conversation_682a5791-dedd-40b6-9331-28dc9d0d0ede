import React from 'react';
import Svg, {Path} from 'react-native-svg';

interface RefreshIconProps {
  width?: number;
  height?: number;
  color?: string;
  style?: any;
}

const RefreshIcon: React.FC<RefreshIconProps> = ({
  width = 24,
  height = 24,
  color = '#666666',
  style,
}) => (
  <Svg width={width} height={height} viewBox="0 0 24 24" fill="none" style={style}>
    {/* Refresh arrow path */}
    <Path
      d="M3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12C21 16.97 16.97 21 12 21C9.5 21 7.26 19.94 5.63 18.31"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    {/* Arrow head */}
    <Path
      d="M3 18L5.63 18.31L6 15.69"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

export default RefreshIcon;
