import React from 'react';
import Svg, {Path} from 'react-native-svg';

interface AlertTriangleIconProps {
  width?: number;
  height?: number;
  color?: string;
  style?: any;
}

const AlertTriangleIcon: React.FC<AlertTriangleIconProps> = ({
  width = 24,
  height = 24,
  color = '#666666',
  style,
}) => (
  <Svg width={width} height={height} viewBox="0 0 24 24" fill="none" style={style}>
    {/* Triangle outline */}
    <Path
      d="M10.29 3.86L1.82 18C1.64 18.37 1.64 18.82 1.82 19.19C2 19.56 2.37 19.78 2.77 19.78H21.23C21.63 19.78 22 19.56 22.18 19.19C22.36 18.82 22.36 18.37 22.18 18L13.71 3.86C13.53 3.49 13.16 3.27 12.76 3.27C12.36 3.27 11.99 3.49 11.81 3.86H10.29Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    {/* Exclamation mark */}
    <Path
      d="M12 9V13"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    {/* Dot */}
    <Path
      d="M12 17H12.01"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

export default AlertTriangleIcon;
