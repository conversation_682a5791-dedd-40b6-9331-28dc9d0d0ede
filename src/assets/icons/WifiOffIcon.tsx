import React from 'react';
import Svg, {Path} from 'react-native-svg';

interface WifiOffIconProps {
  width?: number;
  height?: number;
  color?: string;
  style?: any;
}

const WifiOffIcon: React.FC<WifiOffIconProps> = ({
  width = 24,
  height = 24,
  color = '#666666',
  style,
}) => (
  <Svg width={width} height={height} viewBox="0 0 24 24" fill="none" style={style}>
    {/* WiFi signal lines with slash through them */}
    <Path
      d="M1 9L23 9M12 21L12 17M8.5 13.5L15.5 13.5M5 17L19 17"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      opacity="0.3"
    />
    {/* Diagonal slash line */}
    <Path
      d="M3 3L21 21"
      stroke={color}
      strokeWidth="2.5"
      strokeLinecap="round"
    />
    {/* WiFi icon base */}
    <Path
      d="M1 9C4.97 5.03 10.47 3 16 3C18.76 3 21.4 3.5 23.64 4.42"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      opacity="0.4"
    />
    <Path
      d="M5 13C7.76 10.24 11.76 9 16 9C18.12 9 20.16 9.4 22 10.1"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      opacity="0.4"
    />
    <Path
      d="M9 17C10.66 15.34 13.34 15.34 15 17"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      opacity="0.4"
    />
    {/* Center dot */}
    <Path
      d="M12 21H12.01"
      stroke={color}
      strokeWidth="3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

export default WifiOffIcon;
