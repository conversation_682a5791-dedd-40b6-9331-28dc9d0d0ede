import * as React from 'react';
import {useEffect, useState} from 'react';
import {Appearance, Image, PermissionsAndroid, Platform, Text, View} from 'react-native';
import Config from 'react-native-config';
import FastImage from 'react-native-fast-image';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import OneSignal from 'react-native-onesignal';
import {QueryClientProvider} from 'react-query';
import '~i18n';
import Navigation from './navigation/Navigation';
import {useLanguage} from './navigation/SettingsStack/zustand';
// import * as Sentry from '@sentry/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import {useTranslation} from 'react-i18next';
import {StatusBar} from 'react-native';
import {KeyboardProvider} from 'react-native-keyboard-controller';
import {NotifierWrapper} from 'react-native-notifier';
import SplashScreen from 'react-native-splash-screen';
import {getFcmToken} from '~Utils/fcmHelper';
import FirebaseChatsService from '~services/FirebaseChats';
import {StripeProvider} from '@stripe/stripe-react-native';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import {ThemeProvider} from '~contexts/ThemeContext';
import {initializeGlobalErrorHandler} from '~Utils/globalErrorHandler';
import './../../Utils/fetchIntercept';
import {startPerformanceLogging} from '~Utils/performance/performanceMonitor';
import PrefetchService from '~services/performance/PrefetchService';
import { createOptimizedQueryClient } from '~Utils/performance';
import { appStartup, timing } from '~Utils/debugLogger';

// Sentry.init({
//   dsn: 'https://<EMAIL>/4505561115131904',
//   debug: false,
//   release: DeviceInfo.getApplicationName() + '@' + DeviceInfo.getVersion() + '(' + DeviceInfo.getBuildNumber() + ')',
//   tracesSampleRate: 1,
// });
const queryClient = createOptimizedQueryClient();

// Initialize performance monitoring and prefetching
const prefetchService = new PrefetchService(queryClient);

// Start performance logging in development
if (__DEV__) {
  startPerformanceLogging(30000); // Log every 30 seconds in dev
}

const isAndroid = Platform.OS === 'android';

export default function App() {
  // OneSignal Initialization
  OneSignal.setLogLevel(6, 0);
  OneSignal.setAppId(Config.ONE_SIGNAL_APP_ID);

  // Request permissions for both iOS and Android
  if (!isAndroid) {
    OneSignal.promptForPushNotificationsWithUserResponse();
  } else {
    // For Android, ensure push is enabled by default
    OneSignal.disablePush(false);
  }
  //END OneSignal Init Code

  console.log('🚀 [DEBUG] App component started rendering - basic log');
  try {
    appStartup('App component started rendering');
  } catch (error) {
    console.error('❌ [DEBUG] Error with appStartup logger:', error);
  }
  const appStartTime = Date.now();

  // Initialize global error handler for view hierarchy errors
  React.useEffect(() => {
    initializeGlobalErrorHandler();
  }, []);

  const {i18n} = useTranslation();
  const {setLanguage, language} = useLanguage();

  const [darkTheme, setDarkTheme] = useState(false);

  useEffect(() => {
    // Subscribe to theme changes
    const themeListener = Appearance.addChangeListener(({colorScheme}) => {
      setDarkTheme(colorScheme === 'dark');
    });

    // Cleanup listener
    return () => themeListener.remove();
  }, []);

  useEffect(() => {
    const fetchAndSaveFcmToken = async () => {
      const deviceFcmToken = await getFcmToken();
      console.log('deviceFcmToken', deviceFcmToken);
      if (deviceFcmToken) {
        await FirebaseChatsService.updateUserFcmToken(deviceFcmToken);
      }
    };

    fetchAndSaveFcmToken();
  }, []);

  //Method for handling notifications received while app in foreground
  // Handle notifications received while the app is in the foreground
  // useEffect(() => {
  //   handleForegroundNotification();
  // }, []);

  //Method for handling notifications opened
  useEffect(() => {
    appStartup('Starting image preloading');
    const imagePreloadStart = Date.now();

    const images = [
      require('~assets/images/personalImage.jpg'),
      require('~assets/images/businessImage.jpg'),
      require('~assets/images/SignInImage.png'),
      require('~assets/images/eventPhotoPlaceholder.png'),
      require('~assets/images/mainSplashImage.jpg'),
    ];
    const uris = images.map(image => ({
      uri: Image.resolveAssetSource(image).uri,
    }));

    FastImage.preload(uris);
    timing('Image preloading initiated', imagePreloadStart);
  }, []);

  useEffect(() => {
    appStartup('Setting up splash screen hide timer');
    const timeout = setTimeout(() => {
      appStartup('Hiding splash screen');
      SplashScreen.hide();
      timing('Total app startup time', appStartTime);
    }, 200);
    return () => clearTimeout(timeout);
  }, [appStartTime]);

  React.useEffect(() => {
    if (Platform.OS === 'android') {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_CALENDAR, {
        title: 'Calendar Permission',
        message: 'App needs access to your calendar to create events.',
        buttonPositive: 'OK',
      });
    }
  }, []);

  useEffect(() => {
    AsyncStorage.getItem('language-storage').then((item: any) => {
      const locale = JSON.parse(item).state?.language;
      i18n.changeLanguage(locale);
      setLanguage(locale);
    });
  }, [language]);

  // OneSignal subscription management
  useEffect(() => {
    const initializeOneSignal = async () => {
      try {
        // Check if user was previously opted out and re-enable if needed
        const deviceState = await OneSignal.getDeviceState();
        console.log('OneSignal Device State:', deviceState);

        // If push is disabled, check if we should re-enable it
        if (deviceState?.isPushDisabled) {
          // Check if user has notifications enabled in settings
          const notificationSettings = await AsyncStorage.getItem('notifications-storage');
          if (notificationSettings) {
            const settings = JSON.parse(notificationSettings);
            if (settings.state?.isNotificationsEnabled !== false) {
              // Re-enable push notifications
              OneSignal.disablePush(false);
              console.log('✅ OneSignal push notifications re-enabled');
            }
          } else {
            // Default to enabled for new users
            OneSignal.disablePush(false);
            console.log('✅ OneSignal push notifications enabled for new user');
          }
        }
      } catch (error) {
        console.error('❌ OneSignal initialization error:', error);
      }
    };

    initializeOneSignal();
  }, []);

  // Initialize background prefetching
  useEffect(() => {
    const initializePrefetching = async () => {
      try {
        // Start prefetching popular content after app loads
        await prefetchService.prefetchPopularEvents({priority: 'medium', delay: 3000});
        await prefetchService.prefetchSubcategories({priority: 'low', delay: 5000});

        console.log('🚀 Background prefetching initialized');
      } catch (error) {
        console.warn('Failed to initialize prefetching:', error);
      }
    };

    // Delay initialization to not block app startup
    const timer = setTimeout(initializePrefetching, 2000);
    return () => clearTimeout(timer);
  }, []);

  // Note: Appearance.setColorScheme is deprecated and causes UI thread errors
  // Theme management is now handled by ThemeContext
  // useEffect(() => {
  //   Appearance.setColorScheme('light');
  // }, []);
  // useEffect(() => {
  //   if (userLocation?.coords?.latitude && userLocation?.coords?.longitude && userLocation?.locationName) {
  //     setCurrentPositionState({
  //       latitude: userLocation.coords.latitude,
  //       longitude: userLocation.coords.longitude,
  //       address: userLocation.locationName,
  //     });
  //   }
  // }, [userLocation?.locationName, userLocation?.coords.longitude, userLocation?.coords.latitude]);

  return (
    <ThemeProvider>
      <KeyboardProvider>
        <StripeProvider publishableKey={Config.STRIPE_PUBLISH_KEY}>
          <QueryClientProvider client={queryClient}>
            <GestureHandlerRootView style={{flex: 1}}>
              <NotifierWrapper>
                <Navigation />
              </NotifierWrapper>
            </GestureHandlerRootView>
          </QueryClientProvider>
        </StripeProvider>
      </KeyboardProvider>
    </ThemeProvider>
  );
}
