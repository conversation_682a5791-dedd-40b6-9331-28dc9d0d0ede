import {RouteProp, useRoute} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {SCREENS} from '~constants';
import {RootStackParamsList} from '~types/navigation/navigation.type';
import {chatScreens} from '../screens';

const Stack = createNativeStackNavigator();

export const ChatStack = () => {
  console.log('🔍 ChatStack: Component started rendering');
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.CHAT_STACK>>();

  console.log('🔍 ChatStack: Rendering with params:', params);
  console.log('🔍 ChatStack: Route params type:', typeof params);
  console.log('🔍 ChatStack: Route params keys:', params ? Object.keys(params) : 'no params');

  // Determine initial screen and params based on the route params
  const getInitialScreen = () => {
    if (params?.chatType === 'AdminChat') {
      return SCREENS.CONCIERGE_CHAT;
    }
    if (params?.key) {
      return SCREENS.USER_CHAT;
    }
    return SCREENS.CHATS; // Default to chat list
  };

  const getInitialParams = () => {
    if (params?.chatType === 'AdminChat') {
      return {};
    }
    if (params?.key) {
      return {
        chatId: params.key,
        image: params.image || '',
        userName: params.userName || 'Chat',
        fromEventId: params.fromEventId,
        fromScreen: params.fromScreen,
      };
    }
    return {};
  };

  const initialRouteName = getInitialScreen();
  const initialParams = getInitialParams();

  console.log('🔍 ChatStack: Initial route:', initialRouteName, 'with params:', initialParams);

  return (
    <Stack.Navigator screenOptions={{headerShown: false}} initialRouteName={initialRouteName}>
      {chatScreens.map(screen => (
        <Stack.Screen
          name={screen.name}
          key={screen.name}
          component={screen.component}
          options={screen.options}
          initialParams={screen.name === initialRouteName ? initialParams : undefined}
        />
      ))}
    </Stack.Navigator>
  );
};
