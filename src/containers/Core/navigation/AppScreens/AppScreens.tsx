import React, {useCallback, useEffect, useState, useRef} from 'react';
import {Alert, Platform, Text, View, StyleSheet, Linking} from 'react-native';

import {SCREENS} from '~constants';
import {appScreens} from '../screens';
import {AnimatedTabBarNavigator, TabElementDisplayOptions} from 'react-native-animated-nav-tab-bar';
import useTabBar from './zustand';
import {useTranslation} from 'react-i18next';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import OneSignal from 'react-native-onesignal';
import {registerListenerWithFCM} from '~Utils/fcmHelper';
import useDeepLinking from './useDeepLinking';
import firestore from '@react-native-firebase/firestore';
import {ChatType, ChatTypeWithKey} from '~types/chat';
import {useTheme} from '~contexts/ThemeContext';

const Tabs = AnimatedTabBarNavigator();

const AppScreens = () => {
  const {colors} = useTheme();
  const {isTabBarDisabled} = useTabBar();
  const uid = auth().currentUser?.uid;
  const {t} = useTranslation();
  useDeepLinking();
  const {refetch} = useGetUserAccount(auth().currentUser!.uid);
  const {navigate} = useNavigation<NavigationProps>();
  const [unreadChatCount, setUnReadChatCount] = useState<number | null>(null);
  const isMountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (uid) {
      const unsubscribe = firestore()
        .collection('chats')
        .where('userIds', 'array-contains', uid)
        .onSnapshot(querySnapshot => {
          // Check if component is still mounted before updating state
          if (!isMountedRef.current) {
            return;
          }

          const conversations: ChatTypeWithKey[] = [];
          let unReadCount = 0;
          querySnapshot.forEach(documentSnapshot => {
            const data = documentSnapshot.data() as ChatType; // Cast Firestore data to our ChatData type
            conversations.push({
              ...data,
              key: documentSnapshot.id,
            });
            const unread = data?.history.filter(
              message => message?.readUserIds && !message.readUserIds.includes(uid),
            ).length;
            unReadCount = unReadCount + unread;
          });
          setUnReadChatCount(unReadCount);
        });

      // Clean up subscription on unmount
      return () => unsubscribe();
    }
  }, [uid]);

  useEffect(() => {
    const unsubscribe = registerListenerWithFCM();
    return unsubscribe;
  }, []);

  const openEvent = useCallback(
    async (eventID: string) => {
      const data = await refetch();
      console.log(eventID);

      if (data?.data) {
        navigate(SCREENS.HOME_STACK, {eventId: Number(eventID)});
        return;
      }
      Alert.alert('No permission to event');
    },
    [navigate, refetch],
  );

  useEffect(() => {
    const handleDeepLink = ({url}: {url: string}) => {
      console.log(url);
      const parts = url.split('/').filter(item => item);
      console.log(parts);
      const eventID = parts[parts.length - 1];
      console.log(eventID);
      openEvent(eventID);
    };
    // Check for the initial deep link when the app is launched
    const checkInitialURL = async () => {
      const initialUrl = await Linking.getInitialURL();
      if (initialUrl) {
        console.log(`Initial deep link URL: ${initialUrl}`);
        handleDeepLink({url: initialUrl});
      }
    };

    // Add an event listener for deep linking when the app is already running
    const linkingListener = Linking.addListener('url', handleDeepLink);

    checkInitialURL();

    return () => {
      // Clean up the event listeners
      linkingListener.remove();
    };
  }, [openEvent]);

  const openChat = useCallback(
    async ({chatId, chatType}: {chatId: string; chatType: string}) => {
      const data = await refetch();
      if (data?.data) {
        navigate(SCREENS.CHAT_STACK, {key: chatId, chatType});
        return;
      }
      Alert.alert('No permission to chat');
    },
    [navigate, refetch],
  );

  useEffect(() => {
    OneSignal.setNotificationOpenedHandler(notification => {
      const additionalData = notification.notification.additionalData as {
        type?: string;
        chat_id?: string;
        event_id?: string;
        refineSubcategories?: boolean;
      };
      console.log(additionalData, 'additionalDataadditionalData');
      if (additionalData?.chat_id || additionalData?.type === 'AdminChat') {
        openChat({
          chatId: additionalData?.chat_id || '',
          chatType: additionalData?.type || '',
        });
      } else if (additionalData?.event_id && additionalData?.type === 'EVENT_UPDATED') {
        navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
      } else if (additionalData?.event_id && additionalData?.type === 'EVENT_COMMENT') {
        navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
      } else if (additionalData?.event_id && additionalData?.type === 'EVENT_CANCELLED') {
        navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
      } else if (additionalData?.event_id && additionalData?.type === 'EVENT_REQUEST_UPDATED') {
        navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
      } else if (additionalData?.event_id && additionalData?.type === 'EVENT_REMINDER') {
        navigate(SCREENS.HOME_EVENT, {eventId: Number(additionalData.event_id)});
      } else if (additionalData?.event_id && additionalData?.type === 'EVENT_REVIEW_REMINDER') {
        const data = {
          event_id: additionalData.event_id,
        };
        navigate(SCREENS.RATE_EVENT, {event: data});
      } else if (additionalData?.event_id && additionalData?.type === 'EVENT_REQUESTING') {
        navigate(SCREENS.PENDING_ATTENDEES, {eventId: Number(additionalData.event_id), eventName: 'Event'});
      } else if (additionalData?.type === 'EVENT_DELETED') {
        return;
      } else if (additionalData?.refineSubcategories) {
        navigate(SCREENS.SETTINGS_STACK, {refineSubcategories: additionalData?.refineSubcategories});
      }
    });
  }, [navigate, openChat, openEvent]);

  const styles = StyleSheet.create({
    badgeContainer: {
      position: 'absolute',
      top: -5,
      right: -5,
      backgroundColor: colors.error, // Keep red for notifications
      borderRadius: 10,
      width: 20,
      height: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    badgeText: {
      color: colors.white,
      fontSize: 12,
      fontWeight: 'bold',
    },
    iconContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      maxHeight: 25,
    },
    iconWrapper: {
      backgroundColor: colors.gray400 + '1A',
      borderRadius: 25,
      padding: 10,
    },
    focusedIconWrapper: {
      backgroundColor: colors.primary, // Keep primary color
    },
    iconText: {
      marginLeft: 10,
      fontWeight: '600',
    },
  });

  return (
    <Tabs.Navigator
      screenOptions={{
        unmountOnBlur: true,
        lazy: false,
      }}
      tabBarOptions={{
        activeTintColor: colors.white,
        inactiveTintColor: colors.white,
        activeBackgroundColor: colors.black,
      }}
      appearance={{
        isDisabled: isTabBarDisabled,
        floating: true,
        tabBarBackground:
          Platform.OS === 'ios'
            ? `${colors.surface}B3` // 70% opacity
            : `${colors.surface}B3`, // 70% opacity
        whenActiveShow: TabElementDisplayOptions.ICON_ONLY,
        shadow: true,
        dotCornerRadius: 100,
      }}
      initialRouteName={SCREENS.HOME_STACK}>
      {appScreens.map(screen => (
        <Tabs.Screen
          component={screen.component}
          key={screen.name}
          name={screen.name}
          listeners={() => ({})}
          initialParams={screen.params}
          options={{
            tabBarIcon: ({focused}: {focused: boolean}) => {
              // Use theme-aware colors with slight grey for focused icons
              const iconColor = focused ? colors.white : colors.black;

              return (
                <View style={styles.iconContainer}>
                  <View style={[styles.iconWrapper, focused && styles.focusedIconWrapper]}>
                    {focused ? <screen.iconBar color={iconColor} /> : <screen.icon color={iconColor} />}
                    {/* Add the Badge with Unread Count */}
                    {!!unreadChatCount && screen.name === 'SCREEN:CHAT_STACK' && (
                      <View style={styles.badgeContainer}>
                        <Text style={styles.badgeText}>{unreadChatCount + ''}</Text>
                      </View>
                    )}
                  </View>
                  {focused && <Text style={[styles.iconText, {color: colors.white}]}>{t(screen.title)}</Text>}
                </View>
              );
            },
          }}
        />
      ))}
    </Tabs.Navigator>
  );
};

export default AppScreens;
