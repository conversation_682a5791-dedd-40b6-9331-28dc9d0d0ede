import AsyncStorage from '@react-native-async-storage/async-storage';
import OneSignal from 'react-native-onesignal';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';
import {immer} from 'zustand/middleware/immer';

interface NotificationState {
  isNotificationsEnabled: boolean;
}

interface NotificationsActions {
  setIsNotificationsEnabled: (value: boolean) => void;
}

interface LanguageState {
  language: string;
}

interface LanguageActions {
  setLanguage: (value: string) => void;
}

const useNotifications = create<NotificationState & NotificationsActions>()(
  persist(
    immer(set => ({
      isNotificationsEnabled: true,
      setIsNotificationsEnabled: async (value: boolean) => {
        try {
          OneSignal.disablePush(!value);
          console.log(`✅ OneSignal push notifications ${value ? 'enabled' : 'disabled'}`);

          // Verify the state was set correctly
          setTimeout(async () => {
            const deviceState = await OneSignal.getDeviceState();
            console.log('OneSignal device state after toggle:', deviceState);
          }, 1000);

          set({
            isNotificationsEnabled: value,
          });
        } catch (error) {
          console.error('❌ Failed to toggle OneSignal notifications:', error);
        }
      },
    })),
    {
      name: 'notifications-storage',
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);

const useLanguage = create<LanguageState & LanguageActions>()(
  persist(
    immer(set => ({
      language: 'en',
      setLanguage: (value: string) =>
        set({
          language: value,
        }),
    })),
    {name: 'language-storage', storage: createJSONStorage(() => AsyncStorage)},
  ),
);

export {useNotifications, useLanguage};
