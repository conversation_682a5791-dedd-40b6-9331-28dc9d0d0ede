import React, {useEffect, useRef} from 'react';
import {View, Text, Animated, Easing, useWindowDimensions} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import {useNavigation} from '@react-navigation/native';
import {PyxiLabel} from '~assets/icons';
import {useTheme} from '~contexts/ThemeContext';
import {ThemedStatusBar} from '~components/ThemedStatusBar';
import {spacing, typography, shadows} from '~constants/design';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';

const MagicalSplash: React.FC = () => {
  const {colors} = useTheme();
  const {width, height} = useWindowDimensions();
  const navigation = useNavigation<NavigationProps>();

  // Core animations
  const logoScale = useRef(new Animated.Value(0.3)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const logoRotation = useRef(new Animated.Value(0)).current;
  const textOpacity = useRef(new Animated.Value(0)).current;
  const backgroundOpacity = useRef(new Animated.Value(0)).current;

  // Magical effects
  const shimmerPosition = useRef(new Animated.Value(-width)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const glowAnimation = useRef(new Animated.Value(0)).current;

  // Simplified floating particles (3 particles)
  const particles = useRef(
    Array.from({length: 3}, () => ({
      translateY: new Animated.Value(height),
      opacity: new Animated.Value(0),
      scale: new Animated.Value(0.8),
    })),
  ).current;

  // Simplified floating shapes
  const floatingShapes = useRef(
    Array.from({length: 3}, () => ({
      translateY: new Animated.Value(0),
      opacity: new Animated.Value(0.2),
      scale: new Animated.Value(1),
    })),
  ).current;

  useEffect(() => {
    // Magical particle system
    const startParticleSystem = () => {
      particles.forEach((particle, index) => {
        const delay = index * 150;
        const duration = 3000 + Math.random() * 2000;

        Animated.loop(
          Animated.sequence([
            Animated.delay(delay),
            Animated.parallel([
              Animated.timing(particle.translateY, {
                toValue: -100,
                duration,
                easing: Easing.out(Easing.cubic),
                useNativeDriver: true,
              }),
              Animated.timing(particle.opacity, {
                toValue: 0.8,
                duration: duration * 0.3,
                useNativeDriver: true,
              }),
            ]),
            Animated.timing(particle.opacity, {
              toValue: 0,
              duration: duration * 0.2,
              useNativeDriver: true,
            }),
          ]),
        ).start();
      });
    };

    // Simplified floating shapes
    const startFloatingShapes = () => {
      floatingShapes.forEach((shape, index) => {
        const amplitude = 20 + index * 10;

        // Simple vertical floating
        Animated.loop(
          Animated.sequence([
            Animated.timing(shape.translateY, {
              toValue: amplitude,
              duration: 2000 + index * 500,
              easing: Easing.inOut(Easing.quad),
              useNativeDriver: true,
            }),
            Animated.timing(shape.translateY, {
              toValue: -amplitude,
              duration: 2000 + index * 500,
              easing: Easing.inOut(Easing.quad),
              useNativeDriver: true,
            }),
          ]),
        ).start();
      });
    };

    // Shimmer effect
    const startShimmerEffect = () => {
      Animated.loop(
        Animated.timing(shimmerPosition, {
          toValue: width + 100,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
      ).start();
    };

    // Glow effect
    const startGlowEffect = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnimation, {
            toValue: 1,
            duration: 1500,
            easing: Easing.inOut(Easing.quad),
            useNativeDriver: true,
          }),
          Animated.timing(glowAnimation, {
            toValue: 0,
            duration: 1500,
            easing: Easing.inOut(Easing.quad),
            useNativeDriver: true,
          }),
        ]),
      ).start();
    };

    // Main animation sequence
    const mainSequence = Animated.sequence([
      // Background appears
      Animated.timing(backgroundOpacity, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      // Logo dramatic entrance
      Animated.parallel([
        Animated.timing(logoScale, {
          toValue: 1,
          duration: 1200,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 800,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(logoRotation, {
          toValue: 360,
          duration: 1200,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
      ]),
      // Text appears with delay
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 600,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]);

    // Pulse animation for logo
    const pulseLoop = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1500,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1500,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
      ]),
    );

    // Start all animations
    mainSequence.start();
    startParticleSystem();
    startFloatingShapes();
    startShimmerEffect();
    startGlowEffect();

    setTimeout(() => {
      pulseLoop.start();
    }, 2000);

    // Navigate after magical experience
    const timer = setTimeout(() => {
      navigation.replace(SCREENS.MODERN_INTRO);
    }, 4000);

    return () => {
      clearTimeout(timer);
      pulseLoop.stop();
    };
  }, [navigation]);

  const styles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    gradientBackground: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    magicalOverlay: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'transparent',
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      paddingHorizontal: spacing.xl,
    },
    logoContainer: {
      alignItems: 'center' as const,
      marginBottom: spacing['2xl'],
      position: 'relative' as const,
    },
    logoWrapper: {
      transform: [
        {
          scale: logoScale,
        },
        {
          rotateZ: logoRotation.interpolate({
            inputRange: [0, 360],
            outputRange: ['0deg', '360deg'],
          }),
        },
      ],
    },
    logoGlow: {
      position: 'absolute' as const,
      top: -20,
      left: -20,
      right: -20,
      bottom: -20,
      borderRadius: 100,
      backgroundColor: colors.primary,
      ...shadows['2xl'],
    },
    textContainer: {
      alignItems: 'center' as const,
      position: 'relative' as const,
    },
    appName: {
      fontSize: typography.fontSize['5xl'],
      fontWeight: typography.fontWeight.black,
      color: colors.textInverse,
      textAlign: 'center' as const,
      marginBottom: spacing.sm,
      letterSpacing: -1,
      textShadowColor: colors.black + '40',
      textShadowOffset: {width: 0, height: 4},
      textShadowRadius: 8,
    },
    tagline: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.semibold,
      color: colors.infoDark,
      textAlign: 'center' as const,
      letterSpacing: 1,
      opacity: 0.95,
      textShadowColor: colors.black + '30',
      textShadowOffset: {width: 0, height: 2},
      textShadowRadius: 4,
    },
    shimmerOverlay: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'transparent',
    },
    shimmerGradient: {
      position: 'absolute' as const,
      top: 0,
      bottom: 0,
      width: 100,
      transform: [{translateX: shimmerPosition}],
    },
    particle: {
      position: 'absolute' as const,
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: colors.textInverse,
    },
    floatingShape: {
      position: 'absolute' as const,
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: colors.textInverse + '20',
      borderWidth: 1,
      borderColor: colors.textInverse + '30',
    },
  };

  return (
    <SafeAreaView style={styles.container} edges={[]}>
      <ThemedStatusBar translucent />

      {/* Animated Background */}
      <Animated.View style={[styles.gradientBackground, {opacity: backgroundOpacity}]}>
        <LinearGradient
          colors={[
            colors.primary, // Your brand orange
            '#FFB347', // Peach
            '#FFD700', // Gold
            '#FF7F50', // Coral
            colors.primaryLight, // Lighter brand orange
          ]}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
          style={styles.gradientBackground}
        />
      </Animated.View>

      {/* Floating Particles */}
      {particles.map((particle, index) => (
        <Animated.View
          key={`particle-${index}`}
          style={[
            styles.particle,
            {
              left: 50 + index * 100,
              transform: [{translateY: particle.translateY}, {scale: particle.scale}],
              opacity: particle.opacity,
            },
          ]}
        />
      ))}

      {/* Floating Geometric Shapes */}
      {floatingShapes.map((shape, index) => (
        <Animated.View
          key={`shape-${index}`}
          style={[
            styles.floatingShape,
            {
              left: 50 + index * (width / 4),
              top: 100 + index * 150,
              transform: [{translateY: shape.translateY}, {scale: shape.scale}],
              opacity: shape.opacity,
            },
          ]}
        />
      ))}

      {/* Shimmer Effect */}
      <View style={styles.shimmerOverlay}>
        <Animated.View style={styles.shimmerGradient}>
          <LinearGradient
            colors={['transparent', colors.textInverse + '30', 'transparent']}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}
            style={{flex: 1}}
          />
        </Animated.View>
      </View>

      {/* Content */}
      <View style={styles.contentContainer}>
        {/* Logo with Glow */}
        <Animated.View style={[styles.logoContainer, {opacity: logoOpacity}]}>
          <Animated.View
            style={[
              styles.logoGlow,
              {
                opacity: glowAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 0.3],
                }),
              },
            ]}
          />
          <Animated.View style={styles.logoWrapper}>
            <PyxiLabel />
          </Animated.View>
        </Animated.View>

        {/* Text */}
        <Animated.View style={[styles.textContainer, {opacity: textOpacity}]}>
          <Text style={styles.tagline}>Discover • Connect • Experience</Text>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
};

export default MagicalSplash;
