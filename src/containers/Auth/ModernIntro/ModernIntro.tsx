import React, {useState, useRef, useEffect} from 'react';
import {View, Text, ScrollView, Dimensions, TouchableOpacity, Platform} from 'react-native';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {PyxiLabel} from '~assets/icons';
import ModernButton from '~components/ModernButton';
import {FadeIn, ScaleIn} from '~components/MicroInteractions/MicroInteractions';
import {useTheme} from '~contexts/ThemeContext';
import {ThemedStatusBar} from '~components/ThemedStatusBar';
import {spacing, borderRadius, typography, shadows} from '~constants/design';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';
import {logScreenView} from '~Utils/firebaseAnalytics';

const {width, height} = Dimensions.get('window');

interface IntroSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
}

const ModernIntro: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();
  const scrollViewRef = useRef<ScrollView>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const {top} = useSafeAreaInsets();

  useEffect(() => {
    logScreenView('ModernIntro', 'ModernIntro');
  }, []);

  const slides: IntroSlide[] = [
    {
      id: 0,
      title: 'Discover Events',
      subtitle: 'Find amazing experiences near you',
      description:
        'Explore thousands of curated events happening around you, from intimate concerts to inspiring workshops',
      icon: '🎭',
    },
    {
      id: 1,
      title: 'Connect & Meet',
      subtitle: 'Build meaningful connections',
      description:
        'Meet like-minded people who share your passions and create lasting friendships through shared experiences',
      icon: '🤝',
    },
    {
      id: 2,
      title: 'Create Memories',
      subtitle: 'Live unforgettable moments',
      description: 'Turn every event into a magical memory with the vibrant Pyxi community by your side',
      icon: '✨',
    },
  ];

  const handleScroll = (event: any) => {
    const slideIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    setCurrentSlide(slideIndex);
  };

  const goToSlide = (index: number) => {
    scrollViewRef.current?.scrollTo({x: index * width, animated: true});
    setCurrentSlide(index);
  };

  const handleNext = () => {
    if (currentSlide < slides.length - 1) {
      goToSlide(currentSlide + 1);
    } else {
      navigation.navigate(SCREENS.LOGIN);
    }
  };

  const handleSkip = () => {
    navigation.navigate(SCREENS.LOGIN);
  };

  const styles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    slideContainer: {
      width,
      height,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      paddingHorizontal: spacing.xl,
    },
    gradientBackground: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      paddingTop: spacing['4xl'],
    },
    logoContainer: {
      marginBottom: spacing['4xl'],
      transform: [{scale: 1.2}],
    },
    iconContainer: {
      width: 140,
      height: 140,
      borderRadius: 70,
      backgroundColor: colors.primary + '15',
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      marginBottom: spacing['3xl'],
      ...shadows['2xl'],
      borderWidth: 2,
      borderColor: colors.primary + '30',
      position: 'relative' as const,
    },
    iconText: {
      fontSize: 70,
      textShadowColor: colors.black + '20',
      textShadowOffset: {width: 0, height: 2},
      textShadowRadius: 4,
    },
    titleText: {
      fontSize: typography.fontSize['5xl'],
      fontWeight: typography.fontWeight.black,
      color: colors.textPrimary,
      textAlign: 'center' as const,
      marginBottom: spacing.lg,
      letterSpacing: -1,
      textShadowColor: colors.primary + '20',
      textShadowOffset: {width: 0, height: 2},
      textShadowRadius: 4,
    },
    subtitleText: {
      fontSize: typography.fontSize['2xl'],
      fontWeight: typography.fontWeight.bold,
      color: colors.primary,
      textAlign: 'center' as const,
      marginBottom: spacing.xl,
      letterSpacing: 0.5,
      textShadowColor: colors.primary + '15',
      textShadowOffset: {width: 0, height: 1},
      textShadowRadius: 2,
    },
    descriptionText: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.medium,
      color: colors.textSecondary,
      textAlign: 'center' as const,
      lineHeight: typography.fontSize.xl * 1.6,
      marginBottom: spacing['4xl'],
      paddingHorizontal: spacing.md,
      letterSpacing: 0.2,
    },
    bottomContainer: {
      position: 'absolute' as const,
      bottom: spacing['4xl'],
      left: spacing.xl,
      right: spacing.xl,
    },
    paginationContainer: {
      flexDirection: 'row' as const,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      marginBottom: spacing.xl,
    },
    paginationDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: spacing.xs,
    },
    activeDot: {
      backgroundColor: colors.primary,
      width: 24,
    },
    inactiveDot: {
      backgroundColor: colors.border,
    },
    buttonContainer: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'center' as const,
    },
    skipButton: {
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
    },
    skipText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.textSecondary,
      opacity: 0.8,
    },
    nextButton: {
      flex: 1,
      marginLeft: spacing.lg,
    },
    headerContainer: {
      position: 'absolute' as const,
      top: top + spacing.md, // Use safe area insets to prevent cut-off
      left: spacing.xl,
      right: spacing.xl,
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'center' as const,
      zIndex: 10,
    },
    headerSkipButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      backgroundColor: colors.textInverse + '10',
      borderRadius: borderRadius.full,
      borderWidth: 1,
      borderColor: colors.textInverse + '20',
      minWidth: 44,
      minHeight: 36,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    headerSkipText: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.semibold,
      color: colors.textSecondary,
      opacity: 0.9,
    },
  };

  const renderSlide = (slide: IntroSlide, index: number) => (
    <View key={slide.id} style={styles.slideContainer}>
      <LinearGradient
        colors={[colors.background, colors.primary + '05', colors.background]}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}
        style={styles.gradientBackground}
      />

      <View style={styles.contentContainer}>
        {index === 0 && (
          <FadeIn delay={300} duration={800}>
            <View style={styles.logoContainer}>
              <PyxiLabel />
            </View>
          </FadeIn>
        )}

        <ScaleIn delay={index === 0 ? 600 : 200} duration={600}>
          <View style={styles.iconContainer}>
            <Text style={styles.iconText}>{slide.icon}</Text>
          </View>
        </ScaleIn>

        <FadeIn delay={index === 0 ? 800 : 400} duration={600}>
          <Text style={styles.titleText}>{slide.title}</Text>
          <Text style={styles.subtitleText}>{slide.subtitle}</Text>
          <Text style={styles.descriptionText}>{slide.description}</Text>
        </FadeIn>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <ThemedStatusBar translucent={true} />

      {/* Header with Skip Button */}
      <View style={styles.headerContainer}>
        <View />
        <TouchableOpacity style={styles.headerSkipButton} onPress={handleSkip}>
          <Text style={styles.headerSkipText}>Skip</Text>
        </TouchableOpacity>
      </View>

      {/* Slides */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScroll}
        scrollEventThrottle={16}>
        {slides.map((slide, index) => renderSlide(slide, index))}
      </ScrollView>

      {/* Bottom Controls */}
      <View style={styles.bottomContainer}>
        {/* Pagination */}
        <View style={styles.paginationContainer}>
          {slides.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.paginationDot, index === currentSlide ? styles.activeDot : styles.inactiveDot]}
              onPress={() => goToSlide(index)}
            />
          ))}
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
            <Text style={styles.skipText}>{currentSlide === slides.length - 1 ? 'Skip' : 'Skip'}</Text>
          </TouchableOpacity>

          <ModernButton
            title={currentSlide === slides.length - 1 ? 'Get Started' : 'Next'}
            variant="primary"
            size="lg"
            onPress={handleNext}
            style={styles.nextButton}
            hapticFeedback
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ModernIntro;
