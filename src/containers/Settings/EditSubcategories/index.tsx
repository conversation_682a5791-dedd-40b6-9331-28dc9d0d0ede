import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useEffect, useLayoutEffect, useState, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, Text, TextInput, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {organizeByCategory} from '~Utils/filterSubcategories';
import {ModernHeader} from '~components/ModernHeader';
import SubcategoriesLists from '~components/SubcategoriesLists';
import ModernButton from '~components/ModernButton';
import ModernSpinner from '~components/ModernSpinner';

import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetCategories} from '~hooks/subcategories/useGetCategories';
import {useGetSubcategories} from '~hooks/subcategories/useGetSubcategories';
import {useGetUserSubcategories} from '~hooks/user/useGetUserSubcategories';
import {useUpdateUserSubcategories} from '~hooks/user/useUpdateUserSubcategories';
import {SubCategoryType} from '~types/categories';

import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';

const EditSubcategories = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const {data} = useGetSubcategories();
  const {data: d} = useGetCategories();
  const {mutateAsync: updateUserSubcategories} = useUpdateUserSubcategories();
  const {data: userSubcategories, isLoading: isUserSubcategoriesLoading} = useGetUserSubcategories(
    auth().currentUser?.uid || '',
  );
  const {goBack} = useNavigation();
  const {setIsTabBarDisabled} = useTabBar();

  const [categories, setCategories] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [selectedSubcategories, setSelectedSubcategories] = useState<number[]>([]);
  const [searchValue, setSearchValue] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useLayoutEffect(() => {
    setIsTabBarDisabled(true);
  }, [setIsTabBarDisabled]);

  useEffect(() => {
    if (userSubcategories) {
      setSelectedSubcategories(userSubcategories.map(item => item.subcategory_id));
    }
  }, [userSubcategories]);

  useEffect(() => {
    logScreenView('Edit Subcategories', 'EditSubcategories');
  }, []);

  const submit = async () => {
    setIsLoading(true);
    try {
      await updateUserSubcategories({ids: selectedSubcategories});
      goBack();
    } catch (E) {
      setIsLoading(false);
      console.log(E);
    }
  };

  const handleSearchCategory = useCallback(() => {
    if (searchValue?.length && data && d) {
      const inputData = data?.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchValue.trim().toLowerCase()) !== -1,
      );
      const result = organizeByCategory([], d || [], inputData || []);
      console.log('Filtered categories:', result);
      setCategories(result);
    } else if (data && d) {
      const result = organizeByCategory([], d || [], data || []);
      console.log('All categories:', result);
      setCategories(result);
    }
  }, [searchValue, data, d]);

  useEffect(() => {
    handleSearchCategory();
  }, [handleSearchCategory]);

  const handleInterestToggle = (subcategoryId: number) => {
    setSelectedSubcategories(prevState =>
      prevState.includes(subcategoryId)
        ? prevState.filter(item => item !== subcategoryId)
        : [...prevState, subcategoryId],
    );
  };

  const handleSubmit = submit;

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader
        title={t('settings.edit_interests') || 'Edit Interests'}
        subtitle={
          selectedSubcategories.length > 0
            ? `${selectedSubcategories.length} ${t('settings.interests_selected') || 'interests selected'}`
            : undefined
        }
        variant="default"
      />

      <Text
        style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: colors.textPrimary,
          marginHorizontal: 20,
          marginTop: 20,
          marginBottom: 20,
        }}>
        {t('settings.edit_interests') || 'Edit interests'}
      </Text>

      <View style={{flexDirection: 'row', paddingHorizontal: 20, marginBottom: 20, gap: 10}}>
        <TextInput
          placeholder={t('settings.search_interests') || 'Search interests...'}
          value={searchValue}
          onChangeText={setSearchValue}
          style={{
            flex: 1,
            height: 48,
            borderWidth: 1,
            borderColor: colors.border,
            borderRadius: 8,
            paddingHorizontal: 16,
            fontSize: 16,
            color: colors.textPrimary,
            backgroundColor: colors.surface,
          }}
          placeholderTextColor={colors.textSecondary}
        />
        <ModernButton
          title={t('generic.search') || 'Search'}
          onPress={handleSearchCategory}
          variant="primary"
          size="md"
          style={{minWidth: 80}}
        />
      </View>

      <View style={{flex: 1}}>
        {isUserSubcategoriesLoading ? (
          <View style={{alignItems: 'center', justifyContent: 'center', flex: 1}}>
            <ModernSpinner size={50} />
            <Text
              style={{
                fontSize: 16,
                color: colors.textSecondary,
                marginTop: 16,
              }}>
              {t('generic.loading') || 'Loading interests...'}
            </Text>
          </View>
        ) : categories && d ? (
          <SubcategoriesLists
            categoriesList={d}
            searchValue={searchValue}
            filteredSubcategoriesData={categories}
            selectedCategories={selectedSubcategories}
            onCategoryChange={handleInterestToggle}
            isFromSettings={true}
          />
        ) : (
          <View style={{alignItems: 'center', justifyContent: 'center', flex: 1}}>
            <Text
              style={{
                fontSize: 16,
                color: colors.textSecondary,
              }}>
              {t('settings.no_categories') || 'No categories available'}
            </Text>
          </View>
        )}
      </View>

      <View
        style={{
          paddingHorizontal: 20,
          paddingBottom: Platform.OS === 'ios' ? bottom + 20 : 20,
          paddingTop: 20,
          backgroundColor: colors.background,
        }}>
        <ModernButton
          title={isLoading ? t('generic.saving') || 'Saving...' : `${t('generic.submit') || 'Submit'}`}
          onPress={handleSubmit}
          loading={isLoading}
          disabled={isLoading}
          variant="primary"
          size="lg"
          fullWidth
          hapticFeedback
          hapticType="success"
        />
      </View>
    </View>
  );
};

export default EditSubcategories;
