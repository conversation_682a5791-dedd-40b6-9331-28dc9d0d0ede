import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, Text, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import OneSignal from 'react-native-onesignal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Button from '~components/Button';
import ModernButton from '~components/ModernButton';
import ModernCard from '~components/ModernCard/ModernCard';
import {useGetUserType} from '~hooks/event/useGetUserType';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import {useMapsContext} from '~providers/maps/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius, shadows} from '~constants/design';
import Animated, {FadeInDown, SlideInUp} from 'react-native-reanimated';

const LogOut = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const {goBack} = useNavigation();
  const {mutateAsync: updateUserMutation} = useUpdateUser();
  const {resetMapsState} = useMapsContext();
  const {data: userType} = useGetUserType(auth().currentUser!.uid);
  const {resetUser} = useUserStore();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Logout', 'LogOut');
  }, []);

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', paddingHorizontal: spacing.lg}}>
        {/* Logout Confirmation Card */}
        <Animated.View entering={FadeInDown.delay(100).duration(600)} style={{width: '100%', maxWidth: 400}}>
          <ModernCard variant="elevated" padding="xl" style={{alignItems: 'center'}}>
            <View
              style={{
                width: 100,
                height: 100,
                borderRadius: 50,
                backgroundColor: colors.warning + '20',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: spacing.xl,
              }}>
              <Text style={{fontSize: 48}}>👋</Text>
            </View>

            <Text
              style={{
                fontSize: typography.fontSize['3xl'],
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.md,
                textAlign: 'center',
              }}>
              {t('settings.sign_out_title') || 'Sign Out'}
            </Text>

            <Text
              style={{
                fontSize: typography.fontSize.lg,
                fontWeight: typography.fontWeight.normal,
                color: colors.textSecondary,
                lineHeight: typography.fontSize.lg * 1.5,
                textAlign: 'center',
                marginBottom: spacing.xl,
              }}>
              {t('settings.sign_out_body') || 'Are you sure you want to sign out of your account?'}
            </Text>

            {/* Action Buttons */}
            <View style={{width: '100%', gap: spacing.md}}>
              <Animated.View entering={SlideInUp.delay(300).duration(400)}>
                <ModernButton
                  title={t('settings.sign_out') || 'Sign Out'}
                  onPress={async () => {
                    setIsLoading(true);
                    try {
                      // Use OneSignal v4 API for disabling push notifications
                      OneSignal.disablePush(true);
                      console.log('✅ OneSignal push notifications disabled');
                    } catch (error) {
                      console.error('❌ Failed to disable OneSignal push notifications:', error);
                    }
                    if (userType === 'personal') {
                      await updateUserMutation({
                        coords_real: null,
                      });
                    }
                    resetMapsState();
                    resetUser();
                    await FirebaseAuth.logOut();
                  }}
                  loading={isLoading}
                  variant="primary"
                  size="lg"
                  fullWidth
                  hapticFeedback
                  hapticType="warning"
                  style={{
                    backgroundColor: colors.error,
                  }}
                />
              </Animated.View>

              <Animated.View entering={SlideInUp.delay(400).duration(400)}>
                <ModernButton
                  title={t('generic.cancel') || 'Cancel'}
                  onPress={goBack}
                  variant="outline"
                  size="lg"
                  fullWidth
                  hapticFeedback
                  style={{
                    borderColor: colors.border,
                  }}
                  textStyle={{
                    color: colors.textSecondary,
                  }}
                />
              </Animated.View>
            </View>
          </ModernCard>
        </Animated.View>
      </View>
    </View>
  );
};

export default LogOut;
