import auth, { EmailAuthProvider } from '@react-native-firebase/auth';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAvoidingView, Platform, Text, View } from 'react-native';
import Button from '~components/Button';
import { CustomTextInput } from '~components/CustomTextInput';
import { ModernHeader } from '~components/ModernHeader';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernButton from '~components/ModernButton';
import ModernTextInput from '~components/ModernTextInput';
import { SCREENS } from '~constants';
import { NavigationProps } from '~types/navigation/navigation.type';
import getStyles from './styles';
import { useTheme } from '~contexts/ThemeContext';
import { spacing, typography, borderRadius, shadows } from '~constants/design';
import Animated, { FadeInDown, SlideInUp } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const PasswordForDeleteAccount = () => {
  const { colors } = useTheme();
  const styles = getStyles(colors);
  const { navigate } = useNavigation<NavigationProps>();
  const { t } = useTranslation();

  const [pas, setPas] = useState('');
  const [pasError, setPasError] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  const onSubmit = () => {
    setIsLoading(true);
    const authInstance = auth();
    const user = authInstance.currentUser;
    const credential = EmailAuthProvider.credential(user?.email!, pas);
    user
      ?.reauthenticateWithCredential(credential)
      .then(() => {
        setPasError('');
        navigate(SCREENS.DELETE_LOADING);
      })
      .catch(() => {
        setIsLoading(false);
        setPasError('Invalid password');
      });
  };

  const { bottom } = useSafeAreaInsets();

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <ModernHeader
        title={t('settings.delete_account') || 'Delete Account'}
        subtitle={t('settings.delete_account_subtitle') || 'Confirm your password to proceed'}
        variant="default"
      />

      <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        {/* Warning Section */}
        <Animated.View entering={FadeInDown.delay(100).duration(400)}>
          <ModernCard variant="elevated" padding="xl" margin="md" style={{ alignItems: 'center' }}>
            <View
              style={{
                width: 80,
                height: 80,
                borderRadius: 40,
                backgroundColor: colors.error + '20',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: spacing.md,
              }}>
              <Text style={{ fontSize: 32 }}>⚠️</Text>
            </View>
            <Text
              style={{
                fontSize: typography.fontSize['2xl'],
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.sm,
                textAlign: 'center',
              }}>
              {t('settings.delete_account') || 'Delete Account'}
            </Text>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                fontWeight: typography.fontWeight.normal,
                color: colors.textSecondary,
                lineHeight: typography.fontSize.base * 1.5,
                textAlign: 'center',
              }}>
              {t('settings.delete_account_warning') ||
                'Please enter your password to delete the account. This action cannot be undone.'}
            </Text>
          </ModernCard>
        </Animated.View>

        {/* Password Input */}
        <Animated.View entering={FadeInDown.delay(200).duration(400)}>
          <ModernCard variant="default" padding="xl" margin="md">
            <Text
              style={{
                fontSize: typography.fontSize.lg,
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.md,
              }}>
              {t('settings.confirm_password') || 'Confirm Password'}
            </Text>

            <ModernTextInput
              label={t('signin.password') || 'Password'}
              placeholder={t('settings.enter_password') || 'Enter your password'}
              value={pas}
              onChangeText={setPas}
              secureTextEntry
              variant="outlined"
              size="lg"
              errorText={pasError}
            />
          </ModernCard>
        </Animated.View>

        <View style={{ flex: 1 }} />

        {/* Delete Button */}
        <Animated.View
          entering={SlideInUp.delay(300).duration(400)}
          style={{
            paddingHorizontal: spacing.md,
            paddingBottom: Platform.OS === 'ios' ? bottom + spacing.md : spacing.md,
          }}>
          <ModernButton
            title={t('settings.delete_account') || 'Delete Account'}
            onPress={onSubmit}
            loading={isLoading}
            variant="primary"
            size="lg"
            fullWidth
            hapticFeedback
            hapticType="warning"
            style={{
              backgroundColor: colors.error,
            }}
          />
        </Animated.View>
      </KeyboardAvoidingView>
    </View>
  );
};

export default PasswordForDeleteAccount;
