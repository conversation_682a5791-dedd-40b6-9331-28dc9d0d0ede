import React, {useState} from 'react';
import {
  View,
  ScrollView,
  TextInput,
  Alert,
  StyleSheet,
  Text,
  ViewStyle,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import axios from 'axios';
import {BASE_API_URL} from '@env';
import FirebaseAuth from '~services/FirebaseAuthService';
import {useNavigation} from '@react-navigation/native';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useQuery} from 'react-query';
import {fonts} from '~constants/fonts';
import {ModernHeader} from '~components/ModernHeader';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernButton from '~components/ModernButton';
import ModernTextInput from '~components/ModernTextInput';
import Button from '~components/Button';
import ModernSpinner from '~components/ModernSpinner';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius, shadows} from '~constants/design';
import Animated, {FadeInDown, FadeInLeft} from 'react-native-reanimated';

interface Group {
  id: string;
  community_name: string;
  community_desc: string;
}

const Groups = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();
  const [invitationCode, setInvitationCode] = useState('');
  const [isJoining, setIsJoining] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  const {
    data: groups,
    isLoading,
    refetch,
  } = useQuery<Group[]>('joined-groups', async () => {
    const token = await FirebaseAuth.getAuthToken();
    const config = {
      headers: {Authorization: token, Accept: 'application/json'},
    };
    const response = await axios.get(BASE_API_URL + 'community/joined', config);
    return response.data;
  });

  const joinGroup = async () => {
    try {
      setIsJoining(true);
      const token = await FirebaseAuth.getAuthToken();
      const config = {
        headers: {Authorization: token, Accept: 'application/json'},
      };
      await axios.post(BASE_API_URL + 'community/join', {invitation_code: invitationCode}, config);
      Notifier.showNotification({
        title: t('settings.join_success'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
      setInvitationCode('');
      refetch();
    } catch (error) {
      Notifier.showNotification({
        title: t('settings.join_error'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'error',
        },
      });
    } finally {
      setIsJoining(false);
    }
  };

  const leaveGroup = async (groupId: string) => {
    try {
      setIsLeaving(true);
      const token = await FirebaseAuth.getAuthToken();
      const config = {
        headers: {Authorization: token, Accept: 'application/json'},
      };
      await axios.delete(BASE_API_URL + `community/leave/${groupId}`, config);
      Notifier.showNotification({
        title: t('settings.leave_success'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'info',
        },
      });
      refetch();
    } catch (error) {
      Notifier.showNotification({
        title: t('settings.leave_error'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'error',
        },
      });
    } finally {
      setIsLeaving(false);
    }
  };

  const styles = getStyles(colors);

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader title={t('settings.groups_management') || 'Groups Management'} variant="default" />

      <ScrollView
        style={{flex: 1}}
        contentContainerStyle={{
          paddingTop: spacing.md,
          paddingBottom: spacing['6xl'],
        }}
        showsVerticalScrollIndicator={false}>
        {/* Join New Group Section */}
        <Animated.View entering={FadeInDown.delay(100).duration(400)}>
          <ModernCard variant="elevated" padding="xl" margin="md">
            <View
              style={{
                width: 80,
                height: 80,
                borderRadius: 40,
                backgroundColor: colors.primary + '20',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: spacing.md,
                alignSelf: 'center',
              }}>
              <Text style={{fontSize: 32}}>👥</Text>
            </View>
            <Text
              style={{
                fontSize: typography.fontSize['2xl'],
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.sm,
                textAlign: 'center',
              }}>
              {t('settings.join_new_group') || 'Join a New Group'}
            </Text>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                color: colors.textSecondary,
                textAlign: 'center',
                lineHeight: typography.fontSize.base * 1.5,
                marginBottom: spacing.lg,
              }}>
              {t('settings.join_group_description') || 'Enter an invitation code to join a community group.'}
            </Text>

            <ModernTextInput
              label={t('settings.invitation_code') || 'Invitation Code'}
              placeholder={t('settings.enter_invitation_code') || 'Enter invitation code'}
              value={invitationCode}
              onChangeText={setInvitationCode}
              variant="outlined"
              size="lg"
              style={{marginBottom: spacing.md}}
            />

            <ModernButton
              title={t('settings.join_group') || 'Join Group'}
              onPress={joinGroup}
              loading={isJoining}
              disabled={!invitationCode || isJoining}
              variant="primary"
              size="lg"
              fullWidth
              hapticFeedback
              hapticType="success"
            />
          </ModernCard>
        </Animated.View>

        {/* Your Groups Section */}
        <Animated.View entering={FadeInDown.delay(200).duration(400)}>
          <ModernCard variant="default" padding="xl" margin="md">
            <Text
              style={{
                fontSize: typography.fontSize.xl,
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.md,
              }}>
              {t('settings.joined_groups') || 'Your Groups'}
            </Text>

            {isLoading ? (
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  paddingVertical: spacing.xl,
                }}>
                <ModernSpinner size={40} />
                <Text
                  style={{
                    fontSize: typography.fontSize.base,
                    color: colors.textSecondary,
                    marginTop: spacing.md,
                  }}>
                  {t('settings.loading_groups') || 'Loading your groups...'}
                </Text>
              </View>
            ) : groups && groups.length > 0 ? (
              <View>
                {groups.map((group, index) => (
                  <Animated.View key={group.id} entering={FadeInLeft.delay(300 + index * 100).duration(400)}>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        paddingVertical: spacing.md,
                        borderBottomWidth: index < groups.length - 1 ? 1 : 0,
                        borderBottomColor: colors.border + '40',
                      }}>
                      <View style={{flex: 1}}>
                        <Text
                          style={{
                            fontSize: typography.fontSize.lg,
                            fontWeight: typography.fontWeight.semibold,
                            color: colors.textPrimary,
                            marginBottom: spacing.xs,
                          }}>
                          {group.community_name}
                        </Text>
                        <Text
                          style={{
                            fontSize: typography.fontSize.sm,
                            color: colors.textSecondary,
                          }}>
                          {group.community_desc || t('settings.community_group') || 'Community Group'}
                        </Text>
                      </View>
                      <ModernButton
                        title={t('settings.leave_group') || 'Leave'}
                        onPress={() => leaveGroup(group.id)}
                        loading={isLeaving}
                        disabled={isLeaving}
                        variant="outline"
                        size="sm"
                        hapticFeedback
                        hapticType="warning"
                        style={{
                          borderColor: colors.error,
                          minWidth: 80,
                        }}
                        textStyle={{
                          color: colors.error,
                        }}
                      />
                    </View>
                  </Animated.View>
                ))}
              </View>
            ) : (
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  paddingVertical: spacing.xl,
                }}>
                <View
                  style={{
                    width: 100,
                    height: 100,
                    borderRadius: 50,
                    backgroundColor: colors.primary + '20',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: spacing.lg,
                  }}>
                  <Text style={{fontSize: 40}}>🏠</Text>
                </View>
                <Text
                  style={{
                    fontSize: typography.fontSize.lg,
                    fontWeight: typography.fontWeight.bold,
                    color: colors.textPrimary,
                    marginBottom: spacing.sm,
                    textAlign: 'center',
                  }}>
                  {t('settings.no_groups') || 'No Groups Yet'}
                </Text>
                <Text
                  style={{
                    fontSize: typography.fontSize.base,
                    color: colors.textSecondary,
                    textAlign: 'center',
                    lineHeight: typography.fontSize.base * 1.5,
                  }}>
                  {t('settings.no_groups_description') ||
                    "You haven't joined any groups yet. Use an invitation code to join your first group."}
                </Text>
              </View>
            )}
          </ModernCard>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const getStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.white,
    },
    content: {
      flex: 1,
      marginTop: 70,
    },
    contentContainer: {
      padding: 16,
    },
    card: {
      backgroundColor: colors.white,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    sectionTitle: {
      fontSize: 18,
      fontFamily: fonts.medium,
      color: colors.black,
      marginBottom: 16,
    },
    sectionInfo: {
      fontSize: 12,
      fontFamily: fonts.medium,
      color: colors.black,
      marginBottom: 16,
    },
    input: {
      height: 48,
      borderWidth: 1,
      borderColor: colors.lightGray,
      borderRadius: 8,
      paddingHorizontal: 16,
      marginBottom: 16,
      fontFamily: fonts.regular,
      color: colors.black,
      backgroundColor: colors.gray100,
    },
    joinButton: {
      marginTop: 8,
      backgroundColor: colors.primary,
      borderRadius: 8,
    } as ViewStyle,
    groupItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
    },
    groupName: {
      fontFamily: fonts.medium,
      fontSize: 16,
      color: colors.black,
      flex: 1,
    },
    leaveButton: {
      backgroundColor: colors.red,
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 6,
      justifyContent: 'center',
      alignItems: 'center',
    },
    leaveButtonText: {
      color: colors.white,
      fontFamily: fonts.medium,
      fontSize: 14,
    },
    disabledButton: {
      opacity: 0.7,
    },
    emptyContainer: {
      paddingVertical: 32,
      alignItems: 'center',
      justifyContent: 'center',
    },
    noGroups: {
      fontFamily: fonts.regular,
      fontSize: 16,
      color: colors.gray,
      textAlign: 'center',
    },
    spinnerContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: 100,
    },
  });

export default Groups;
