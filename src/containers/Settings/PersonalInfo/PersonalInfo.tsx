import React, {useCallback, useEffect, useState, useRef} from 'react';
import {View, Text, Platform, ScrollView, Alert, TouchableOpacity} from 'react-native';
import {Formik} from 'formik';
import * as yup from 'yup';
import moment from 'moment-timezone';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import auth from '@react-native-firebase/auth';
import {CalendarIcon, DropdownArrowIcon, LocationIcon, SettingsPersonIcon} from '~assets/icons';
import {reverseGeocode} from '~Utils/location';
import {NavigationProps} from '~types/navigation/navigation.type';
import {logScreenView} from '~Utils/firebaseAnalytics';
import DateTimeModal from '~components/DateTimeModal';
import EditProfileModal from '~components/EditProfileModal';
import {ModernHeader} from '~components/ModernHeader';

import {ModalWithGenders} from '~components/ModalWithItems';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernButton from '~components/ModernButton';
import ModernTextInput from '~components/ModernTextInput';
import ModernLocationInput from '~components/ModernLocationInput/ModernLocationInput';
import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
import {useMapsContext} from '~providers/maps/zustand';

import {spacing, typography, borderRadius, shadows} from '~constants/design';
import Animated, {FadeInDown, SlideInUp} from 'react-native-reanimated';
import ChildrenQuantity from '~containers/Onboarding/Children/Children';
import SelectTypeOfProfile from '~containers/Onboarding/Group/Group';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import {useGetUserGroups} from '~hooks/user/useGetUserGroups';
import {useUpdateUser} from '~hooks/user/useUpdateUser';
import {useTheme} from '~contexts/ThemeContext';
import {haptics} from '~Utils/haptics';

// Modern Interactive Field Component
interface ModernFieldProps {
  label: string;
  value: string;
  onPress: () => void;
  icon?: React.ReactNode;
  errorText?: string;
  placeholder?: string;
}

const ModernField: React.FC<ModernFieldProps> = ({
  label,
  value,
  onPress,
  icon,
  errorText,
  placeholder = 'Tap to select',
}) => {
  const {colors} = useTheme();

  const handlePress = () => {
    haptics.light();
    onPress();
  };

  return (
    <View style={{marginBottom: spacing.md}}>
      <Text
        style={{
          fontSize: typography.fontSize.sm,
          fontWeight: typography.fontWeight.medium,
          color: colors.textSecondary,
          marginBottom: spacing.xs,
        }}>
        {label}
      </Text>
      <TouchableOpacity
        onPress={handlePress}
        style={{
          backgroundColor: colors.surface,
          borderWidth: 1,
          borderColor: errorText ? colors.error : colors.border + '40',
          borderRadius: borderRadius.lg,
          paddingHorizontal: spacing.lg,
          paddingVertical: spacing.lg,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          minHeight: 56,
        }}>
        <Text
          style={{
            fontSize: typography.fontSize.base,
            color: value ? colors.textPrimary : colors.textSecondary,
            flex: 1,
          }}
          numberOfLines={1}>
          {value || placeholder}
        </Text>
        {icon && <View style={{marginLeft: spacing.sm}}>{icon}</View>}
      </TouchableOpacity>
      {errorText && (
        <Text
          style={{
            fontSize: typography.fontSize.xs,
            color: colors.error,
            marginTop: spacing.xs,
          }}>
          {errorText}
        </Text>
      )}
    </View>
  );
};

const PersonalInfo = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();
  const {bottom} = useSafeAreaInsets();
  const isFocused = useIsFocused();
  // Data hooks
  const {data} = useGetUserAccount(auth().currentUser?.uid || '');
  const {data: userGroups} = useGetUserGroups();
  const {mutateAsync: handleUpdateUser} = useUpdateUser();

  // Maps context and location hooks
  const {currentPositionState, setCurrentPositionState} = useMapsContext();
  const {getCurrentPosition} = useGetCurrentPosition();

  // State management
  const [isLoading, setIsLoading] = useState(false);
  const [isDateModalOpen, setIsDateModalOpen] = useState(false);
  const [isGenderModalOpen, setIsGenderModalOpen] = useState(false);

  const [isProfileTypeModalOpen, setIsProfileTypeModalOpen] = useState(false);
  const [isKidsModalOpen, setIsKidsModalOpen] = useState(false);
  const [birthdayDate, setBirthdayDate] = useState<moment.Moment | null>(null);
  const [addressName, setAddressName] = useState('');
  const [coords, setCoords] = useState({
    latitude: data?.coords?.lat || 0,
    longitude: data?.coords?.long || 0,
  });
  const [locationError, setLocationError] = useState('');

  // Field refs for smart scrolling
  const fieldRefs = useRef<{[key: string]: View | null}>({});

  // Ref to track if we've already tried to get current position
  const hasTriedAutoLocation = useRef(false);

  // Validation schema
  const validationSchema = yup.object().shape({
    first_name: yup
      .string()
      .trim()
      .min(2, t('settings.nameMinError') || 'Name must be at least 2 characters')
      .required(t('settings.nameError') || 'First name is required'),
    lastName: yup.string().trim().optional(),
    description: yup
      .string()
      .trim()
      .min(10, t('settings.descriptionMinError') || 'Description must be at least 10 characters')
      .max(500, t('settings.descriptionMaxError') || 'Description must be less than 500 characters')
      .required(t('settings.descriptionError') || 'Description is required'),
    birthday: yup.string().optional(),
    gender: yup.string().required(t('settings.genderError') || 'Gender is required'),
    location: yup.string().optional(),
    groups: yup.string().optional(),
  });

  // Callbacks
  const handleChooseBirthdayDate = useCallback((value?: Date) => {
    if (value) {
      setBirthdayDate(moment(value));
    }
  }, []);

  const handleSaveSuccess = useCallback(() => {
    haptics.success();
    Alert.alert(
      t('settings.success') || 'Success',
      t('settings.profileUpdated') || 'Your profile has been updated successfully!',
      [
        {
          text: t('generic.ok') || 'OK',
          onPress: () => navigation.goBack(),
        },
      ],
    );
  }, [navigation, t]);

  const handleSaveError = useCallback(() => {
    haptics.error();
    Alert.alert(
      t('generic.error') || 'Error',
      t('settings.updateError') || 'Failed to update profile. Please try again.',
      [
        {
          text: t('generic.ok') || 'OK',
        },
      ],
    );
  }, [t]);

  // Effects
  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  // Initialize location data when component mounts or data changes
  useEffect(() => {
    const initializeLocation = async () => {
      if (data?.coords) {
        // User has saved location - use it
        setCoords({latitude: data.coords.lat, longitude: data.coords.long});
        try {
          const address = await reverseGeocode({
            coords: {latitude: data.coords.lat, longitude: data.coords.long},
          });
          setAddressName(address || '');
        } catch (error) {
          console.log('Error getting address:', error);
        }
      } else if (!currentPositionState && !hasTriedAutoLocation.current) {
        // No saved location and no current position - try to get current location (only once)
        console.log('📍 Auto-populating current location for personal info...');
        hasTriedAutoLocation.current = true;
        getCurrentPosition(true);
      }
    };
    initializeLocation();
  }, [data?.coords]); // Only depend on data.coords to avoid circular updates

  // Update local state when current position changes (only if no saved location)
  useEffect(() => {
    if (currentPositionState && !data?.coords) {
      setCoords({
        latitude: currentPositionState.latitude,
        longitude: currentPositionState.longitude,
      });
      setAddressName(currentPositionState.address);
    }
  }, [currentPositionState?.latitude, currentPositionState?.longitude, currentPositionState?.address, data?.coords]);

  useEffect(() => {
    logScreenView('Personal Info', 'PersonalInfo');
  }, []);

  // Reset auto-location flag when data changes (user might have updated their location)
  useEffect(() => {
    if (data?.coords) {
      hasTriedAutoLocation.current = false;
    }
  }, [data?.coords]);

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader title={t('settings.personalInfo') || 'Personal Information'} variant="default" />
      <Formik
        initialValues={{
          first_name: data?.first_name || '',
          lastName: data?.last_name || '',
          description: data?.description || '',
          birthday: data?.date_of_birth ? moment(data.date_of_birth).toISOString() : '',
          gender: data?.gender || '',
          location: addressName || currentPositionState?.address || '',
          groups: userGroups?.map(group => group.group_id).toString() || '',
        }}
        validationSchema={validationSchema}
        enableReinitialize
        onSubmit={async values => {
          setIsLoading(true);
          try {
            let timezoneNaiveDate = '';
            if (values.birthday) {
              const inputDate = new Date(values.birthday);
              timezoneNaiveDate = inputDate.toISOString().substring(0, 19);
            }

            // Use current position state coordinates if available, otherwise fall back to local coords
            const finalCoords = currentPositionState
              ? {lat: currentPositionState.latitude, long: currentPositionState.longitude}
              : coords.latitude && coords.longitude
                ? {lat: coords.latitude, long: coords.longitude}
                : undefined;

            const updatedUser = {
              uid: auth().currentUser!.uid,
              first_name: values.first_name.trim(),
              last_name: values.lastName.trim(),
              description: values.description.trim(),
              coords: finalCoords,
              gender: values.gender,
              date_of_birth: timezoneNaiveDate || undefined,
            };

            await handleUpdateUser(updatedUser);
            handleSaveSuccess();
          } catch (error) {
            console.error('Error updating user:', error);
            handleSaveError();
          } finally {
            setIsLoading(false);
          }
        }}>
        {({values, handleChange, handleSubmit, errors, touched, isValid}) => (
          <>
            <ScrollView
              style={{flex: 1}}
              contentContainerStyle={{
                paddingBottom: spacing['6xl'] + bottom,
              }}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled">
              {/* Basic Information Section */}
              <Animated.View entering={FadeInDown.delay(200).duration(400)}>
                <ModernCard variant="default" padding="xl" margin="md">
                  <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: spacing.lg}}>
                    <SettingsPersonIcon />
                    <Text
                      style={{
                        fontSize: typography.fontSize.xl,
                        fontWeight: typography.fontWeight.bold,
                        color: colors.textPrimary,
                        marginLeft: spacing.sm,
                      }}>
                      {t('settings.basic_info') || 'Basic Information'}
                    </Text>
                  </View>

                  <ModernTextInput
                    label={t('settings.first_name') || 'First Name'}
                    placeholder={t('settings.name_placeholder') || 'Enter your first name'}
                    value={values.first_name}
                    onChangeText={handleChange('first_name')}
                    errorText={touched.first_name && errors.first_name ? errors.first_name : undefined}
                    variant="outlined"
                    size="lg"
                    style={{marginBottom: spacing.lg}}
                  />

                  <ModernTextInput
                    label={t('settings.last_name') || 'Last Name'}
                    placeholder={t('settings.last_name_placeholder') || 'Enter your last name'}
                    value={values.lastName}
                    onChangeText={handleChange('lastName')}
                    errorText={touched.lastName && errors.lastName ? errors.lastName : undefined}
                    variant="outlined"
                    size="lg"
                    style={{marginBottom: spacing.lg}}
                  />

                  <ModernTextInput
                    label={t('settings.about_me') || 'About Me'}
                    placeholder={t('settings.description_placeholder') || 'Tell us about yourself...'}
                    value={values.description}
                    onChangeText={handleChange('description')}
                    errorText={touched.description && errors.description ? errors.description : undefined}
                    variant="outlined"
                    size="lg"
                    multiline
                    numberOfLines={4}
                    style={{marginBottom: spacing.sm}}
                  />
                </ModernCard>
              </Animated.View>

              {/* Personal Details Section */}
              <Animated.View entering={FadeInDown.delay(300).duration(400)}>
                <ModernCard variant="default" padding="xl" margin="md">
                  <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: spacing.lg}}>
                    <CalendarIcon />
                    <Text
                      style={{
                        fontSize: typography.fontSize.xl,
                        fontWeight: typography.fontWeight.bold,
                        color: colors.textPrimary,
                        marginLeft: spacing.sm,
                      }}>
                      {t('settings.personal_details') || 'Personal Details'}
                    </Text>
                  </View>

                  <ModernField
                    label={t('settings.birthday') || 'Birthday'}
                    value={values.birthday ? moment(values.birthday).format('DD/MM/YYYY') : ''}
                    onPress={() => setIsDateModalOpen(true)}
                    icon={<CalendarIcon color={colors.textSecondary} />}
                    placeholder={t('settings.select_birthday') || 'Select your birthday'}
                  />

                  <ModernField
                    label={t('settings.gender1') || 'Gender'}
                    value={values.gender ? t(`onboarding.${values.gender.split(' ')[0]}`) : ''}
                    onPress={() => setIsGenderModalOpen(true)}
                    icon={<DropdownArrowIcon />}
                    errorText={touched.gender && errors.gender ? errors.gender : undefined}
                    placeholder={t('settings.select_gender') || 'Select your gender'}
                  />

                  <View
                    ref={ref => {
                      fieldRefs.current['location'] = ref;
                    }}
                    style={{marginBottom: spacing.sm}}>
                    <ModernLocationInput
                      label={t('settings.location') || 'Location'}
                      value={values.location || addressName || currentPositionState?.address || ''}
                      onLocationSelect={location => {
                        setCurrentPositionState({
                          latitude: location.latitude,
                          longitude: location.longitude,
                          address: location.address,
                        });
                        setCoords({
                          latitude: location.latitude,
                          longitude: location.longitude,
                        });
                        setAddressName(location.address);
                        handleChange('location')(location.address);
                        setLocationError('');
                        haptics.light();
                      }}
                      errorText={locationError}
                      placeholder={t('settings.location_search_placeholder') || 'Search for your location...'}
                    />
                  </View>
                </ModernCard>
              </Animated.View>
            </ScrollView>

            {/* Floating Save Button */}
            <Animated.View
              entering={SlideInUp.delay(400).duration(400)}
              style={{
                position: 'absolute',
                bottom: Platform.OS === 'ios' ? bottom + spacing.md : spacing.md,
                left: spacing.md,
                right: spacing.md,
                backgroundColor: colors.background,
                borderRadius: borderRadius.xl,
                ...shadows.lg,
              }}>
              <ModernButton
                title={isLoading ? t('generic.saving') || 'Saving...' : t('settings.save_changes') || 'Save Changes'}
                onPress={() => handleSubmit()}
                loading={isLoading}
                disabled={!isValid || isLoading}
                variant="primary"
                size="lg"
                fullWidth
                hapticFeedback
                hapticType="success"
              />
            </Animated.View>

            {/* Modals */}
            <DateTimeModal
              isVisible={isDateModalOpen}
              close={() => setIsDateModalOpen(false)}
              onPress={() => {
                if (birthdayDate) {
                  handleChange('birthday')(birthdayDate.format('YYYY-MM-DDTHH:mm:ss'));
                }
                setIsDateModalOpen(false);
              }}
              date={birthdayDate?.toDate() || moment().subtract(18, 'years').toDate()}
              handleChooseMaxDate={handleChooseBirthdayDate}
            />

            <ModalWithGenders
              chosenGender={values.gender}
              isVisible={isGenderModalOpen}
              close={() => setIsGenderModalOpen(false)}
              onPress={value => () => {
                handleChange('gender')(value);
                setIsGenderModalOpen(false);
                haptics.light();
              }}
            />

            <EditProfileModal isOpen={isProfileTypeModalOpen} onClose={() => setIsProfileTypeModalOpen(false)}>
              <SelectTypeOfProfile
                selectedGroups={values.groups.split(',').map(value => +value)}
                isModal
                isFromSettings
                onSubmit={arr => {
                  handleChange('groups')(arr.toString());
                  setIsProfileTypeModalOpen(false);
                }}
              />
            </EditProfileModal>

            <EditProfileModal isOpen={isKidsModalOpen} onClose={() => setIsKidsModalOpen(false)}>
              <ChildrenQuantity isFromSettings onSubmit={() => setIsKidsModalOpen(false)} />
            </EditProfileModal>
          </>
        )}
      </Formik>
    </View>
  );
};

export default PersonalInfo;
