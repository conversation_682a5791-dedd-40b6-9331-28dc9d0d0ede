import React, {useState} from 'react';
import {SafeAreaView, View, Text, TouchableOpacity, Image, Alert} from 'react-native';
import {lightTheme, darkTheme} from '~constants/colors'; // Adjust path as per your project
import {createStyles} from './styles';
import {PyxiLabel} from '~assets/icons';
import {useNavigation, useRoute} from '@react-navigation/native';
import backIcon from '~assets/images/backIcon.png';
import {Event, PaymentIntentResponse} from '~types/api/event';
import useMatchingLoadingModalAnimation from '~hooks/react-hooks/useMatchingLoadingModalAnimation';
import {useCreatePayPerMatchPaymentIntent} from '~hooks/event/useCreatePayPerMatchPaymentIntent';
import {useStripe} from '@stripe/stripe-react-native';
import {useAddUserEventStatus} from '~hooks/event/useAddUserEventStatus';

// Define the props type for the component
interface OptionsScreenProps {
  theme: typeof lightTheme | typeof darkTheme;
}

const OptionsScreen: React.FC<OptionsScreenProps> = ({theme = lightTheme}) => {
  const styles = createStyles(theme);
  const navigation = useNavigation();
  const route = useRoute<any>();
  const event: Event = route.params?.event;
  const userStatus = route.params?.userStatus;
  const {openMatchingLoadingModal, setCurrentMatchingEvent, setRefresh} = useMatchingLoadingModalAnimation();

  console.log(userStatus, 'userStatususerStatus');

  // Payment related hooks and state
  const [isLoading, setIsLoading] = useState(false);
  const {mutateAsync: createPayPerMatchPaymentIntent} = useCreatePayPerMatchPaymentIntent();
  const {initPaymentSheet, presentPaymentSheet} = useStripe();
  const {mutateAsync: addEventUserStatus} = useAddUserEventStatus(true);

  const onFreePress = () => {
    setCurrentMatchingEvent(event?.event_id || null);
    setRefresh(false);
    openMatchingLoadingModal();
  };

  // Initialize Stripe payment sheet
  const initializePaymentSheet = async (data: PaymentIntentResponse) => {
    const {error} = await initPaymentSheet({
      merchantDisplayName: 'Pyxi',
      paymentIntentClientSecret: data.client_secret,
      returnURL: 'http://partner.pyxi.ai/payment-return',
    });

    if (error) {
      console.error('Error initializing Payment Sheet:', error);
      return false;
    }

    console.log('Payment Sheet initialized successfully');
    return true;
  };

  // Present payment sheet and handle result
  const openPaymentSheet = async (data: PaymentIntentResponse) => {
    const {error} = await presentPaymentSheet();

    if (error) {
      setIsLoading(false);
      Alert.alert('Payment failed', error.message);
      console.error('Error presenting Payment Sheet:', error.message);
    } else {
      setIsLoading(false);
      // Show success alert with payment information
      Alert.alert(
        'Payment Successful!',
        `Your payment has been processed successfully.\n\nOrder ID: ${data.order_id}\n\nYou will be matched with other participants for this event.`,
        [
          {
            text: 'OK',
            onPress: () => {
              navigation.goBack();
            },
          },
        ],
      );
      console.log('Payment successful!');
    }
  };

  const onPayCLick = async () => {
    if (!event?.event_id) {
      Alert.alert('Error', 'Event information is missing');
      return;
    }

    try {
      setIsLoading(true);

      try {
        await addEventUserStatus({
          event_id: event.event_id.toString(),
          count: '1',
        });
      } catch (error) {}

      // Create payment intent for pay-per-match
      const data = await createPayPerMatchPaymentIntent({
        eventId: event.event_id,
      });

      // Initialize payment sheet
      const isInitialized = await initializePaymentSheet(data);

      if (isInitialized) {
        // Present payment sheet
        await openPaymentSheet(data);
      } else {
        setIsLoading(false);
        Alert.alert('Error', 'Failed to initialize payment');
      }
    } catch (error: any) {
      console.log(error, 'Failed to initialize pay-per-match payment!');
      console.log(error.message, 'Failed to initialize pay-per-match payment!');

      setIsLoading(false);
      Alert.alert('Payment Error', error.message || 'Failed to initialize payment. Please try again.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header Row: Back Button + Logo */}
      <View style={styles.headerRow}>
        <TouchableOpacity activeOpacity={0.7} onPress={() => navigation.goBack?.()}>
          <Image source={backIcon} style={styles.backButton} />
        </TouchableOpacity>
        <View style={styles.logoContainer}>
          <PyxiLabel />
        </View>
        <View style={{width: 44}} />
      </View>

      <View style={{marginHorizontal: 16}}>
        <Text style={styles.header}>Choose an option</Text>
        <Text style={styles.subheader}>Find the best way to pair up and{'\n'}attend events together.</Text>

        <View style={[styles.optionCard, styles.paidOptionCard]}>
          <Text style={styles.optionTitle}>Give this to Team Pyxi</Text>
          <Text style={styles.optionDescription}>We'll do the matching for you.</Text>
          <Text style={styles.optionDescriptionA}>
            We will match you on our algorithm with 4 other people. You just show up!
          </Text>
          {userStatus?.match_payment ? (
            <TouchableOpacity
              style={[styles.priceContainer, isLoading && {opacity: 0.6}]}
              disabled={true}>
              <Text style={styles.priceText}>
                {'You’re all set! We’ll arrange matches for you'}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              onPress={onPayCLick}
              style={[styles.priceContainer, isLoading && {opacity: 0.6}]}
              disabled={isLoading}>
              <Text style={styles.priceText}>
                {isLoading ? 'Processing...' : `${event.pay_per_match_currency} ${event.pay_per_match_fee}`}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.optionCard}>
          <Text style={[styles.optionTitle, {color: '#1D1E20'}]}>Show 5 matches</Text>
          <Text style={[styles.optionDescription, {color: '#1D1E20'}]}>You take care of this manually.</Text>
          <Text style={[styles.optionDescriptionA, {color: '#1D1E20', opacity: 0.6}]}>
            We will show you 5 users who are interested in the event. You can request to speak to them and make plans to
            go to the event together
          </Text>
          <TouchableOpacity onPress={onFreePress} style={[styles.priceContainer, styles.freePriceContainer]}>
            <Text style={[styles.priceText, styles.freePriceText]}>Free</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default OptionsScreen;
