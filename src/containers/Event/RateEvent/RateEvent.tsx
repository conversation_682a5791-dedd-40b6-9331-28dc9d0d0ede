import {useNavigation, useRoute} from '@react-navigation/native';
import React, {useRef, useState} from 'react';
import {View, StyleSheet, ImageBackground, Text, TouchableOpacity, Alert} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import {GoBackHeader} from '~components/GoBackHeader';
import ModernButton from '~components/ModernButton';
import ModernCard from '~components/ModernCard/ModernCard';
import {NavigationProps} from '~types/navigation/navigation.type';
import {spacing, borderRadius, typography, shadows} from '~constants/design';
import StarRating from 'react-native-star-rating-widget';
import RatingSheet from '~components/RatingSheet';
import BottomSheet from '@gorhom/bottom-sheet';
import {ThemedStatusBar} from '~components/ThemedStatusBar/ThemedStatusBar';
import {useTheme} from '~contexts/ThemeContext';
import Animated, {FadeInDown, FadeInUp} from 'react-native-reanimated';
import {useGetEventById} from '~hooks/event/useGetEventById';
import {useAddReviewByEventId} from '~hooks/event/useAddReviewByEventId';

const RateEvent: React.FC = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const navigation = useNavigation<NavigationProps>();
  const route = useRoute<any>();
  const insects = useSafeAreaInsets();

  const {data: event, isLoading} = useGetEventById(route.params.event.event_id);
  const {giveReview, isLoading: reviewLoading} = useAddReviewByEventId();
  const [rating, setRating] = useState(0);
  const [isSubmitted, setSubmitted] = useState(false);

  const goBackHandler = () => {
    navigation.goBack();
  };

  const onSubmit1 = () => {
    setSubmitted(true);
    bottomSheetRef.current?.snapToIndex(0);
  };

  const onSubmit = async (data: {comment: string; rating: number; companyRating: number}) => {
    bottomSheetRef.current?.close();
    giveReview(
      {event_id: event?.event_id || 0, comment: data.comment, rating: data.rating},
      {
        onSuccess: data => {
          navigation.goBack();
          Alert.alert('Success', 'Review added successfully!');
        },
        onError: error => {
          console.log(error, 'error');
          
        },
      },
    );
  };

  const styles = StyleSheet.create({
    eventNameText: {
      textAlign: 'center',
      fontWeight: 'bold',
      color: colors.white,
      fontSize: 20,
      marginBottom: 8,
      position: 'absolute',
      zIndex: 10000,
    },
    buttonText: {
      color: colors.white,
      fontWeight: '600',
      textAlign: 'center',
    },
    button: {
      backgroundColor: colors.secondary,
      paddingVertical: 10,
      width: '88%',
      borderRadius: 20,
      marginHorizontal: 10,
      marginTop: 30,
    },
    eventTitle: {
      textAlign: 'center',
      fontWeight: 'bold',
      color: colors.white,
      fontSize: 20,
      marginBottom: 8,
    },
    container: {flex: 1, backgroundColor: colors.white},
    overlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: colors.overlayBackground,
    },
  });

  return (
    <View style={styles.container}>
      <ThemedStatusBar />
      <GoBackHeader customCallback={goBackHandler} isGradientShow={false} />
      <ImageBackground
        style={[
          styles.container,
          {justifyContent: 'flex-end', alignItems: 'center', paddingBottom: insects.bottom + spacing.xl},
        ]}
        source={{uri: event?.image_url}}>
        {/* Event Name (shown after submission) */}
        {isSubmitted && (
          <Animated.Text
            entering={FadeInDown.delay(100).duration(400)}
            style={[styles.eventNameText, {top: insects.top + 45}]}>
            {event?.name}
          </Animated.Text>
        )}

        {/* Overlay */}
        <View style={styles.overlay} />

        {/* Rating Content */}
        <Animated.View
          entering={FadeInUp.delay(200).duration(600)}
          style={{alignItems: 'center', paddingHorizontal: spacing.lg}}>
          <ModernCard
            variant="elevated"
            padding="xl"
            style={{
              backgroundColor: colors.cardBackground + 'E6', // Semi-transparent
              backdropFilter: 'blur(10px)',
              marginBottom: spacing.xl,
            }}>
            <Text
              style={{
                fontSize: typography.fontSize['2xl'],
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                textAlign: 'center',
                marginBottom: spacing.lg,
                textShadowColor: 'rgba(0, 0, 0, 0.3)',
                textShadowOffset: {width: 0, height: 1},
                textShadowRadius: 2,
              }}>
              {t('events.rate_experience') || `What did you think of\n${event?.name}?`}
            </Text>

            <Animated.View entering={FadeInUp.delay(400).duration(500)}>
              <StarRating
                rating={rating}
                onChange={setRating}
                color={colors.warning}
                emptyColor={colors.gray400}
                starSize={40}
                style={{marginBottom: spacing.lg}}
              />
            </Animated.View>

            <ModernButton
              onPress={onSubmit1}
              title={t('generic.submit') || 'Submit Rating'}
              variant="primary"
              size="lg"
              fullWidth
              disabled={rating === 0}
              loading={reviewLoading}
              hapticFeedback
              style={{
                backgroundColor: colors.primary,
                shadowColor: colors.primary,
                shadowOffset: {width: 0, height: 4},
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 8,
              }}
            />
          </ModernCard>
        </Animated.View>
      </ImageBackground>

      <RatingSheet bottomSheetRef={bottomSheetRef} rate={rating} onSubmit={onSubmit} />
    </View>
  );
};

export default RateEvent;
