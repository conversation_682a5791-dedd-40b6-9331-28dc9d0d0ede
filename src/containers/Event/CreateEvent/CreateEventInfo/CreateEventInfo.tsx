import {RouteProp, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import {Formik, FormikProps} from 'formik';
import moment from 'moment-timezone';
import React, {useEffect, useMemo, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Alert,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Switch} from 'react-native-switch';
import * as yup from 'yup';
import {RightArrowIcon} from '~assets/icons';
import Button from '~components/Button/Button';
import CreateEventDateTime from '~components/CreateEvent/CreateEventDateTime/CreateEventDateTime';
import {DATE_FORMAT, TIME_FORMAT} from '~components/DateTimeToggle';
import {GoBackHeader} from '~components/GoBackHeader';
import {Location} from '~components/LocationModal/LocationModal';
import LocationToggle from '~components/LocationToggle';
import GroupCapacityModal from '~components/ModalWithItems/GroupCapacityModal';
import {VisibilityModal} from '~components/ModalWithItems/VisibilityModal';
import ProgressDots from '~components/ProgressDots';
import {SCREENS} from '~constants';
import {useEventCreationStore} from '~providers/eventCreation/zustand';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import auth from '@react-native-firebase/auth';
import {reverseGeocode} from '~Utils/location';
import RadioGroup from 'react-native-radio-buttons-group';
import TicketForm, {TicketFormHandle} from '~components/TicketForm';
import {useTheme} from '~contexts/ThemeContext';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';

const isAndroid = Platform.OS === 'android';

const CreateEventInfo = () => {
  const {colors} = useTheme();
  const navigation = useNavigation<NavigationProps>();
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.CREATE_EVENT_INFO>>();
  const {bottom} = useSafeAreaInsets();
  const {t} = useTranslation();
  const {setIsTabBarDisabled} = useTabBar();
  const [isVisibilityModalOpen, setIsVisibilityModalOpen] = useState(false);
  const [isCapacityModalOpen, setIsCapacityModalOpen] = useState(false);
  const [dateError, setDateError] = useState(false);
  const {setEventInfo} = useEventCreationStore();
  const [isLoading, setIsLoading] = useState(false);
  const {data: userAccountData} = useGetUserAccount(auth().currentUser!.uid);
  const formikRef = useRef<FormikProps<any>>(null);
  const ticketFormRef = useRef<TicketFormHandle>(null);
  let ticketFormData: any = {
    ticketTypes: [],
    promoCodes: [],
  };
  const radioButtons = [
    {
      id: 'Public',
      label: 'Public',
      value: 'Public',
      color: colors.eventInfluencer,
    },
    {
      id: 'Private',
      label: 'Private',
      value: 'Private',
      color: colors.eventInfluencer,
    },
  ];
  const radioButtonsEventType = [
    {
      id: 'free',
      label: 'Free',
      value: 'Free',
      color: colors.eventInfluencer,
    },
    {
      id: 'payment_url',
      label: 'Payment URL',
      value: 'Payment URL',
      color: colors.eventInfluencer,
    },
    {
      id: 'paid_event',
      label: 'Pyxi payment',
      value: 'Paid Event',
      color: colors.eventInfluencer,
    },
  ];

  const isFocused = useIsFocused();

  
  useEffect(() => {
    if (userAccountData && userAccountData.coords && !route.params?.item) {
      reverseGeocode({
        withAccurateAdrs: true,
        coords: {latitude: userAccountData.coords.lat, longitude: userAccountData.coords.long},
      })
        .then(address => {
          const locationData: Location = {
            address: address + '',
            latitude: userAccountData.coords.lat,
            longitude: userAccountData.coords.long,
          };
          if (formikRef.current) {
            formikRef.current.setFieldValue('locationIsChosen', locationData, false);
          }
        })
        .catch(error => {
          console.error('Error fetching geocode data:', error);
        });
    }
  }, [userAccountData, route.params?.item]);

  useEffect(() => {
    if (route.params?.item && route.params.item.coords) {
      reverseGeocode({
        withAccurateAdrs: true,
        coords: {latitude: route.params?.item?.coords.lat, longitude: route.params?.item?.coords.long},
      })
        .then(address => {
          const locationData: Location = {
            address: address + '',
            latitude: route.params?.item?.coords.lat || 0,
            longitude: route.params?.item?.coords.long || 0,
          };
          if (formikRef.current) {
            formikRef.current.setFieldValue('locationIsChosen', locationData, false);
          }
        })
        .catch(error => {
          console.error('Error fetching geocode data:', error);
        });
    }
  }, [route.params?.item]);

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Create Event info', 'CreateEventInfo');
    // Hide tab bar when entering create event flow
    setIsTabBarDisabled(true);

    // Show tab bar when leaving the screen
    return () => {
      setIsTabBarDisabled(false);
    };
  }, [setIsTabBarDisabled]);

  const validationSchema = yup.object().shape({
    eventName: yup.string().trim().required(t('events.event_name_error')),
    eventDescription: yup.string().trim().required(t('events.event_description_error')),
    startDate: yup.string().required(t('events.date_error')),
    startTime: yup.string().required(t('events.date_error')),
    endDate: yup.string().required(t('events.date_error')),
    endTime: yup.string().required(t('events.date_error')),
    eventAddress: yup.string().trim().optional(),
    locationIsChosen: yup.object().required(t('events.location_error')),
    payment_url: yup
      .string()
      .url(t('events.payment_url_error2'))
      .when('event_type', {
        is: (event_type: string) => event_type === 'payment_url',
        then: schema => schema.required(t('events.payment_url_error')),
        otherwise: schema => schema.notRequired(),
      }),
  });

  const initialValue = useMemo(() => {
    if (route.params?.item) {
      return {
        eventName: route.params.item.name,
        eventDescription: route.params.item.description,
        startDate: moment(route.params.item.start_date).format(DATE_FORMAT),
        startTime: moment(route.params.item.start_date).format(TIME_FORMAT),
        endDate: moment(route.params.item.end_date).format(DATE_FORMAT),
        endTime: moment(route.params.item.end_date).format(TIME_FORMAT),
        visibility: route.params.item.private ? t('events.events_private') : t('events.events_public'),
        groupCapacity: route.params.item.number_slots,
        eventAddress: route.params.item.address_name,
        locationIsChosen: route.params.item.coord_address,
        isForKids: route.params.item.is_for_kids,
        is_paid: route.params.item.is_paid,
        payment_url: route.params.item.payment_url,
        event_type: route.params.item.payment_url ? 'payment_url' : route.params.item.is_paid ? 'paid_event' : 'free',
      };
    } else {
      return {
        eventName: '',
        eventDescription: '',
        startDate: moment().format(DATE_FORMAT),
        startTime: moment().format(TIME_FORMAT),
        endDate: moment().add(1, 'hour').format(DATE_FORMAT),
        endTime: moment().add(1, 'hour').format(TIME_FORMAT),
        visibility: t('events.events_public'),
        groupCapacity: 100,
        eventAddress: '',
        locationIsChosen: null,
        isForKids: '',
        is_paid: false,
        payment_url: '',
        event_type: 'free',
      };
    }
  }, [route.params?.item]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      backgroundColor: colors.inputBackground,
      width: '100%',
    },
    mainTextContainer: {
      width: '100%',
      alignItems: 'flex-start',
      paddingHorizontal: 16,
      marginTop: 20,
    },
    mainText: {
      textAlign: 'left',
      lineHeight: 36,
      fontSize: 28,
      color: colors.textPrimary,
      fontWeight: '700',
      marginTop: 12,
      marginBottom: 8,
    },
    subtitleText: {
      textAlign: 'left',
      fontSize: 16,
      color: colors.textSecondary,
      fontWeight: '400',
      lineHeight: 22,
      marginBottom: 24,
    },
    errorContainer: {
      paddingTop: 4,
    },
    errorText: {
      fontSize: 12,
      fontWeight: '400',
      color: colors.error,
      width: '100%',
      lineHeight: 14,
    },
    dateTimeContainer: {
      marginTop: 8,
      width: '100%',
      zIndex: 100,
    },
    datesContainer: {
      marginTop: 4,
      marginLeft: 10,
      flexDirection: 'row',
      justifyContent: 'flex-start',
    },
    datesText: {
      fontSize: 12,
      fontWeight: '400',
      color: colors.statusBlue,
      width: '100%',
    },
    datesErrors: {
      fontSize: 12,
      fontWeight: '400',
      color: colors.error,
      width: '100%',
    },
    textInput: {
      fontWeight: '400',
      fontSize: 16,
      lineHeight: 24,
      width: '100%',
      paddingTop: 16,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      backgroundColor: colors.surface,
      color: colors.textPrimary,
      marginBottom: 16,
    },
    inputContainer: {
      width: '100%',
      paddingHorizontal: 0,
      borderRadius: 12,
    },
    modernCard: {
      backgroundColor: colors.surface,
      borderRadius: 16,
      padding: 20,
      marginBottom: 20,
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.05,
      shadowRadius: 8,
      elevation: 2,
      width: '100%',
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.textPrimary,
      marginBottom: 16,
    },
    visibilityCapacityContainer: {
      marginTop: 12,
      width: '100%',
      borderRadius: 10,
      paddingLeft: 12,
    },
    openModalBtn: {
      alignItems: 'center',
      flexDirection: 'row',
      height: 54,
      borderBottomWidth: 0.5,
      borderBottomColor: colors.gray400,
    },
    isForKids: {
      alignItems: 'center',
      flexDirection: 'row',
      height: 54,
      paddingRight: 12,
    },
    visibilityTitle: {
      fontSize: 17,
      fontWeight: '400',
      lineHeight: 21,
    },
    visibilityRightContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      paddingRight: 12,
      height: '100%',
    },
    scrollViewStyle: {
      width: '100%',
    },
    scrollViewContentContainerStyle: {
      width: '100%',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      paddingTop: 8,
      paddingBottom: 16,
      paddingHorizontal: 16,
    },
    footerContainer: {
      width: '100%',
      paddingHorizontal: 25,
      paddingTop: 16,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      justifyContent: 'flex-end',
    },
    buttonText: {
      fontSize: 15,
      fontWeight: '500',
      color: colors.white,
    },
    buttonContainer: {
      width: '100%',
      height: 40,
      borderRadius: 6,
      backgroundColor: colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 24,
    },
  });

  
  return (
    <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <GoBackHeader isGradientShow={false} />
      <SafeAreaView style={[styles.container, {paddingTop: 30, backgroundColor: colors.background}]}>
        <View style={styles.mainTextContainer}>
          <Text style={styles.mainText}>{t('events.event_info')}</Text>
          <Text style={styles.subtitleText}>Share the details that will help people discover and join your event</Text>
        </View>
        <Formik
          innerRef={formikRef}
          enableReinitialize
          initialValues={initialValue}
          onSubmit={({
            eventName,
            eventDescription,
            eventAddress,
            startDate,
            startTime,
            endDate,
            endTime,
            visibility,
            groupCapacity,
            isForKids,
            payment_url,
            event_type,
            locationIsChosen,
          }) => {
            setIsLoading(true);

            // Check date validation first, regardless of other field states
            if (
              moment(`${startDate} ${startTime}`, 'DD/MM/YYYY h:mm A').isAfter(
                moment(`${endDate} ${endTime}`, 'DD/MM/YYYY h:mm A'),
              )
            ) {
              setDateError(true);
              setIsLoading(false);
              return;
            }
            console.log(ticketFormData, 'ticketFormDataticketFormDataticketFormData');

            setEventInfo({
              name: eventName,
              description: eventDescription,
              address_name: eventAddress,
              payment_url: event_type == 'payment_url' ? payment_url : '',
              start_date: moment.utc(`${startDate} ${startTime}`, 'DD/MM/YYYY h:mm A').format('YYYY-MM-DDTHH:mm:ss'),
              end_date: moment.utc(`${endDate} ${endTime}`, 'DD/MM/YYYY h:mm A').format('YYYY-MM-DDTHH:mm:ss'),
              private: t('events.events_private') === visibility,
              is_paid: event_type == 'paid_event' || event_type == 'payment_url',
              coords: {
                lat: locationIsChosen!.latitude,
                long: locationIsChosen!.longitude,
              },
              number_slots: groupCapacity,
              isForKids: !!isForKids,
              ticketTypes: ticketFormData ? ticketFormData?.ticketTypes : [],
              promoCodes: ticketFormData ? ticketFormData?.promoCodes : [],
              locationIsChosen: locationIsChosen,
            });

            navigation.navigate(
              SCREENS.CREATE_EVENT_GROUPS,
              route.params?.item ? {item: route.params?.item} : undefined,
            );
          }}
          validationSchema={validationSchema}>
          {({values, errors, handleChange, handleSubmit}) => (
            <>
              <ScrollView
                keyboardShouldPersistTaps={'handled'}
                style={styles.scrollViewStyle}
                contentContainerStyle={styles.scrollViewContentContainerStyle}>
                {/* Basic Information Card */}
                <View style={styles.modernCard}>
                  <Text style={styles.sectionTitle}>Basic Information</Text>
                  <View style={styles.inputContainer}>
                    <TextInput
                      placeholder={t('events.event_name_placeholder')}
                      value={values.eventName}
                      onChangeText={handleChange('eventName')}
                      style={[styles.textInput, errors.eventName && {borderColor: colors.error}]}
                      placeholderTextColor={colors.placeholderText}
                    />
                    {errors.eventName && (
                      <View style={styles.errorContainer}>
                        <Text style={styles.errorText}>{errors.eventName}</Text>
                      </View>
                    )}

                    <TextInput
                      placeholder={t('events.event_description_placeholder')}
                      value={values.eventDescription}
                      onChangeText={handleChange('eventDescription')}
                      multiline={true}
                      numberOfLines={4}
                      style={[
                        styles.textInput,
                        {height: 100, textAlignVertical: 'top'},
                        errors.eventDescription && {borderColor: colors.error},
                      ]}
                      placeholderTextColor={colors.placeholderText}
                    />
                    {errors.eventDescription && (
                      <View style={styles.errorContainer}>
                        <Text style={styles.errorText}>{errors.eventDescription}</Text>
                      </View>
                    )}
                  </View>
                </View>
                {/* Date & Time Card */}
                <View style={styles.modernCard}>
                  <Text style={styles.sectionTitle}>Date & Time</Text>
                  <View style={styles.dateTimeContainer}>
                    <CreateEventDateTime
                      startDate={values.startDate}
                      startTime={values.startTime}
                      onChangeStartDate={value => {
                        handleChange('startDate')(value);
                        if (
                          values.endDate &&
                          moment(values.endDate, DATE_FORMAT).isBefore(moment(value, DATE_FORMAT))
                        ) {
                          handleChange('endDate')(moment(value, DATE_FORMAT).add(1, 'd').format(DATE_FORMAT));
                        }
                        if (value && values.startTime && values.endDate && values.endTime) {
                          setDateError(false);
                        }
                      }}
                      onChangeStartTime={value => {
                        handleChange('startTime')(value);
                        if (value && values.startDate && values.endDate && values.endTime) {
                          setDateError(false);
                        }
                      }}
                      endDate={values.endDate}
                      endTime={values.endTime}
                      onChangeEndDate={value => {
                        handleChange('endDate')(value);
                        if (value && values.startTime && values.startDate && values.endTime) {
                          setDateError(false);
                        }
                      }}
                      onChangeEndTime={value => {
                        handleChange('endTime')(value);
                        if (value && values.startTime && values.startDate && values.endDate) {
                          setDateError(false);
                        }
                      }}
                      formikErrors={errors}
                    />
                    <View style={styles.datesContainer}>
                      <Text style={styles.datesText}>
                        {`${values.startDate} ${values.startTime} - ${values.endDate} ${values.endTime} `}
                        <Text style={styles.datesErrors}>{dateError && t('events.date_before_error')}</Text>
                      </Text>
                    </View>
                  </View>
                </View>
                {/* Location Card */}
                <View style={styles.modernCard}>
                  <Text style={styles.sectionTitle}>Location</Text>
                  <LocationToggle
                    errorText={errors.locationIsChosen}
                    location={values.locationIsChosen}
                    onChangeLocation={value => {
                      formikRef.current?.setFieldValue('locationIsChosen', value);
                    }}
                  />

                  <TextInput
                    placeholder={t('events.event_address')}
                    value={values.eventAddress}
                    onChangeText={handleChange('eventAddress')}
                    multiline={true}
                    style={[styles.textInput, {marginTop: 12}]}
                    placeholderTextColor={colors.placeholderText}
                  />

                  {errors.eventAddress && (
                    <View style={styles.errorContainer}>
                      <Text style={styles.errorText}>{errors.eventAddress}</Text>
                    </View>
                  )}
                </View>

                {/* Event Settings Card */}
                <View style={styles.modernCard}>
                  <Text style={styles.sectionTitle}>Event Settings</Text>
                  <View style={styles.visibilityCapacityContainer}>
                    <View style={[styles.openModalBtn, {justifyContent: 'space-between'}]}>
                      <Text style={styles.visibilityTitle}>{t('events.visibility')}</Text>
                      <RadioGroup
                        radioButtons={radioButtons}
                        onPress={id => {
                          handleChange('visibility')(id);
                        }}
                        selectedId={values.visibility}
                        layout="row"
                      />
                    </View>

                    <View style={[styles.isForKids, {borderBottomWidth: 0.5, borderBottomColor: colors.gray400}]}>
                      <View style={{flex: 1}}>
                        <Text style={styles.visibilityTitle}>{t('events.children_event')}</Text>
                      </View>
                      <Switch
                        value={!!values.isForKids}
                        onValueChange={value => handleChange('isForKids')(value ? 'true' : '')}
                        backgroundActive={colors.statusPurple}
                        backgroundInactive={colors.gray400 + '29'}
                        renderActiveText={false}
                        renderInActiveText={false}
                        circleBorderWidth={0}
                        circleSize={27}
                        switchWidthMultiplier={2.2}
                        barHeight={31}
                      />
                    </View>

                    <View
                      style={[
                        values.event_type == 'payment_url' && {
                          borderBottomWidth: 0.5,
                          borderBottomColor: colors.gray400,
                          paddingBottom: 8,
                        },
                        {flexDirection: 'column', alignItems: 'flex-start'},
                      ]}>
                      <View style={{flex: 1, marginVertical: 8}}>
                        <Text style={styles.visibilityTitle}>{t('events.eventtype')}</Text>
                      </View>
                      <RadioGroup
                        radioButtons={radioButtonsEventType}
                        onPress={id => {
                          handleChange('event_type')(id);
                        }}
                        selectedId={values.event_type}
                        layout="row"
                      />
                      {/* <Switch
                      value={isPayment}
                      onValueChange={value => {
                        setIsPayment(value);
                      }}
                      backgroundActive={colors.statusPurple}
                      backgroundInactive={colors.gray400 + "29"}
                      renderActiveText={false}
                      renderInActiveText={false}
                      circleBorderWidth={0}
                      circleSize={27}
                      switchWidthMultiplier={2.2}
                      barHeight={31}
                    /> */}
                    </View>

                    {values.event_type != 'paid_event' && (
                      <TouchableOpacity
                        style={styles.openModalBtn}
                        onPress={() => {
                          Keyboard.dismiss();
                          setIsCapacityModalOpen(true);
                        }}>
                        <Text style={styles.visibilityTitle}>{t('events.group_capacity')}</Text>
                        <View style={styles.visibilityRightContainer}>
                          <Text style={{...styles.visibilityTitle, color: colors.placeholderText}}>
                            {values.groupCapacity}
                          </Text>
                          <RightArrowIcon />
                        </View>
                      </TouchableOpacity>
                    )}

                    {values.event_type == 'payment_url' && (
                      <TextInput
                        placeholder="Payment URL"
                        value={values.payment_url}
                        onChangeText={handleChange('payment_url')}
                        multiline={true}
                        style={{
                          ...styles.textInput,
                          borderBottomColor: colors.gray400,
                        }}
                        placeholderTextColor={colors.placeholderText}
                      />
                    )}
                  </View>
                </View>

                {values.event_type == 'payment_url' && errors.payment_url && (
                  <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>{errors.payment_url}</Text>
                    <Text style={[styles.errorText, {color: colors.black, marginTop: 5}]}>
                      For example: https://www.pyxi.ai/payment_url
                    </Text>
                  </View>
                )}
                {values.event_type == 'paid_event' && (
                  <TicketForm
                    ref={ticketFormRef}
                    tickets={route.params?.item?.tickets || []}
                    promoCode={route.params?.item?.promo_codes || []}
                  />
                )}
              </ScrollView>
              <View style={styles.footerContainer}>
                <Button
                  label={t('generic.continue')}
                  isLoading={isLoading}
                  onPress={() => {
                    if (values.event_type == 'paid_event' && !ticketFormRef.current?.validateForm()) {
                      setIsLoading(false);
                      return;
                    }
                    if (values.event_type == 'paid_event' && !ticketFormRef.current) {
                      Alert.alert('Something went wrong.');
                      setIsLoading(false);
                      return;
                    }
                    const ticketFormDataA = ticketFormRef.current?.getFormData();
                    ticketFormData = ticketFormDataA;
                    handleSubmit();
                  }}
                  textStyle={styles.buttonText}
                  containerStyle={styles.buttonContainer}
                />
                <View style={{marginBottom: 20}}>
                  <ProgressDots dotsNumber={4} selectedDotNumber={3} />
                </View>
              </View>
              <VisibilityModal
                isVisible={isVisibilityModalOpen}
                onPress={value => () => handleChange('visibility')(value)}
                chosenItem={values.visibility}
                onClose={() => setIsVisibilityModalOpen(false)}
              />

              <GroupCapacityModal
                isVisible={isCapacityModalOpen}
                onClose={() => setIsCapacityModalOpen(false)}
                onPress={value => {
                  handleChange('groupCapacity')(value.toString());
                  setIsCapacityModalOpen(false);
                }}
                initialCapacity={Number(values.groupCapacity)}
              />
            </>
          )}
        </Formik>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default CreateEventInfo;
