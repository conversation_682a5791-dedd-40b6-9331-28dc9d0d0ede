import React, {useEffect, useRef, useState} from 'react';
import {
  SafeAreaView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  Platform,
  ScrollView,
  FlatList,
  Dimensions,
  Button,
} from 'react-native';
import {useRoute, useNavigation} from '@react-navigation/native';
import {SCREENS} from '~constants';
import {RouteProp} from '@react-navigation/native';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import moment from 'moment';
import {LONG_DATE_FORMAT} from '~Utils/Time';
import {useGetOrderById} from '~hooks/event/useGetOrderById';
import {useGetEventById} from '~hooks/event/useGetEventById';
import QRCodeGenerator from '~components/QR-codes/QRCodeGenerator';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import auth from '@react-native-firebase/auth';
import {useCancelOrder} from '~hooks/event/useCancelOrder';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import {useDeleteUserEventStatus} from '~hooks/event/useDeleteUserEventStatus';
import StarIcon from '~assets/icons/StarIcon';
import {useTheme} from '~contexts/ThemeContext';
import {ThemedStatusBar} from '~components/ThemedStatusBar';
import { ModernHeader } from '~components/ModernHeader';

const screenWidth = Dimensions.get('window').width;

const PaymentSuccess: React.FC = () => {
  const {colors} = useTheme();
  const DATE_FORMAT = 'ddd ' + LONG_DATE_FORMAT;
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.PAYMENT_SUCCESS>>();
  const navigation = useNavigation<NavigationProps>();
  const {eventId, selectedTickets, order_id} = route.params;

  const [qrValue, setQrValue] = useState<string>('');
  const {data: orderDetails, isLoading, refetch} = useGetOrderById(order_id);
  const {data: userAccount} = useGetUserAccount(auth().currentUser?.uid);
  const {data: eventData, isLoading: eventLoading} = useGetEventById(orderDetails?.event_id);
  const {mutate: cancelOrder, isLoading: cancelLoading} = useCancelOrder();
  const {mutateAsync: deleteEventUserStatus, isLoading: cancelFreeLoading} = useDeleteUserEventStatus();
  const flatListRef = useRef<any>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const styles = StyleSheet.create({
    button: {
      flex: 1, // Make the buttons take equal space
      marginHorizontal: 5, // Add spacing between buttons
    },
    addToWalletText: {
      color: colors.white,
      fontWeight: 'bold',
    },
    cancelButtonText: {
      color: colors.white,
      fontWeight: 'bold',
    },
    navigationContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      marginTop: 25,
    },
    pageIndicator: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    ticketContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 16,
    },
    ticketTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      marginBottom: 20,
      textAlign: 'center',
    },
    loaderContainer: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 100,
      backgroundColor: colors.overlayBackground,
      minHeight: Dimensions.get('window').height,
    },
    successfultext: {
      color: colors.white,
    },
    successcontainer: {
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.statusGreen,
      paddingHorizontal: 8,
      paddingVertical: 10,
      marginBottom: 20,
      borderRadius: 10,
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.5,
      shadowRadius: 4,
      elevation: 5,
      marginHorizontal: 12,
    },
    cancelTicketButton: {
      backgroundColor: colors.error, // Red color for cancellation
      paddingVertical: 15,
      borderRadius: 10,
      alignItems: 'center',
      marginHorizontal: 15,
      marginTop: 10, // Add some spacing from the wallet button
    },
    cancelIcon: {
      tintColor: colors.white,
      width: 24,
      height: 24,
      marginRight: 10,
    },
    walletIcon: {
      tintColor: colors.white,
      width: 24,
      height: 24,
      marginRight: 10,
    },
    borderView: {
      borderColor: colors.border,
      borderBottomWidth: 1,
      marginBottom: 10,
    },
    container: {flex: 1, backgroundColor: colors.white},
    backButton: {
      padding: 10,
      margin: 10,
      alignSelf: 'flex-start',
    },
    eventDetails: {
      flexDirection: 'row',
      padding: 15,
      backgroundColor: colors.white,
      alignItems: 'center',
      borderBottomWidth: 1,
      borderColor: colors.border,
      marginTop: Platform.OS == 'ios' ? 20 : 50,
    },
    eventImage: {width: 100, height: 100, borderRadius: 10, marginRight: 10},
    eventInfo: {flex: 1},
    eventTitle: {fontSize: 18, fontWeight: 'bold', color: colors.textPrimary},
    eventLocation: {fontSize: 14, color: colors.textSecondary, marginTop: 5},
    eventTime: {fontSize: 12, color: colors.textSecondary, marginTop: 5},
    ticketList: {
      padding: 10,
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 5,
      backgroundColor: colors.white,
      margin: 15,
      borderRadius: 10,
    },
    reviewItem: {marginBottom: 10, flexDirection: 'row'},
    ticketItem: {marginBottom: 10, flexDirection: 'row', justifyContent: 'space-between'},
    ticketType: {fontSize: 16, fontWeight: '600'},
    ticketQuantity: {fontSize: 14, color: colors.textSecondary},
    qrContainer: {alignItems: 'center', marginBottom: 20, flex: 1},
    addToWalletButton: {
      backgroundColor: colors.eventInfluencer,
      paddingVertical: 15,
      borderRadius: 10,
      alignItems: 'center',
      marginHorizontal: 15,
      marginTop: 10, // Add some spacing from the wallet button
    },
  });

  const handleCancelOrder = () => {
    if (eventData && eventData?.is_paid) {
      cancelOrder(order_id, {
        onSuccess: data => {
          refetch();
          Notifier.showNotification({
            title: 'Order canceled successfully.',
            Component: NotifierComponents.Alert,
            componentProps: {
              alertType: 'success',
            },
          });
        },
        onError: error => {
          console.error('Error canceling order:', error);
        },
      });
    } else {
      deleteEventUserStatus(
        {event_id: orderDetails?.event_id || 0, user_id: auth().currentUser!.uid},
        {
          onSuccess: data => {
            refetch();
            Notifier.showNotification({
              title: 'Order canceled successfully.',
              Component: NotifierComponents.Alert,
              componentProps: {
                alertType: 'success',
              },
            });
          },
          onError: error => {
            console.error('Error canceling order:', error);
          },
        },
      );
    }
  };

  useEffect(() => {
    const qrData = {
      eventId,
    };
    setQrValue(JSON.stringify(qrData));
  }, [eventId]);

  const handleAddToWallet = () => {
    Alert.alert('Adding ticket to wallet');
  };
  const goBackHandler = () => {
    navigation.goBack();
  };

  const handleTicketCancellation = () => {
    Alert.alert(
      'Cancel Ticket',
      'Are you sure you want to cancel your ticket?',
      [
        {text: 'No', style: 'cancel'},
        {text: 'Yes', onPress: () => handleCancelOrder()},
      ],
      {cancelable: true},
    );
  };

  const calculateTotal = (): number => {
    return orderDetails?.total_amount || 0;
  };

  const getStatusColor = (status: string) => {
    if (status == 'succeeded') {
      return colors.statusGreen;
    } else if (status == 'canceled') {
      return colors.error;
    } else {
      return colors.warning;
    }
  };

  const getPaymentInfo = (status: string, is_paid?: boolean) => {
    if (!is_paid) {
      if (status === 'succeeded') {
        return {
          title: 'Booking Confirmed!',
          description: 'Your booking has been successfully confirmed. We look forward to seeing you at the event!',
        };
      } else if (status === 'canceled') {
        return {
          title: 'Booking Canceled',
          description: 'Your booking has been canceled. If this was a mistake, please contact support.',
        };
      } else if (status === 'failed') {
        return {
          title: 'Booking Failed',
          description: 'Your booking could not be completed. Please try again or contact support.',
        };
      } else if (status === 'refunded') {
        return {
          title: 'Booking Refunded',
          description: 'Your booking has been canceled and refunded. If you have questions, please contact support.',
        };
      } else {
        return {
          title: 'Booking Pending',
          description: 'Your booking is currently being processed. You will receive a confirmation shortly.',
        };
      }
    }

    if (status === 'succeeded') {
      return {
        title: 'Payment Successful!',
        description: 'Thank you for your payment. We look forward to seeing you at the event!',
      };
    } else if (status === 'canceled') {
      return {
        title: 'Payment Canceled',
        description: 'Your payment has been canceled. Please try again or contact support for assistance.',
      };
    } else if (status === 'failed') {
      return {
        title: 'Payment Failed',
        description: 'Your payment could not be completed. Please check your payment details and try again.',
      };
    } else if (status === 'refunded') {
      return {
        title: 'Payment Refunded',
        description: 'Your payment has been successfully refunded. If you have any questions, please contact support.',
      };
    } else {
      return {
        title: 'Payment Pending',
        description: 'Your payment is currently being processed. You will receive a confirmation shortly.',
      };
    }
  };
  const handleNext = () => {
    if (
      orderDetails &&
      orderDetails.order_details &&
      currentIndex < orderDetails.order_details.length - 1 &&
      flatListRef.current
    ) {
      const nextIndex = currentIndex + 1;
      flatListRef.current.scrollToIndex({index: nextIndex, animated: true});
      setCurrentIndex(nextIndex);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0 && flatListRef.current) {
      const prevIndex = currentIndex - 1;
      flatListRef.current.scrollToIndex({index: prevIndex, animated: true});
      setCurrentIndex(prevIndex);
    }
  };

  const renderTicket = ({item, index}: any) => (
    <View style={[styles.ticketContainer, {width: screenWidth}]}>
      <Text style={styles.ticketTitle}>Ticket {index + 1}</Text>
      <QRCodeGenerator
        qrCode={item.qr_codes && item.qr_codes.length > 0 ? item.qr_codes[0] : undefined}
        user={userAccount}
        event={eventData}
        isBigQr={true}
      />
    </View>
  );

  const isEventEnded = () => {
    if (!eventData) {
      return false;
    }

    if (moment(eventData.start_date).isBefore(moment())) {
      return true;
    }

    return false;
  };

  const onRateEventClick = () => {
    if (eventData) {
      navigation.navigate(SCREENS.RATE_EVENT, {event: eventData});
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ThemedStatusBar />

      <ModernHeader
        title={orderDetails?.status === 'succeeded' ? 'Payment Successful' : 'Payment Status'}
        subtitle={eventData?.name}
        onBackPress={goBackHandler}
        variant="elevated"
      />
      <ScrollView>
        <View style={styles.eventDetails}>
          <Image source={{uri: eventData?.image_url}} style={styles.eventImage} />
          <View style={styles.eventInfo}>
            <Text style={styles.eventTitle}>{eventData?.name}</Text>
            <Text style={styles.eventLocation}>{eventData?.coord_address}</Text>
            <Text style={styles.eventTime}>
              {eventData?.start_date ? moment.utc(eventData?.start_date).format(DATE_FORMAT) : ''} -{'\n'}
              {eventData?.end_date ? moment.utc(eventData?.end_date).format(DATE_FORMAT) : ''}
            </Text>
          </View>
        </View>

        {eventData?.is_paid ? (
          <View style={styles.ticketList}>
            {orderDetails?.order_details?.map(ticket => (
              <View key={ticket.name} style={styles.ticketItem}>
                <Text style={styles.ticketType}>
                  {ticket.name} -{' '}
                  <Text style={styles.ticketQuantity}>
                    {`${ticket.currency} ${ticket.discounted_price}`} * {ticket.count}
                  </Text>
                </Text>
                <Text style={styles.ticketType}>
                  {ticket.currency} {ticket.discounted_price * (ticket.count || 1)}
                </Text>
              </View>
            ))}
            <View style={styles.borderView} />
            <View style={styles.ticketItem}>
              <Text style={styles.ticketType}>Total</Text>
              <Text style={styles.ticketType}>
                {orderDetails?.currency} {calculateTotal()}
              </Text>
            </View>
          </View>
        ) : (
          <View style={[styles.ticketList, {padding: 12}]}>
            <View style={[styles.ticketItem, {marginBottom: 0}]}>
              <Text style={styles.ticketType}>No of ticket: </Text>
              <Text style={styles.ticketType}>{orderDetails?.order_details.length}</Text>
            </View>
          </View>
        )}

        {isEventEnded() && (
          <TouchableOpacity
            onPress={onRateEventClick}
            style={[styles.ticketList, {padding: 10, margin: 0, marginHorizontal: 15, marginBottom: 15}]}>
            <View style={[styles.reviewItem, {marginBottom: 0, alignSelf: 'center', alignItems: 'center'}]}>
              <StarIcon />
              <Text style={[styles.ticketType, {marginLeft: 3}]}>Rate Event</Text>
            </View>
          </TouchableOpacity>
        )}

        {qrValue && orderDetails?.status && (
          <View style={styles.qrContainer}>
            <View style={[styles.successcontainer, {backgroundColor: getStatusColor(orderDetails.status)}]}>
              <Text style={styles.successfultext}>
                <Text style={{fontWeight: 'bold'}}>
                  {getPaymentInfo(orderDetails.status, eventData?.is_paid).title}
                </Text>
                {' ' + getPaymentInfo(orderDetails.status, eventData?.is_paid).description}
              </Text>
            </View>

            {orderDetails?.status == 'succeeded' && (
              <View style={{flex: 1}}>
                <FlatList
                  data={orderDetails?.order_details}
                  keyExtractor={(item, index) => `ticket-${index}`}
                  horizontal
                  pagingEnabled
                  ref={flatListRef}
                  renderItem={renderTicket}
                  showsHorizontalScrollIndicator={false}
                  onMomentumScrollEnd={e => {
                    const offset = e.nativeEvent.contentOffset.x;
                    const index = Math.round(offset / screenWidth);
                    setCurrentIndex(index);
                  }}
                />
                <View style={styles.navigationContainer}>
                  <Button title="Previous" onPress={handlePrevious} disabled={currentIndex === 0} />
                  <Text style={styles.pageIndicator}>
                    {currentIndex + 1} / {orderDetails?.order_details.length}
                  </Text>
                  <Button
                    title="Next"
                    onPress={handleNext}
                    disabled={currentIndex === orderDetails.order_details.length - 1}
                  />
                </View>
              </View>
            )}
          </View>
        )}
        {orderDetails?.status == 'succeeded' && (
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <TouchableOpacity style={[styles.button, styles.addToWalletButton]} onPress={handleAddToWallet}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Image
                  source={require('~assets/icons/wallet.png')} // Replace with your wallet icon
                  style={styles.walletIcon}
                />
                <Text style={styles.addToWalletText}>Add to Wallet</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, styles.cancelTicketButton]} onPress={handleTicketCancellation}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Image
                  source={require('~assets/icons/cancel.png')} // Replace with your cancel icon
                  style={styles.cancelIcon}
                />
                <Text style={styles.cancelButtonText}>Cancel Ticket</Text>
              </View>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
      {(isLoading || eventLoading || cancelLoading || cancelFreeLoading) && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size={'large'} color={colors.eventInfluencer} />
        </View>
      )}
    </SafeAreaView>
  );
};

export default PaymentSuccess;
