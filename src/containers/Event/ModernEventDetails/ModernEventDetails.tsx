import React, {useEffect} from 'react';
import {View, StyleSheet} from 'react-native';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {useGetEventById} from '~hooks/event/useGetEventById';
import {RootStackParamsList, NavigationProps} from '~types/navigation/navigation.type';
import {SCREENS} from '~constants';
import CompleteModernEventDetail from '~components/HomeScreenComponent/tabComponents/HomeContent/CompleteModernEventDetail';
import ModernSpinner from '~components/ModernSpinner';
import {useTheme} from '~contexts/ThemeContext';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {logScreenView} from '~Utils/firebaseAnalytics';

const ModernEventDetails: React.FC = () => {
  const {colors} = useTheme();
  const navigation = useNavigation<NavigationProps>();
  const route = useRoute<RouteProp<RootStackParamsList, SCREENS.HOME_EVENT>>();
  const {setIsTabBarDisabled} = useTabBar();

  const {eventId, item: itemFromRoute} = route.params;

  const {data: fetchedItem, isLoading, isFetching} = useGetEventById(eventId);

  // Use fetched item or fallback to route item
  const event = fetchedItem || itemFromRoute;

  // Only show loading spinner if we have no data at all (not just when fetching)
  const shouldShowLoading = isLoading && !event;

  // Debug logging for cache behavior (can be removed in production)
  useEffect(() => {
    console.log('🔍 [EVENT DETAIL CACHE] Status:', {
      eventId,
      hasRouteItem: !!itemFromRoute,
      hasFetchedItem: !!fetchedItem,
      isLoading,
      isFetching,
      shouldShowLoading,
    });
  }, [eventId, itemFromRoute, fetchedItem, isLoading, isFetching, shouldShowLoading]);

  useEffect(() => {
    // Hide tab bar when entering event details
    setIsTabBarDisabled(true);

    // Log screen view
    logScreenView('EventDetails', 'ModernEventDetails');

    // Restore tab bar when leaving
    return () => {
      setIsTabBarDisabled(false);
    };
  }, []); // Remove setIsTabBarDisabled from dependencies to prevent re-runs

  const handleClose = () => {
    setIsTabBarDisabled(false);
    navigation.goBack();
  };

  if (shouldShowLoading) {
    return (
      <View style={[styles.loadingContainer, {backgroundColor: colors.background}]}>
        <ModernSpinner size={120} />
      </View>
    );
  }

  // If we don't have event data at all, show error state
  if (!event) {
    return (
      <View style={[styles.loadingContainer, {backgroundColor: colors.background}]}>
        <ModernSpinner size={120} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CompleteModernEventDetail event={event} onClose={handleClose} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ModernEventDetails;
