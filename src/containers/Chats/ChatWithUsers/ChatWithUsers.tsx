import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import { RouteProp, useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import moment from 'moment-timezone';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, ImageBackground, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { KeyboardGestureArea, useKeyboardHandler } from 'react-native-keyboard-controller';
import Animated, { useAnimatedStyle, useSharedValue } from 'react-native-reanimated';
import { addDateHeaders } from '~Utils/chat';
import { ChevronIcon, LogoIcon } from '~assets/icons';
import { ChatInput, ChatItem, ModernChatHeader, ChatTypingIndicator } from '~components/Chat';
import ModernChatInput from '~components/Chat/ModernChatInput/ModernChatInput';
import OfflineChatInput from '~components/Chat/OfflineChatInput/OfflineChatInput';

import { SCREENS } from '~constants';
import { ThemedStatusBar } from '~components/ThemedStatusBar/ThemedStatusBar';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import { useGetUserAccount } from '~hooks/user/useGetUser';
import FirebaseChatsService from '~services/FirebaseChats';
import { CHAT_MESSAGE_TYPE_ENUM, CHAT_TYPE_ENUM, ChatType, ChatTypeWithKey, MESSAGE_STATUS } from '~types/chat';
import { MessageStatus } from '~constants/messageStatus';
import { haptics } from '~Utils/haptics';
import OfflineMessageNotification from '~components/Chat/OfflineMessageNotification/OfflineMessageNotification';
import OfflineChatStorage from '~services/OfflineChatStorage/OfflineChatStorage';
import MessageRetryService from '~services/MessageRetryService';
import NetInfo from '@react-native-community/netinfo';
import FastImage from 'react-native-fast-image';
import { spacing, typography, borderRadius, shadows } from '~constants';
import { NavigationProps, RootStackParamsList } from '~types/navigation/navigation.type';
import { useMessageStatus } from '~hooks/chat/useMessageStatus';
import { useChatStore } from '~providers/chats/zustand'; // Імпортуємо ваш Zustand store
import { logScreenView } from '~Utils/firebaseAnalytics';
import { useGetBusinessAccount } from '~hooks/business/useGetBusinessAccount';
import { Business } from '~types/api/business';
import { User } from '~types/api/user';
import { Notifier, NotifierComponents } from 'react-native-notifier';
import LikeButton from '~components/LikeButton';
import { useTheme } from '~contexts/ThemeContext';

const useKeyboardAnimationAndroid = () => {
  // Removed useTheme() as it was causing unnecessary re-renders
  // const {colors} = useTheme();
  const progress = useSharedValue(0);
  const height = useSharedValue(0);

  useKeyboardHandler({
    onMove: e => {
      'worklet';

      progress.value = e.progress;
      height.value = e.height;
    },
    onInteractive: e => {
      'worklet';

      progress.value = e.progress;
      height.value = e.height;
    },
  });

  return { height, progress };
};

const useKeyboardAnimationIos = () => {
  const progress = useSharedValue(0);
  const height = useSharedValue(0);
  const shouldUseOnMoveHandler = useSharedValue(false);
  useKeyboardHandler({
    onStart: e => {
      'worklet';

      if (progress.value !== 1 && progress.value !== 0 && e.height !== 0) {
        shouldUseOnMoveHandler.value = true;
        return;
      }

      progress.value = e.progress;
      height.value = e.height;
    },
    onInteractive: e => {
      'worklet';

      progress.value = e.progress;
      height.value = e.height;
    },
    onMove: e => {
      'worklet';

      if (shouldUseOnMoveHandler.value) {
        progress.value = e.progress;
        height.value = e.height;
      }
    },
    onEnd: e => {
      'worklet';

      height.value = e.height;
      progress.value = e.progress;
      shouldUseOnMoveHandler.value = false;
    },
  });

  return { height, progress };
};

const AnimatedFlatList = Animated.createAnimatedComponent(FlashList);

const useKeyboardAnimation = Platform.OS === 'android' ? useKeyboardAnimationAndroid : useKeyboardAnimationIos;

const ChatWithUsers = () => {
  console.log('🚨🚨🚨 ChatWithUsers: COMPONENT IS RENDERING! 🚨🚨🚨');
  const { colors } = useTheme();
  const { setIsTabBarDisabled } = useTabBar();
  const { params } = useRoute<RouteProp<RootStackParamsList, SCREENS.USER_CHAT>>();


  // Add authentication check
  const currentUserId = auth().currentUser?.uid;
  const { data: currentUser } = useGetUserAccount(currentUserId || '');
  const { image, userName, fromEventId, fromScreen } = params;

  // Early return if no authentication
  if (!currentUserId) {
    console.log('ChatWithUsers: No authenticated user found');
    return (
      <View style={{ flex: 1, backgroundColor: colors.background, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: colors.textPrimary }}>Authentication required...</Text>
      </View>
    );
  }
  const { data: host } = useGetBusinessAccount('wLJLEn8J6oN9RpPyep2BjdnagcA2');
  const navigation = useNavigation<NavigationProps>();
  const { t } = useTranslation();
  // Extract functions separately to prevent infinite loops
  const acceptChat = useChatStore(state => state.acceptChat);
  const checkChatAcceptance = useChatStore(state => state.checkChatAcceptance);
  const isAcceptedChats = useChatStore(state => state.isAcceptedChats);

  const defaultCallback = () => {
    // If we have navigation context from an event, navigate back to that event
    if (fromEventId && fromScreen === 'EventDetails') {
      console.log('Navigating back to event details:', fromEventId);
      navigation.navigate(SCREENS.HOME_EVENT, { eventId: fromEventId });
    } else {
      // Default back navigation
      navigation.goBack();
    }
  };

  // Hide tab bar when chat is focused
  useFocusEffect(
    useCallback(() => {
      setIsTabBarDisabled(true);
      return () => {
        setIsTabBarDisabled(false);
      };
    }, [setIsTabBarDisabled]),
  );

  useEffect(() => {
    logScreenView('Chat With Users', 'ChatWithUsers');
  }, []);

  const key = params?.chatId;

  const [currentChat, setCurrentChat] = useState<ChatTypeWithKey | null>(null);
  const [isAccepted, setIsAccepted] = useState<boolean | null>(null);
  const [isInitiator, setIsInitiator] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  // Offline message notification state
  const [showOfflineNotification, setShowOfflineNotification] = useState(false);
  const [offlineNotificationData, setOfflineNotificationData] = useState({
    successCount: 0,
    failureCount: 0,
  });

  // Offline messages state
  const [offlineMessages, setOfflineMessages] = useState<any[]>([]);

  // Network state
  const [isOnline, setIsOnline] = useState(true);

  // Message status tracking
  const { updateMessageStatus, getMessageStatus, pendingMessages, addPendingMessage } = useMessageStatus();

  // Use useState to prevent infinite loops with user ID calculation
  const [otherUserId, setOtherUserId] = useState<string>('');

  // Combine Firestore messages with offline messages
  const combinedMessages = useMemo(() => {
    const firestoreMessages = currentChat?.history || [];

    // Combine and sort all messages by timestamp
    const allMessages = [...firestoreMessages, ...offlineMessages];
    return allMessages.sort((a: any, b: any) => {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return timeA - timeB; // Oldest first
    });
  }, [currentChat?.history, offlineMessages]);

  // Update otherUserId only when currentChat.userIds actually changes
  useEffect(() => {
    if (currentChat?.userIds) {
      const foundUserId = currentChat.userIds.find(user => user.toLowerCase() !== (currentUserId || '').toLowerCase());
      if (foundUserId && foundUserId !== otherUserId) {
        setOtherUserId(foundUserId);
      }
    }
  }, [currentChat?.userIds, otherUserId, currentUserId]);

  const { data: otherUser } = useGetUserAccount(otherUserId, false);
  const { data: otherBusinessUser } = useGetBusinessAccount(otherUserId, false);

  // Remove this problematic useEffect that's causing infinite re-renders
  // The data will be fetched automatically by the hooks when userIds change
  // useEffect(() => {
  //   if (currentChat?.userIds && currentChat.userIds[userIndex || 0]) {
  //     refetchBusinessAccount().then(data => {
  //       if (!data.data) {
  //         refetchUserAccount();
  //       }
  //     });
  //   }
  // }, [currentChat?.userIds, userIndex, refetchBusinessAccount, refetchUserAccount]);

  const isConfirmationRequired = useMemo(() => {
    // Temporarily disable to fix infinite loop
    return false;
    // if (currentChat && currentChat.history && currentChat.history.length > 1) {
    //   const isConfirmMessage = currentChat.history[0];
    //   if (isConfirmMessage.type == CHAT_MESSAGE_TYPE_ENUM.ISSUE_CONFIRMATION) {
    //     return true;
    //   }
    //   return false;
    // }
    // return false;
  }, []);

  // Re-enable keyboard animation for proper chat input positioning
  const { height, progress } = useKeyboardAnimation();

  const flatListRef = useRef<any>(null);

  // Fix keyboard handling - use paddingBottom instead of marginBottom
  const scrollViewStyle = useAnimatedStyle(
    () => ({
      flex: 1,
      paddingBottom: height.value,
    }),
    [],
  );

  // Simplified chat loading to prevent infinite loops
  useEffect(() => {
    if (!key || !currentUserId) {
      console.log('ChatWithUsers: Missing key or currentUserId', { key, currentUserId });
      return;
    }

    let isMounted = true;
    console.log('ChatWithUsers: Setting up chat listener for key:', key);

    const loadChatData = async () => {
      try {
        // Set up real-time chat listener first
        const unsubscribe = firestore()
          .collection('chats')
          .doc(key)
          .onSnapshot(
            documentSnapshot => {
              if (!isMounted) return;

              console.log('ChatWithUsers: Chat document snapshot received', {
                exists: documentSnapshot.exists,
                id: documentSnapshot.id,
              });

              const data = documentSnapshot.data() as ChatTypeWithKey | undefined;
              if (data) {
                console.log('ChatWithUsers: Chat data loaded', {
                  type: data.type,
                  userIds: data.userIds,
                  historyLength: data.history?.length || 0,
                });

                // Sort messages by timestamp to ensure correct order
                const sortedMessages = (data.history || []).sort((a: any, b: any) => {
                  const timeA = new Date(a.timestamp).getTime();
                  const timeB = new Date(b.timestamp).getTime();
                  return timeA - timeB; // Oldest first, since list is inverted
                });

                setCurrentChat({
                  ...data,
                  history: sortedMessages,
                  key: documentSnapshot.id,
                } as ChatTypeWithKey);

                // Check if current user is the chat initiator
                const isCurrentUserInitiator = data.userIds?.[0] === currentUserId;
                setIsInitiator(isCurrentUserInitiator);
                console.log('ChatWithUsers: User is initiator:', isCurrentUserInitiator);

                // Set acceptance logic based on chat type and user role
                if (data.type === 'contact-pyxi' || data.type === CHAT_TYPE_ENUM.ORGANISATION) {
                  // Support chats and organisation chats are always accepted
                  setIsAccepted(true);
                } else if (isCurrentUserInitiator) {
                  // Initiators of user-to-user chats are always accepted
                  setIsAccepted(true);
                } else {
                  // For non-initiators of user-to-user chats, check AsyncStorage
                  checkChatAcceptance(key)
                    .then(accepted => {
                      if (isMounted) {
                        setIsAccepted(accepted);
                      }
                    })
                    .catch(error => {
                      console.error('ChatWithUsers: Error checking chat acceptance:', error);
                      if (isMounted) {
                        // Default to false for user-to-user chats if there's an error
                        setIsAccepted(false);
                      }
                    });
                }
              } else {
                console.log('ChatWithUsers: No chat data found for document');
              }
            },
            error => {
              console.error('ChatWithUsers: Chat subscription error:', error);
              if (isMounted) {
                setIsAccepted(false);
              }
            },
          );

        return unsubscribe;
      } catch (error) {
        console.error('Error loading chat data:', error);
        if (isMounted) {
          setIsAccepted(false);
        }
      }
    };

    const unsubscribePromise = loadChatData();

    return () => {
      isMounted = false;
      unsubscribePromise.then(unsubscribe => {
        if (unsubscribe) {
          unsubscribe();
        }
      });
    };
  }, [key, currentUserId, checkChatAcceptance]); // Include necessary dependencies

  // Network state monitoring
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const isNowOnline = Boolean(state.isConnected && state.isInternetReachable);
      setIsOnline(isNowOnline);
    });

    return () => unsubscribe();
  }, []);

  // Initialize retry service
  useEffect(() => {
    const retryService = MessageRetryService.getInstance();
    retryService.initialize();

    return () => {
      retryService.cleanup();
    };
  }, []);

  // Extracted loadOfflineMessages function for reuse
  const loadOfflineMessages = async () => {
    if (!key) return;

    try {
      const unsentMessages = await OfflineChatStorage.getUnsentMessages();
      const chatOfflineMessages = unsentMessages
        .filter(msg => msg.chatId === key)
        .map(msg => ({
          id: msg.tempMessageId,
          message: msg.text,
          text: msg.text,
          timestamp: msg.timestamp,
          user_id: msg.userId,
          sender_id: msg.userId,
          senderId: msg.userId,
          user_name: msg.userName,
          sender: msg.userName,
          senderName: msg.userName,
          user_image: msg.userImage || '',
          sender_image: msg.userImage || '',
          status: msg.status,
          messageStatus: msg.status,
          isPending: true,
        }));

      setOfflineMessages(chatOfflineMessages);
      console.log('📱 Loaded offline messages for chat:', { chatId: key, count: chatOfflineMessages.length });
    } catch (error) {
      console.error('Failed to load offline messages:', error);
    }
  };

  // Load offline messages and try to sync when chat opens
  useEffect(() => {
    if (!key) return;

    // Load offline messages immediately for display
    loadOfflineMessages();

    // Try to send unsent messages when chat opens (if online)
    if (isOnline) {
      const retryService = MessageRetryService.getInstance();
      retryService.retryChatMessages(key, onSendMessage, {
        onMessageStatusChange: handleMessageStatusChange,
        onOfflineMessagesSent: handleOfflineMessagesSent,
      });
    }
  }, [key, isOnline]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (combinedMessages && combinedMessages.length > 0) {
      setTimeout(() => {
        flatListRef?.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [combinedMessages.length]);

  // Removed unreadMessagesCount useMemo as it was causing infinite loops and not being used

  // Handle message status changes
  const handleMessageStatusChange = (tempId: string, status: MessageStatus, realMessageId?: string) => {
    updateMessageStatus(tempId, status, realMessageId);

    // Refresh offline messages when status changes
    if (status === 'pending') {
      // Reload offline messages to show the new one
      const loadOfflineMessages = async () => {
        try {
          const unsentMessages = await OfflineChatStorage.getUnsentMessages();
          const chatOfflineMessages = unsentMessages
            .filter(msg => msg.chatId === key)
            .map(msg => ({
              id: msg.tempMessageId,
              message: msg.text, // Use 'message' instead of 'text'
              text: msg.text, // Keep both for compatibility
              timestamp: msg.timestamp,
              user_id: msg.userId,
              sender_id: msg.userId, // Add sender_id for ChatItem compatibility
              senderId: msg.userId, // Add senderId for ChatItem compatibility
              user_name: msg.userName,
              sender: msg.userName, // Add sender for ChatItem compatibility
              senderName: msg.userName, // Add senderName for ChatItem compatibility
              user_image: msg.userImage || '',
              sender_image: msg.userImage || '', // Add sender_image for ChatItem compatibility
              status: msg.status,
              messageStatus: msg.status, // Add messageStatus for ModernChatBubble
              isPending: true,
            }));

          setOfflineMessages(chatOfflineMessages);
        } catch (error) {
          console.error('Failed to refresh offline messages:', error);
        }
      };

      loadOfflineMessages();
    }
  };

  // Handle offline messages sent notification
  const handleOfflineMessagesSent = (successCount: number, failureCount: number) => {
    console.log(`📤 Offline messages result: ${successCount} success, ${failureCount} failed`);

    // Update notification data and show notification
    setOfflineNotificationData({ successCount, failureCount });
    setShowOfflineNotification(true);

    // Provide haptic feedback
    if (successCount > 0) {
      haptics.success();
    } else if (failureCount > 0) {
      haptics.error();
    }
  };

  const onSendMessage = async (text: string, tempMessageId?: string) => {
    try {
      console.log('ChatWithUsers: onSendMessage called with:', {
        text,
        key,
        userId: currentUserId,
        userName: `${currentUser?.first_name || ''} ${currentUser?.last_name || ''}`,
        currentChatStatus: currentChat?.status,
        currentChatType: currentChat?.type,
        tempMessageId,
        isAccepted,
        isInitiator,
      });

      // Validate required parameters
      if (!key) {
        console.error('ChatWithUsers: Missing chat key');
        throw new Error('Chat ID is required');
      }
      if (!currentUserId) {
        console.error('ChatWithUsers: Missing current user ID');
        throw new Error('User authentication required');
      }
      if (!text?.trim()) {
        console.error('ChatWithUsers: Empty message text');
        throw new Error('Message text is required');
      }

      // Simplified chat status handling - only update status if needed
      if (currentChat && currentChat?.status !== 'open' && currentChat?.type !== 'contact-pyxi') {
        console.log('ChatWithUsers: Chat is not open, updating status to open...');
        const chatRef = firestore().collection('chats').doc(key);
        await chatRef.update({ status: 'open' });
        console.log('ChatWithUsers: Chat status updated to open');
      }

      // Scroll to bottom (end of list) since we removed inverted
      flatListRef?.current?.scrollToEnd({ animated: true });
      const messageId = await FirebaseChatsService.pushMessageForUsers({
        chat_id: key,
        user_id: currentUserId,
        user_image: currentUser?.photo || '',
        user_name: `${currentUser?.first_name || ''} ${currentUser?.last_name || ''}`,
        text: text,
        temp_message_id: tempMessageId,
      });
      console.log('ChatWithUsers: Message sent successfully');
      return messageId;
    } catch (error) {
      console.error('ChatWithUsers: Error sending message:', error);
      throw error; // Re-throw so ChatInput can handle it
    }
  };

  const handleAccept = async () => {
    setIsAccepted(true);
    await acceptChat(key); // Зберегти стан прийняття чату
  };

  const handleReject = async () => {
    await firestore().collection('chats').doc(key).delete();
    navigation.goBack();
  };

  const onEventIssueClick = () => {
    onSendMessage(
      `Thanks for confirming it's an event related issue, ${currentChat?.users[0]}! Can you please provide more details about the issue?`,
    );
  };

  const onCloseIssue = async () => {
    const chatRef = firestore().collection('chats').doc(key);
    await chatRef.update({ status: 'closed' });
    navigation.goBack();
    Notifier.showNotification({
      title: 'Your support issue has been successfully closed. If you need further assistance, feel free to reach out!',
      Component: NotifierComponents.Alert,
      componentProps: {
        alertType: 'success',
      },
    });
  };

  const onTechnicalIssueClick = async () => {
    const chatId = await FirebaseChatsService.createOrganisationChat({
      user_id1: currentUserId,
      user_id2: host?.uid + '',
      user_name1: `${currentUser?.first_name} ${currentUser?.last_name || ''}`,
      user_name2: (host as Business)?.name
        ? (host as Business)?.name
        : (host as unknown as User)?.last_name
          ? `${(host as unknown as User)?.first_name} ${(host as unknown as User)?.last_name || ''}`
          : (host as unknown as User)?.first_name || '',
      user_image: currentUser?.photo + '',
      isTechnical: true,
      event: null,
    });

    const chatRef = firestore().collection('chats').doc(chatId);
    const doc = await chatRef.get();
    if (doc.exists) {
      const chatData = doc.data as unknown as ChatType;
      const updatedMessages = chatData.history.map((message: any) => {
        if (!message.readUserIds?.includes(currentUserId)) {
          console.log('Updating message:', message);
          return { ...message, readUserIds: [...(message.readUserIds || []), currentUserId] };
        }
        return message;
      });

      await chatRef.update({ history: updatedMessages });
    }

    // No need to navigate since we're already in the CHAT_STACK
    // The chat will be updated automatically through the chat listeners
  };

  // Move styles useMemo to before conditional returns to fix hooks ordering
  const styles = useMemo(
    () =>
      StyleSheet.create({
        // Modern Event Card Styles
        modernEventCard: {
          backgroundColor: colors.surface,
          borderRadius: borderRadius.xl,
          marginHorizontal: spacing.md,
          marginTop: spacing.sm,
          marginBottom: spacing.xs,
          overflow: 'hidden',
          borderWidth: 1,
          borderColor: colors.border + '30',
          ...shadows.sm,
        },
        eventImageContainer: {
          height: 120,
          position: 'relative',
        },
        eventImage: {
          width: '100%',
          height: '100%',
        },
        eventImageOverlay: {
          ...StyleSheet.absoluteFillObject,
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
        },
        eventTopRow: {
          position: 'absolute',
          top: spacing.sm,
          right: spacing.sm,
          flexDirection: 'row',
          alignItems: 'flex-start',
          gap: spacing.xs,
        },
        eventLikeButton: {
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderRadius: borderRadius.full,
          padding: spacing.xs,
          ...shadows.sm,
        },
        eventTitleOverlay: {
          position: 'absolute',
          bottom: spacing.sm,
          left: spacing.sm,
          right: spacing.sm,
        },
        eventTitle: {
          fontSize: typography.fontSize.lg,
          fontWeight: typography.fontWeight.bold,
          color: colors.white,
          textShadowColor: 'rgba(0, 0, 0, 0.5)',
          textShadowOffset: { width: 0, height: 1 },
          textShadowRadius: 2,
        },
        eventContentContainer: {
          padding: spacing.sm,
          gap: spacing.xs,
        },
        eventDateText: {
          fontSize: typography.fontSize.sm,
          color: colors.textSecondary,
          fontWeight: typography.fontWeight.medium,
        },
        eventLocationText: {
          fontSize: typography.fontSize.sm,
          color: colors.textSecondary,
        },
        eventPriceContainer: {
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginTop: spacing.xs,
        },
        eventPriceText: {
          fontSize: typography.fontSize.base,
          fontWeight: typography.fontWeight.semibold,
          color: colors.primary,
        },
        eventChatBadge: {
          backgroundColor: colors.primary + '20',
          paddingHorizontal: spacing.sm,
          paddingVertical: spacing.xs,
          borderRadius: borderRadius.md,
        },
        eventChatBadgeText: {
          fontSize: typography.fontSize.xs,
          fontWeight: typography.fontWeight.medium,
          color: colors.primary,
        },
      }),
    [colors],
  );

  if (isConfirmationRequired) {
    return (
      <View style={{ flex: 1 }}>
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 20,
            paddingBottom: 10,
            marginTop: Platform.OS === 'android' ? 30 : 50,
          }}
          onPress={defaultCallback}>
          <ChevronIcon />
          <Text style={{ color: colors.primary, fontWeight: '600' }}>{t('generic.back')}</Text>
        </TouchableOpacity>
        <KeyboardGestureArea interpolator="ios" enableSwipeToDismiss style={{ flex: 1 }}>
          <Animated.View style={[scrollViewStyle, { paddingHorizontal: 5 }]} />
        </KeyboardGestureArea>

        <View
          style={{
            padding: 20,
            borderTopWidth: 1,
            borderTopColor: colors.gray400,
            backgroundColor: colors.gray100,
          }}>
          <Text style={{ fontSize: 14, marginBottom: 20, textAlign: 'center' }}>{currentChat?.history[0].message}</Text>

          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <TouchableOpacity
              style={{
                padding: 12,
                borderRadius: 18,
                marginRight: 10,
                flex: 1,
                alignItems: 'center',
                backgroundColor: colors.secondary,
              }}
              onPress={onTechnicalIssueClick}>
              <Text style={{ color: colors.white, fontWeight: '600' }}>{t('technicalissue')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                padding: 12,
                backgroundColor: colors.secondary,
                borderRadius: 18,
                marginLeft: 10,
                flex: 1,
                alignItems: 'center',
              }}
              onPress={onEventIssueClick}>
              <Text style={{ color: colors.white, fontWeight: '600' }}>{t('eventissue')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  // Show loading state while checking acceptance
  if (isAccepted === null) {
    return (
      <View style={{ flex: 1, backgroundColor: colors.background, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: colors.textPrimary }}>Loading...</Text>
      </View>
    );
  }

  // If user is initiator or chat type is contact-pyxi, always show the chat
  if (isInitiator || currentChat?.type === 'contact-pyxi') {
    // Continue to render the chat normally
  } else if (!isAccepted) {
    // Show acceptance UI for non-initiators who haven't accepted yet
    // This will be handled by the conditional render later in the component
  }

  const selectedEvent = currentChat?.event;

  const onEventClick = () => {
    if (selectedEvent) {
      navigation.navigate(SCREENS.HOME_STACK, {
        eventId: selectedEvent.event_id,
      });
    }
  };

  const onHeaderUserClick = () => {
    if (otherUser?.uid) {
      navigation.navigate(SCREENS.PERSONAL_INFO, {
        user: {
          tag: otherUser?.uid + 'image',
          source: otherUser?.photo || '',
          description: otherUser?.description || '',
          name: `${otherUser?.first_name} ${otherUser?.last_name || ''}`,
          user_id: otherUser?.uid || '',
          eventName: '',
        },
      });
    } else if (otherBusinessUser?.uid) {
      navigation.navigate(SCREENS.PERSONAL_INFO, {
        user: {
          tag: otherBusinessUser?.uid + 'image',
          source: otherBusinessUser?.photo || '',
          description: otherBusinessUser?.description || '',
          name: `${otherBusinessUser?.name}`,
          user_id: otherBusinessUser?.uid || '',
          eventName: '',
        },
      });
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <ThemedStatusBar />
      <ModernChatHeader
        userName={userName}
        userImage={image}
        isOnline={false}
        issueNumber={currentChat?.issueNumber?.[0]?.toString()}
        onBackPress={defaultCallback}
        onUserPress={onHeaderUserClick}
        showCallButtons={false}
        showMoreButton={false}
        onMorePress={() => {
          // Add more options functionality here
        }}
      />

      <KeyboardGestureArea interpolator="ios" enableSwipeToDismiss style={{ flex: 1 }}>
        <Animated.View style={[scrollViewStyle, { paddingHorizontal: 5, flex: 1 }]}>
          <AnimatedFlatList
            ref={flatListRef}
            data={combinedMessages}
            renderItem={({ item, index }) => <ChatItem item={item} chatType={currentChat!.type} index={index} />}
            keyExtractor={(item: any, index) => item.id || `key_${index}`}
            showsVerticalScrollIndicator={true}
            estimatedItemSize={100}
            contentContainerStyle={{ paddingBottom: 10 }}
            keyboardShouldPersistTaps="handled"
            ListFooterComponent={() => <ChatTypingIndicator isVisible={isTyping} userName={userName} />}
          />
        </Animated.View>
      </KeyboardGestureArea>

      {currentChat?.status != 'open' && currentChat?.issueNumber && (
        <Text style={{ alignSelf: 'center', marginTop: 10, marginBottom: 10, color: colors.gray400 }}>
          Chat #{currentChat?.issueNumber.length > 0 ? currentChat?.issueNumber[0] : ''} is closed, to reopen it send a
          new message.
        </Text>
      )}

      {isAccepted === false && !isInitiator && currentChat?.type !== 'contact-pyxi' && (
        <View
          style={{
            padding: 20, // Збільшено padding для збільшення висоти блоку
            borderTopWidth: 1,
            borderTopColor: colors.gray400,
            backgroundColor: colors.gray100,
          }}>
          {currentChat?.eventName ? (
            <Text style={{ fontSize: 14, marginBottom: 20, textAlign: 'center' }}>
              {currentChat?.users[1]} wants to chat with you about {currentChat?.eventName}.
            </Text>
          ) : (
            <Text style={{ fontSize: 14, marginBottom: 20, textAlign: 'center' }}>
              {currentChat?.users[1]} wants to chat with you about the {currentChat?.eventName} event.
            </Text>
          )}
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <TouchableOpacity
              style={{
                padding: 12, // Збільшено padding для кнопок
                backgroundColor: colors.white,
                borderRadius: 18,
                marginRight: 10,
                flex: 1,
                alignItems: 'center',
                borderColor: colors.textSecondary,
                borderWidth: 1,
              }}
              onPress={handleReject}>
              <Text style={{ color: colors.textSecondary, fontWeight: '600' }}>{t('decline')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                padding: 12, // Збільшено padding для кнопок
                backgroundColor: colors.secondary,
                borderRadius: 18,
                marginLeft: 10,
                flex: 1,
                alignItems: 'center',
              }}
              onPress={handleAccept}>
              <Text style={{ color: colors.white, fontWeight: '600' }}>{t('accept')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Show OfflineChatInput when conditions are met */}
      {(isAccepted || isInitiator || currentChat?.type == 'contact-pyxi') && (
        <OfflineChatInput
          submit={onSendMessage}
          h={height}
          progress={progress}
          chatId={key}
          userId={currentUserId}
          userName={`${currentUser?.first_name || ''} ${currentUser?.last_name || ''}`.trim()}
          userImage={currentUser?.photo}
          onMessageStatusChange={handleMessageStatusChange}
          onOfflineMessagesSent={handleOfflineMessagesSent}
        />
      )}

      {/* Offline Message Notification */}
      <OfflineMessageNotification
        visible={showOfflineNotification}
        successCount={offlineNotificationData.successCount}
        failureCount={offlineNotificationData.failureCount}
        onHide={() => setShowOfflineNotification(false)}
      />
    </View>
  );
};

export default ChatWithUsers;
