import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import { RouteProp, useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import moment from 'moment-timezone';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform, Text, TouchableOpacity, View } from 'react-native';
import { KeyboardGestureArea, useKeyboardHandler } from 'react-native-keyboard-controller';
import Animated, { useAnimatedStyle, useSharedValue } from 'react-native-reanimated';
import { addDateHeaders } from '~Utils/chat';
import { logScreenView } from '~Utils/firebaseAnalytics';
import { ChevronIcon } from '~assets/icons';
import { ChatItem } from '~components/Chat';
import OfflineChatInput from '~components/Chat/OfflineChatInput/OfflineChatInput';
import NetworkAwareChatWrapper from '~components/Chat/NetworkAwareChatWrapper/NetworkAwareChatWrapper';
import ChatErrorBoundary from '~components/Chat/ChatErrorBoundary/ChatErrorBoundary';
import { SCREENS } from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import FirebaseChatsService from '~services/FirebaseChats';
import { ConciergeChatMessageType } from '~types/chat';
import { RootStackParamsList } from '~types/navigation/navigation.type';
import { useTheme } from '~contexts/ThemeContext';

const useKeyboardAnimationAndroid = () => {
  const { colors } = useTheme();
  const progress = useSharedValue(0);
  const height = useSharedValue(0);
  useKeyboardHandler({
    onMove: e => {
      'worklet';

      progress.value = e.progress;
      height.value = e.height;
    },
    onInteractive: e => {
      'worklet';

      progress.value = e.progress;
      height.value = e.height;
    },
  });

  return { height, progress };
};

const useKeyboardAnimationIos = () => {
  const progress = useSharedValue(0);
  const height = useSharedValue(0);
  const shouldUseOnMoveHandler = useSharedValue(false);
  useKeyboardHandler({
    onStart: e => {
      'worklet';

      // i. e. the keyboard was under interactive gesture, and will be showed
      // again. Since iOS will not schedule layout animation for that we can't
      // simply update `height` to destination and we need to listen to `onMove`
      // handler to have a smooth animation
      if (progress.value !== 1 && progress.value !== 0 && e.height !== 0) {
        shouldUseOnMoveHandler.value = true;
        return;
      }

      progress.value = e.progress;
      height.value = e.height;
    },
    onInteractive: e => {
      'worklet';

      progress.value = e.progress;
      height.value = e.height;
    },
    onMove: e => {
      'worklet';

      if (shouldUseOnMoveHandler.value) {
        progress.value = e.progress;
        height.value = e.height;
      }
    },
    onEnd: e => {
      'worklet';

      height.value = e.height;
      progress.value = e.progress;
      shouldUseOnMoveHandler.value = false;
    },
  });

  return { height, progress };
};

const AnimatedFlatList = Animated.createAnimatedComponent(FlashList);

const useKeyboardAnimation = Platform.OS === 'android' ? useKeyboardAnimationAndroid : useKeyboardAnimationIos;
const ChatWithConcierge = () => {
  const { colors } = useTheme();
  const { params } = useRoute<RouteProp<RootStackParamsList, SCREENS.CONCIERGE_CHAT>>();
  const { userId, userName, key, type } = params.chat;

  const uid = auth().currentUser?.uid;
  const [messages, setMessages] = useState<any>([]);
  const [broadcastMessages, setBroadcastMessages] = useState<ConciergeChatMessageType[]>([]);
  const [oneOnOneMessages, setOneOnOneMessages] = useState<ConciergeChatMessageType[]>([]);
  const [oneOnOneId, setOneOnOneId] = useState('');
  const { setIsTabBarDisabled } = useTabBar();

  const navigation = useNavigation();
  const { t } = useTranslation();

  const defaultCallback = () => {
    navigation.goBack();
  };

  const { height, progress } = useKeyboardAnimation();

  const flatListRef = useRef<any>(null);

  const scrollViewStyle = useAnimatedStyle(
    () => ({
      transform: [{ translateY: -height.value }],
      flex: 1,
    }),
    [],
  );

  useFocusEffect(
    useCallback(() => {
      setIsTabBarDisabled(true);
    }, []),
  );

  useEffect(() => {
    logScreenView('Chat With Concierge', 'ChatWithConcierge');
  }, []);

  useEffect(() => {
    if (uid !== 'wLJLEn8J6oN9RpPyep2BjdnagcA2') {
      const oneOnOneUnsubscribe = firestore()
        .collection('conciergeChat')
        .where('userId', '==', uid)
        .where('type', '==', 'one-on-one')
        .onSnapshot(querySnapshot => {
          querySnapshot.forEach(documentSnapshot => {
            const data = documentSnapshot.data();
            if (data) {
              setOneOnOneId(documentSnapshot.id);
              setOneOnOneMessages(data.messages);
            }
          });
        });

      const broadcastUnsubscribe = firestore()
        .collection('conciergeChat')
        .where('type', '==', 'broadcast')
        .onSnapshot(querySnapshot => {
          querySnapshot.forEach(documentSnapshot => {
            const data = documentSnapshot.data();
            setBroadcastMessages(data.messages);
          });
        });

      return () => {
        oneOnOneUnsubscribe();
        broadcastUnsubscribe();
      };
    }
  }, []);

  useEffect(() => {
    const sortedUserMessages = [...broadcastMessages, ...oneOnOneMessages].sort((a, b) => {
      return moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf();
    });
    setMessages([...sortedUserMessages]);

    // Auto-scroll to bottom when new messages arrive
    if (sortedUserMessages.length > 0) {
      setTimeout(() => {
        flatListRef?.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [broadcastMessages, oneOnOneMessages]);

  const onSendMessage = async (text: any, tempMessageId?: string) => {
    try {
      console.log('ChatWithConcierge: onSendMessage called with:', { text, userId, userName, key, tempMessageId });
      // Scroll to bottom (end of list) since we removed inverted
      flatListRef?.current?.scrollToEnd({ animated: true });
      const messageId = await FirebaseChatsService.pushConciergeMessage({
        user_id: userId,
        text: text,
        user_name: userName,
        chat_id: key,
        temp_message_id: tempMessageId,
      });
      console.log('ChatWithConcierge: Message sent successfully');
      return messageId;
    } catch (error) {
      console.error('ChatWithConcierge: Error sending message:', error);
      throw error; // Re-throw so ChatInput can handle it
    }
  };

  const handleRetry = async () => {
    // Force re-render by clearing and reloading messages
    setMessages([]);
    setBroadcastMessages([]);
    setOneOnOneMessages([]);

    // The useEffect hooks will automatically re-subscribe to Firebase
    console.log('ChatWithConcierge: Retrying chat connection...');
  };

  return (
    <ChatErrorBoundary onRetry={handleRetry}>
      <NetworkAwareChatWrapper onRetry={handleRetry}>
        <View style={{ justifyContent: 'flex-end', flex: 1 }}>
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 20,
              paddingBottom: 10,
              marginTop: Platform.OS === 'android' ? 30 : 50,
            }}
            onPress={defaultCallback}>
            <ChevronIcon />
            <Text style={{ color: colors.primary, fontWeight: '600' }}>{t('generic.back')}</Text>
          </TouchableOpacity>
          <KeyboardGestureArea interpolator="ios" enableSwipeToDismiss style={{ flex: 1 }}>
            <Animated.View style={[scrollViewStyle, { paddingHorizontal: 5 }]}>
              <AnimatedFlatList
                ref={flatListRef}
                data={addDateHeaders(messages)}
                renderItem={({ item, index }: any) => <ChatItem item={item} key={index} />}
                keyExtractor={(item, index) => 'key' + index}
                showsVerticalScrollIndicator={true}
                estimatedItemSize={100}
              />
            </Animated.View>
          </KeyboardGestureArea>

          <OfflineChatInput
            submit={text => onSendMessage(text)}
            h={height}
            progress={progress}
            chatId={key}
            userId={userId}
            userName={userName}
            onMessageStatusChange={(tempId, status) => {
              console.log('Message status changed:', tempId, status);
              // Handle message status updates here
            }}
          />
        </View>
      </NetworkAwareChatWrapper>
    </ChatErrorBoundary>
  );
};

export default ChatWithConcierge;
