import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import { useNavigation } from '@react-navigation/native';
import NetInfo from '@react-native-community/netinfo';
import moment from 'moment';
import React, { useEffect, useLayoutEffect, useMemo, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Text, TouchableOpacity, View, ActivityIndicator } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getTimestamp } from '~Utils/Time';
import { getLastMessage } from '~Utils/chat';
import { logScreenView } from '~Utils/firebaseAnalytics';
import { ChevronIcon, ChatIcon, PlusIcon, WifiOffIcon } from '~assets/icons';
import Button from '~components/Button';
import { ModernChatList, ModernChatHeader } from '~components/Chat';
import { ThemedStatusBar } from '~components/ThemedStatusBar/ThemedStatusBar';
import { SCREENS } from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import { useGetBusinessAccount } from '~hooks/business/useGetBusinessAccount';
import { useGetUserAccount } from '~hooks/user/useGetUser';
import FirebaseChatsService from '~services/FirebaseChats';
import OfflineChatStorage from '~services/OfflineChatStorage/OfflineChatStorage';
import {
  ChatType,
  ChatTypeWithKey,
  ConciergeChatMessageType,
  ConciergeChatType,
  ConciergeChatTypeWithKey,
} from '~types/chat';
import { NavigationProps } from '~types/navigation/navigation.type';
import { useTheme } from '~contexts/ThemeContext';
import { spacing, borderRadius, shadows, typography } from '~constants/design';
import { haptics } from '~Utils/haptics';
import { FadeIn } from '~components/MicroInteractions/MicroInteractions';

export default function Chat() {
  const { colors } = useTheme();

  const uid = auth().currentUser?.uid;
  const [chats, setChats] = useState<ChatTypeWithKey[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [hasOfflineData, setHasOfflineData] = useState(false);

  // Debug logging
  console.log('🔍 ChatView Debug:', {
    uid,
    isAuthenticated: !!auth().currentUser,
    chatsCount: chats.length,
    isLoading,
    isOnline,
    hasOfflineData,
  });

  const [sortedByTypeChats, setSortedByTypeChats] = useState<(ConciergeChatTypeWithKey | ChatTypeWithKey)[]>([]);
  const [chatWithConcierge, setChatWithConcierge] = useState<ConciergeChatTypeWithKey>();
  const [broadcastMessages, setBroadcastMessages] = useState([]);
  const [oneToOneMessages, setOneToOneMessages] = useState<ConciergeChatMessageType[]>([]);
  const [contactChat, setContactChat] = useState<ChatTypeWithKey | undefined>();
  const navigation = useNavigation<NavigationProps>();
  const { setIsTabBarDisabled } = useTabBar();
  const { t } = useTranslation();

  const { data: userAccount } = useGetUserAccount(auth().currentUser?.uid);
  const { data: hostBusiness } = useGetBusinessAccount('wLJLEn8J6oN9RpPyep2BjdnagcA2');

  useLayoutEffect(() => {
    navigation.addListener('focus', () => {
      setIsTabBarDisabled(false);
    });
  }, []);

  // Monitor network connectivity
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const isConnected = !!(state.isConnected && state.isInternetReachable);
      setIsOnline(isConnected);

      if (!isConnected) {
        // Load offline data when going offline
        loadOfflineChats();
      }
    });

    return unsubscribe;
  }, []);

  // Check for offline data on mount and load if available
  useEffect(() => {
    const initializeOfflineData = async () => {
      await checkOfflineData();
      // Always try to load offline chats first for immediate display
      await loadOfflineChats();

      // If no offline chats found and we're online, ensure loading continues
      // The Firestore subscription will handle setting isLoading to false
    };

    initializeOfflineData();
  }, []);

  const checkOfflineData = async () => {
    try {
      const hasData = await OfflineChatStorage.hasOfflineData();
      setHasOfflineData(hasData);
    } catch (error) {
      console.error('Error checking offline data:', error);
    }
  };

  const loadOfflineChats = async () => {
    try {
      const offlineChats = await OfflineChatStorage.getStoredChats();
      console.log('📱 Attempting to load offline chats:', offlineChats.length);

      if (offlineChats.length > 0) {
        console.log('📱 Loading offline chats:', offlineChats.length);

        // Separate regular chats from concierge chats
        const regularChats = offlineChats.filter(chat => 'history' in chat) as ChatTypeWithKey[];
        const conciergeChats = offlineChats.filter(chat => 'messages' in chat) as ConciergeChatTypeWithKey[];

        console.log('📱 Regular chats:', regularChats.length, 'Concierge chats:', conciergeChats.length);

        // Only update if we have regular chats or if current chats are empty
        if (regularChats.length > 0 || chats.length === 0) {
          setChats(regularChats);
        }

        if (conciergeChats.length > 0) {
          setChatWithConcierge(conciergeChats[0]);
        }

        setHasOfflineData(true);
        // Only set loading to false if we're offline, otherwise let Firestore handle it
        if (!isOnline) {
          setIsLoading(false);
        }
      } else {
        console.log('📱 No offline chats found');
        setHasOfflineData(false);
        // If we're offline and have no cached data, stop loading
        if (!isOnline) {
          setIsLoading(false);
        }
        // If we're online, let the Firestore subscription handle loading state
      }
    } catch (error) {
      console.error('Error loading offline chats:', error);
      setHasOfflineData(false);
    }
  };

  const storeChatsOffline = async (chatsToStore: (ChatTypeWithKey | ConciergeChatTypeWithKey)[]) => {
    try {
      await OfflineChatStorage.storeChats(chatsToStore);
    } catch (error) {
      console.error('Error storing chats offline:', error);
    }
  };

  useEffect(() => {
    if (uid && uid !== 'wLJLEn8J6oN9RpPyep2BjdnagcA2' && hostBusiness?.uid && userAccount?.uid) {
      FirebaseChatsService.createContactUsChat({
        user_id1: auth().currentUser!.uid + '',
        user_id2: hostBusiness?.uid + '',
        user_name1: `${userAccount?.first_name} ${userAccount?.last_name || ''}`,
        user_name2: hostBusiness?.name || 'Pyxi',
      });
    }
  }, [uid, hostBusiness, userAccount]);

  useEffect(() => {
    if (uid && uid !== 'wLJLEn8J6oN9RpPyep2BjdnagcA2') {
      // Only set loading to true if we don't have offline data showing
      if (!hasOfflineData) {
        setIsLoading(true);
      }

      const unsubscribe = firestore()
        .collection('chats')
        .where('userIds', 'array-contains', uid)
        .onSnapshot(
          querySnapshot => {
            try {
              const conversations: ChatTypeWithKey[] = [];
              querySnapshot.forEach(documentSnapshot => {
                const data = documentSnapshot.data() as ChatType;
                conversations.push({
                  ...data,
                  key: documentSnapshot.id,
                });
              });

              // Sort chats by timestamp of the last message (optimized)
              const sortedConversations = conversations.sort((a, b) => {
                const timestampA = getTimestamp(a.history);
                const timestampB = getTimestamp(b.history);
                return timestampB - timestampA; // For descending order
              });

              // Find support chat more efficiently
              const supportChat = sortedConversations.find(chat => chat.type === 'contact-pyxi');
              if (supportChat) {
                console.log('Support chat found:', supportChat.key);
                setContactChat(supportChat);
              }

              setChats(sortedConversations);
              setIsLoading(false);

              // Store chats offline when online
              if (isOnline && sortedConversations.length > 0) {
                storeChatsOffline(sortedConversations);
              }

              console.log('🔥 Online chats loaded:', sortedConversations.length);
            } catch (error) {
              console.error('Error processing chat snapshot:', error);
              setIsLoading(false);
            }
          },
          error => {
            console.error('Chat subscription error:', error);
            setIsLoading(false);
          },
        );

      // Clean up subscription on unmount
      return () => unsubscribe();
    }
  }, [uid, hasOfflineData, isOnline]);

  useEffect(() => {
    logScreenView('Chat', 'ChatView');
  }, []);

  useEffect(() => {
    const oneOnOneUnsubscribe = firestore()
      .collection('conciergeChat')
      .where('userId', '==', uid)
      .where('type', '==', 'one-on-one')
      .onSnapshot(querySnapshot => {
        querySnapshot.forEach(documentSnapshot => {
          const data = documentSnapshot.data() as ConciergeChatType;
          if (data) {
            const sortedMessages = [...broadcastMessages, ...data.messages].sort((a, b) => {
              return moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf();
            });
            const conciergeChat = {
              ...data,
              key: documentSnapshot.id,
              messages: [...sortedMessages],
            };
            setChatWithConcierge(conciergeChat);
            setOneToOneMessages(data.messages);

            // Store concierge chat offline when online
            if (isOnline) {
              storeChatsOffline([conciergeChat]);
            }
          }
        });
      });

    const broadcastUnsubscribe = firestore()
      .collection('conciergeChat')
      .where('type', '==', 'broadcast')
      .onSnapshot(querySnapshot => {
        querySnapshot.forEach(documentSnapshot => {
          const data = documentSnapshot.data();
          setBroadcastMessages(data.messages);
        });
      });

    return () => {
      oneOnOneUnsubscribe();
      broadcastUnsubscribe();
    };
  }, []);

  useEffect(() => {
    const sortedMessages = [...oneToOneMessages, ...broadcastMessages].sort((a, b) => {
      return moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf();
    });
    setChatWithConcierge(prev => {
      if (prev) {
        return { ...prev, messages: [...sortedMessages] };
      }
    });
  }, [oneToOneMessages, broadcastMessages]);

  useEffect(() => {
    // Show all chats (no filtering by type since we removed tabs)
    let allChats = [...chats];

    // Add concierge chat if it exists
    if (chatWithConcierge) {
      allChats = [chatWithConcierge, ...allChats];
    }

    // Sort by timestamp (most recent first)
    const sortedChats = allChats.sort((a, b) => {
      const timestampA = getTimestamp(a.history);
      const timestampB = getTimestamp(b.history);
      return timestampB - timestampA;
    });

    setSortedByTypeChats(sortedChats);
  }, [chats, chatWithConcierge, uid]);

  const handleRefreshChats = useCallback(async () => {
    setIsRefreshing(true);
    haptics.light();

    try {
      // Force refresh by briefly clearing and reloading data
      // The Firestore listener will automatically update with fresh data
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for UX
    } catch (error) {
      console.error('Error refreshing chats:', error);
      haptics.error();
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  const modernChatsList = useMemo(() => {
    if (isLoading) {
      return (
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingTop: spacing.xl,
          }}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text
            style={{
              marginTop: spacing.md,
              fontSize: typography.fontSize.base,
              color: colors.textSecondary,
              fontWeight: typography.fontWeight.medium,
            }}>
            {t('chat.loading_conversations')}
          </Text>
        </View>
      );
    }

    return (
      <ModernChatList
        chats={sortedByTypeChats}
        onRefresh={handleRefreshChats}
        // isLoading={isRefreshing} // Remove this prop as it's not used in ModernChatList
        emptyStateTitle={t('chat.no_conversations')}
        emptyStateSubtitle={t('chat.start_conversation')}
      />
    );
  }, [sortedByTypeChats, handleRefreshChats, isLoading, isRefreshing, colors, t]);

  const handleChatWithPyxi = () => {
    if (contactChat) {
      const interlocutorId = contactChat?.userIds?.find(userId => userId !== uid);
      const interlocutorMessage = contactChat?.history?.find(message => message.sender_id === interlocutorId);
      const interlocutorImage = interlocutorMessage?.sender_image;

      const image = contactChat?.eventImage || interlocutorImage;
      const userIndex = contactChat?.userIds?.findIndex(user => user.toLowerCase() !== (uid || '').toLowerCase()) || 0;
      const userName = contactChat?.users?.[userIndex];

      navigation.navigate(SCREENS.USER_CHAT, { chatId: contactChat?.key, image: image, userName: userName });
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <ThemedStatusBar />

      {/* Modern Header */}
      <ModernChatHeader userName={t('chat.conversations')} showCallButtons={false} showMoreButton={false} />

      {/* Offline indicator */}
      {!isOnline && (
        <View
          style={{
            backgroundColor: colors.warning,
            paddingHorizontal: spacing.md,
            paddingVertical: spacing.xs,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <WifiOffIcon width={16} height={16} color={colors.white} style={{ marginRight: spacing.xs }} />
          <Text
            style={{
              color: colors.white,
              fontSize: 12,
              fontWeight: '500',
            }}>
            {hasOfflineData
              ? t('chat.offline_viewing_cached') || 'Offline - Viewing cached conversations'
              : t('chat.offline_no_data') || 'Offline - No cached data available'}
          </Text>
        </View>
      )}

      {/* Support Chat Quick Access */}
      {contactChat && (
        <FadeIn delay={100} duration={300}>
          <View
            style={{
              marginHorizontal: spacing.md,
              marginTop: spacing.md,
              marginBottom: spacing.sm,
            }}>
            <TouchableOpacity
              style={{
                backgroundColor: colors.primary,
                borderRadius: borderRadius.xl,
                padding: spacing.md,
                flexDirection: 'row',
                alignItems: 'center',
                ...shadows.md,
              }}
              onPress={handleChatWithPyxi}
              activeOpacity={0.8}>
              <View
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: borderRadius.full,
                  backgroundColor: colors.white,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: spacing.md,
                }}>
                <ChatIcon color={colors.primary} />
              </View>

              <View style={{ flex: 1 }}>
                <Text
                  style={{
                    fontSize: typography.fontSize.base,
                    fontWeight: typography.fontWeight.semibold,
                    color: colors.white,
                    marginBottom: 2,
                  }}>
                  {t('chat.support_chat')}
                </Text>
                <Text
                  style={{
                    fontSize: typography.fontSize.sm,
                    color: colors.white,
                    opacity: 0.9,
                  }}>
                  {t('chat.get_help_instantly')}
                </Text>
              </View>

              <ChevronIcon color={colors.white} style={{ transform: [{ rotate: '180deg' }] }} />
            </TouchableOpacity>
          </View>
        </FadeIn>
      )}

      {/* Modern Chat List */}
      {modernChatsList}
    </View>
  );
}
