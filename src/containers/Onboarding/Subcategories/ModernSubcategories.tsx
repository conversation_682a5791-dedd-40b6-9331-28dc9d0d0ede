import auth from '@react-native-firebase/auth';
import storage from '@react-native-firebase/storage';
import {BASE_API_URL, NODE_ENV, GOOGLE_API_KEY} from '@env';
import {RouteProp, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import React, {useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, Platform, Text, View, TextInput, Modal, TouchableOpacity} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {organizeByCategory} from '~Utils/filterSubcategories';
import {logScreenView} from '~Utils/firebaseAnalytics';
import ModernButton from '~components/ModernButton';
import SubcategoriesLists from '~components/SubcategoriesLists';
import {SCREENS} from '~constants';
import {useGetCategories} from '~hooks/subcategories/useGetCategories';
import {useGetSubcategories} from '~hooks/subcategories/useGetSubcategories';
import {useCreateUser} from '~hooks/user/useCreateUser';
import {useUpdateUserGroups} from '~hooks/user/useUpdateUserGroups';
import {useUpdateUserSubcategories} from '~hooks/user/useUpdateUserSubcategories';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import {SubCategoryType} from '~types/categories';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {useTheme} from '~contexts/ThemeContext';
import ModernOnboardingHeader from '~components/ModernOnboarding/ModernOnboardingHeader';
import Animated, {FadeIn} from 'react-native-reanimated';
import {spacing, typography, borderRadius} from '~constants/design';
import * as haptics from '~Utils/haptics';
import {ModernCloseIcon} from '~assets/icons';

const ModernSubcategories = () => {
  const {colors} = useTheme();
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.ONBOARDING_SUBCATEGORIES>>();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const {mutateAsync} = useCreateUser();
  const {
    personalOnboarding,
    subcategories: onboardingSelectedSubCategories,
    setPersonalSubCategories,
  } = useOnboardingStore();
  const {data} = useGetSubcategories();
  const {data: d} = useGetCategories();
  const {user} = useUserStore();
  const {mutateAsync: updateUserSubcategories} = useUpdateUserSubcategories();
  const {mutateAsync: updateUserGroups} = useUpdateUserGroups();
  const {navigate} = useNavigation<NavigationProps>();
  const isBusiness = params?.isBusiness;

  const [categories, setCategories] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [selectedSubcategories, setSelectedSubcategories] = useState<number[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showNoInterestsModal, setShowNoInterestsModal] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  // Initialize selected subcategories from onboarding store
  useEffect(() => {
    if (onboardingSelectedSubCategories) {
      setSelectedSubcategories(onboardingSelectedSubCategories);
    }
  }, [onboardingSelectedSubCategories]);

  useEffect(() => {
    logScreenView('Categories', 'CategoryScrollView');

    // Critical Config validation - FAIL FAST if missing
    console.log('ModernSubcategories - Environment check:', {
      BASE_API_URL,
      NODE_ENV,
      GOOGLE_API_KEY: GOOGLE_API_KEY ? 'Present' : 'Missing',
      hasBaseUrl: !!BASE_API_URL,
    });

    // CRITICAL: Fail fast if BASE_API_URL is missing
    if (!BASE_API_URL) {
      console.error('🚨 CRITICAL ERROR: BASE_API_URL is undefined!');
      console.error('🚨 This will cause network requests to fail!');
      console.error('🚨 Check your .env file and restart Metro with --reset-cache!');
      console.error('🚨 Current values:', {BASE_API_URL, NODE_ENV, GOOGLE_API_KEY: !!GOOGLE_API_KEY});

      // Show user-friendly error
      Alert.alert('Configuration Error', 'App configuration is missing. Please restart the app or contact support.', [
        {text: 'OK'},
      ]);
      return;
    }
  }, []);

  const submit = async () => {
    console.log('Submit called with:', {
      selectedSubcategories: selectedSubcategories.length,
      isBusiness,
      hasUser: !!user,
    });

    setIsLoading(true);

    try {
      // Handle business users differently
      if (isBusiness) {
        console.log('Business user - navigating to groups');
        setIsLoading(false);
        navigate(SCREENS.ONBOARDING_GROUP, {isBusiness: true});
        return;
      }

      if (user) {
        console.log('Existing user - updating data');
        await updateUserGroups({ids: personalOnboarding.groups});
        await updateUserSubcategories({ids: selectedSubcategories});
        setIsLoading(false);
        console.log('Navigating to preferences');
        navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
        return;
      }

      console.log('New user - creating account');
      if (personalOnboarding.photo[0] != 'h') {
        await storage().ref('users/' + auth().currentUser!.uid + '/profile.png').putFile(personalOnboarding.photo);
      }

      const urlPhoto =
        personalOnboarding.photo[0] == 'h'
          ? auth().currentUser?.photoURL
          : await storage().ref('users/' + auth().currentUser!.uid + '/profile.png').getDownloadURL();

      await mutateAsync({
        ...personalOnboarding,
        email: auth().currentUser!.email!,
        photo: urlPhoto || '',
        uid: auth().currentUser!.uid,
        is_registration_finished: false,
        subcategories: selectedSubcategories,
        coords_real: null,
        postal_code: '',
        onboarding_answers: [],
      });

      setIsLoading(false);
      console.log('Account created - navigating to preferences');
      navigate(SCREENS.EDIT_PREFERANCE, {setting: false});
    } catch (E) {
      setIsLoading(false);
      console.log('Submit error:', E);
    }
  };

  const handleSearchCategory = useCallback(() => {
    if (!data || !d) return;

    if (searchValue?.trim().length > 0) {
      const inputData = data.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchValue.trim().toLowerCase()) !== -1,
      );
      const result = organizeByCategory([], d, inputData);
      setCategories(result);
    } else {
      const result = organizeByCategory([], d, data);
      setCategories(result);
    }
  }, [searchValue, data, d]);

  useEffect(() => {
    handleSearchCategory();
  }, [handleSearchCategory]);

  const handleSearch = () => {
    try {
      haptics.light();
    } catch (error) {
      console.log('Haptic feedback not available:', error);
    }
    handleSearchCategory();
  };

  const handleBack = () => {
    try {
      haptics.light();
    } catch (error) {
      console.log('Haptic feedback not available:', error);
    }
    navigate(SCREENS.ONBOARDING_GROUP);
  };

  const onSubmitPress = () => {
    console.log('Submit button pressed:', {
      selectedCount: selectedSubcategories.length,
      hasSelections: selectedSubcategories.length > 0,
    });

    try {
      haptics.success();
    } catch (error) {
      console.log('Haptic feedback not available:', error);
    }

    if (selectedSubcategories.length > 0) {
      console.log('Has selections - calling submit()');
      submit();
    } else {
      console.log('No selections - showing modal');
      setShowNoInterestsModal(true);
    }
  };

  const handleInterestToggle = (subcategoryId: number) => {
    const newSelectedSubcategories = selectedSubcategories.includes(subcategoryId)
      ? selectedSubcategories.filter(item => item !== subcategoryId)
      : [...selectedSubcategories, subcategoryId];

    setSelectedSubcategories(newSelectedSubcategories);
    // Also update the onboarding store (it expects a single number and handles the toggle internally)
    setPersonalSubCategories(subcategoryId);
  };

  const isButtonDisabled = selectedSubcategories.length === 0;

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernOnboardingHeader
        title={t('signin.tell_us_what_you_like') || 'Tell us what you like'}
        subtitle="Select your interests to get personalized event recommendations"
        step={isBusiness ? 2 : 3}
        totalSteps={isBusiness ? 2 : 6}
        onBackPress={handleBack}
        showSkip={false}
      />

      {/* Search Section */}
      <Animated.View entering={FadeIn.delay(100).duration(600)}>
        <View style={{paddingHorizontal: spacing.md, marginBottom: spacing.sm}}>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: spacing.sm}}>
            <TextInput
              placeholder={t('settings.search_interests') || 'Search interests...'}
              value={searchValue}
              onChangeText={setSearchValue}
              style={{
                flex: 1,
                height: 48,
                borderWidth: 1,
                borderColor: colors.border,
                borderRadius: borderRadius.md,
                paddingHorizontal: spacing.md,
                fontSize: typography.fontSize.base,
                color: colors.textPrimary,
                backgroundColor: colors.surface,
              }}
              placeholderTextColor={colors.textSecondary}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
            <ModernButton
              title={t('generic.search') || 'Search'}
              onPress={handleSearch}
              variant="secondary"
              size="lg"
              style={{minWidth: 80}}
              hapticType="light"
            />
          </View>
        </View>
      </Animated.View>

      {/* Selected Count */}
      {selectedSubcategories.length > 0 && (
        <Animated.View entering={FadeIn.delay(200).duration(500)}>
          <View style={{paddingHorizontal: spacing.md}}>
            <View
              style={{
                backgroundColor: colors.primary + '15',
                borderRadius: borderRadius.md,
                paddingVertical: spacing.sm,
                paddingHorizontal: spacing.md,
                alignItems: 'center',
              }}>
              <Text
                style={{
                  fontSize: typography.fontSize.sm,
                  fontWeight: typography.fontWeight.semibold,
                  color: colors.primary,
                }}>
                {selectedSubcategories.length} {t('settings.interests_selected') || 'interests selected'}
              </Text>
            </View>
          </View>
        </Animated.View>
      )}

      {/* Categories List */}
      <Animated.View entering={FadeIn.delay(300).duration(500)} style={{flex: 1}}>
        <View style={{flex: 1}}>
          {categories && d ? (
            <SubcategoriesLists
              searchValue={searchValue}
              categoriesList={d}
              filteredSubcategoriesData={categories}
              selectedCategories={selectedSubcategories}
              onCategoryChange={handleInterestToggle}
              isFromSettings={false}
            />
          ) : (
            <View style={{alignItems: 'center', justifyContent: 'center', flex: 1}}>
              <Text
                style={{
                  fontSize: typography.fontSize.lg,
                  color: colors.textSecondary,
                }}>
                {t('generic.loading') || 'Loading interests...'}
              </Text>
            </View>
          )}
        </View>
      </Animated.View>

      {/* Footer with Continue Button */}
      <Animated.View entering={FadeIn.delay(400).duration(500)}>
        <View
          style={{
            paddingHorizontal: spacing.md,
            // paddingTop: spacing.md,
            paddingBottom: Platform.OS === 'android' ? spacing.lg : bottom,
            backgroundColor: colors.background,
            borderTopWidth: 1,
            borderTopColor: colors.border + '20',
          }}>
          <ModernButton
            title={
              isButtonDisabled ? t('signin.i_will_do_later') || 'I will do later' : t('generic.submit') || 'Submit'
            }
            loading={isLoading}
            onPress={onSubmitPress}
            variant="primary"
            size="lg"
            fullWidth
            hapticType="success"
          />

          {/* Progress Dots */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              marginTop: spacing.lg,
            }}>
            {[...Array(isBusiness ? 2 : 6)].map((_, index) => (
              <View
                key={index}
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: index === (isBusiness ? 1 : 2) ? colors.primary : colors.border,
                  marginHorizontal: spacing.xs,
                }}
              />
            ))}
          </View>
        </View>
      </Animated.View>

      {/* Custom No Interests Modal */}
      <Modal
        visible={showNoInterestsModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowNoInterestsModal(false)}>
        <View
          style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={{
              backgroundColor: colors.surface,
              borderRadius: 12,
              padding: 20,
              margin: 20,
              minWidth: 280,
              maxWidth: 320,
              position: 'relative',
            }}>
            {/* Modern close button at top right */}
            <TouchableOpacity
              style={{
                position: 'absolute',
                top: 12,
                right: 12,
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: colors.gray100,
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 1,
              }}
              onPress={() => setShowNoInterestsModal(false)}>
              <ModernCloseIcon size={20} color={colors.gray600} variant="minimal" />
            </TouchableOpacity>

            {/* Modal content */}
            <View style={{paddingTop: 20, alignItems: 'center'}}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  textAlign: 'center',
                  marginBottom: 12,
                  color: colors.textPrimary,
                }}>
                {t('onboarding.no_interests_title') || 'No interests selected'}
              </Text>

              <Text
                style={{
                  fontSize: 14,
                  textAlign: 'center',
                  marginBottom: 20,
                  color: colors.textSecondary,
                  lineHeight: 20,
                }}>
                {t('onboarding.no_interests_message') || 'You can modify your selections later in settings'}
              </Text>

              <TouchableOpacity
                style={{
                  backgroundColor: colors.primary,
                  paddingHorizontal: 20,
                  paddingVertical: 12,
                  borderRadius: 8,
                  minWidth: 120,
                }}
                onPress={() => {
                  setShowNoInterestsModal(false);
                  submit();
                }}>
                <Text
                  style={{
                    color: colors.white,
                    fontSize: 16,
                    fontWeight: '600',
                    textAlign: 'center',
                  }}>
                  {t('generic.ok') || 'OK'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ModernSubcategories;
