import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import axios from 'axios';
import {Formik} from 'formik';
import moment from 'moment-timezone';
import {useCallback, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, KeyboardAvoidingView, Platform, ScrollView, Text, View, TouchableOpacity} from 'react-native';
import {BASE_API_URL} from '@env';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import * as yup from 'yup';
import {BookIcon, DropdownArrowIcon, CalendarIcon, UserIcon, LocationIcon} from '~assets/icons';
import ModernButton from '~components/ModernButton';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernTextInput from '~components/ModernTextInput/ModernTextInput';
import ModernOnboardingHeader from '~components/ModernOnboarding/ModernOnboardingHeader';
import DateTimeModal from '~components/DateTimeModal';
import LocationModal from '~components/LocationModal';
import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
import {ModalWithGenders} from '~components/ModalWithItems';
import {ProfileImageComponent} from '~components/Settings';
import {SCREENS} from '~constants';
import {spacing, typography, borderRadius} from '~constants/design';
import {useMapsContext} from '~providers/maps/zustand';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';
import {NavigationProps} from '~types/navigation/navigation.type';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';
import {haptics} from '~Utils/haptics';
import Animated, {FadeInDown, SlideInUp} from 'react-native-reanimated';
import {SlideIn, FadeIn} from '~components/MicroInteractions/MicroInteractions';
import {CustomTextInput} from '~components/CustomTextInput';
import CustomBox from '~components/CustomBox';
import Button from '~components/Button';
import ProgressDots from '~components/ProgressDots';

const validationSchema = yup.object().shape({
  first_name: yup
    .string()
    .transform(value => value.trim())
    .matches(/^[a-zA-Z]+(?: [a-zA-Z]+)?$/, 'onboarding.first_name_error')
    .required('onboarding.first_name_error'),
  last_name: yup.string().optional(),
  date_of_birth: yup.string().required('onboarding.dob_required'),
  gender: yup.string().required('onboarding.selection_error'),
  description: yup.string().optional(),
  photo: yup.string().required('onboarding.image_required'),
});

// Modern Field Component for better UX
interface ModernFieldProps {
  label: string;
  value: string;
  onPress: () => void;
  icon?: React.ReactNode;
  errorText?: string;
  placeholder?: string;
  required?: boolean;
}

const ModernField: React.FC<ModernFieldProps> = ({
  label,
  value,
  onPress,
  icon,
  errorText,
  placeholder,
  required = false,
}) => {
  const {colors} = useTheme();

  const handlePress = () => {
    haptics.light();
    onPress();
  };

  return (
    <View style={{marginBottom: spacing.lg}}>
      <Text
        style={{
          fontSize: typography.fontSize.sm,
          fontWeight: typography.fontWeight.medium,
          color: colors.textPrimary,
          marginBottom: spacing.xs,
        }}>
        {label} {required && <Text style={{color: colors.error}}>*</Text>}
      </Text>
      <TouchableOpacity
        onPress={handlePress}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: spacing.lg,
          paddingVertical: spacing.md,
          borderRadius: borderRadius.lg,
          borderWidth: 1,
          borderColor: errorText ? colors.error : colors.border + '40',
          backgroundColor: colors.surface,
          minHeight: 56,
        }}
        activeOpacity={0.7}>
        {icon && <View style={{marginRight: spacing.md}}>{icon}</View>}
        <Text
          style={{
            flex: 1,
            fontSize: typography.fontSize.base,
            color: value ? colors.textPrimary : colors.textSecondary,
            fontWeight: value ? typography.fontWeight.medium : typography.fontWeight.normal,
          }}>
          {value || placeholder}
        </Text>
        <DropdownArrowIcon color={colors.textSecondary} />
      </TouchableOpacity>
      {errorText && (
        <Text
          style={{
            fontSize: typography.fontSize.sm,
            color: colors.error,
            marginTop: spacing.xs,
            marginLeft: spacing.sm,
          }}>
          {errorText}
        </Text>
      )}
    </View>
  );
};

export default function PersonalInfo() {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();

  const [isDatePickerModalVisible, setIsDatePickerModalVisible] = useState(false);
  const [birthdayDate, setBirthdayDate] = useState<moment.Moment>(moment());
  const [modalIsVisible, setModalIsVisible] = useState(false);
  const [locationModalIsVisible, setLocationModalIsVisible] = useState(false);
  const {currentPositionState} = useMapsContext();
  const [locationError, setLocationError] = useState('');
  const {personalOnboarding, setPersonalOnboardingProfileInfo} = useOnboardingStore();
  const scrollViewRef = useRef<ScrollView>(null);
  const [joinedGroupId, setJoinGroupId] = useState('');

  // Field refs for smart scrolling
  const fieldRefs = useRef<{[key: string]: View | null}>({});

  const {bottom} = useSafeAreaInsets();

  const [isLoading, setIsLoading] = useState(false);
  const [isJoinLoading, setIsJoinLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Personal Info', 'PersonalInfo');
  }, []);

  const {getCurrentPosition} = useGetCurrentPosition();

  const joinGroup = async (invitationCode: string) => {
    try {
      setIsJoinLoading(true);
      const token = await FirebaseAuth.getAuthToken();
      const config = {
        headers: {Authorization: token, Accept: 'application/json'},
      };
      const response = await axios.post(`${BASE_API_URL}community/join`, {invitation_code: invitationCode}, config);
      if (response.data.community_uuid) {
        setJoinGroupId(response.data.community_uuid);
      }
      Notifier.showNotification({
        title: t('settings.join_success'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
    } catch (error) {
      Notifier.showNotification({
        title: t('settings.join_error'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'error',
        },
      });
    } finally {
      setIsJoinLoading(false);
    }
  };

  const leaveGroup = async (groupId: string) => {
    try {
      setIsJoinLoading(true);
      const token = await FirebaseAuth.getAuthToken();
      const config = {
        headers: {Authorization: token, Accept: 'application/json'},
      };
      await axios.delete(BASE_API_URL + `community/leave/${groupId}`, config);
      setJoinGroupId('');
      Notifier.showNotification({
        title: t('settings.leave_success'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
    } catch (error) {
      Notifier.showNotification({
        title: t('settings.leave_error'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'error',
        },
      });
    } finally {
      setIsJoinLoading(false);
    }
  };

  const handleChooseBirthdayDate = useCallback((value?: Date) => {
    if (value) {
      setBirthdayDate(moment(value));
      return;
    }
  }, []);

  const handleOpenDatePicker = useCallback(() => {
    setIsDatePickerModalVisible(true);
  }, []);

  const handleCloseDatePicker = useCallback(() => {
    setIsDatePickerModalVisible(false);
  }, []);

  // Smart scroll to error field function
  const scrollToErrorField = (errors: any) => {
    const errorFields = Object.keys(errors);
    if (errorFields.length === 0) return;

    const firstErrorField = errorFields[0];
    const fieldRef = fieldRefs.current[firstErrorField];

    if (fieldRef && scrollViewRef.current) {
      fieldRef.measureLayout(
        scrollViewRef.current as any,
        (x, y) => {
          scrollViewRef.current?.scrollTo({
            y: Math.max(0, y - 100), // Offset to show field clearly
            animated: true,
          });
        },
        () => {
          // Fallback to scroll to end if measurement fails
          scrollViewRef.current?.scrollToEnd({animated: true});
        },
      );
    }
  };

  const handleBack = () => {
    haptics.light();
    navigation.goBack();
  };

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernOnboardingHeader
        title={t('onboarding.personal_info') || 'Personal Information'}
        subtitle={t('onboarding.personal_info_subtitle') || 'Tell us a bit about yourself to get started'}
        step={2}
        totalSteps={5}
        onBackPress={handleBack}
        showSkip={false}
      />

      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{flex: 1}}>
        <Formik
          initialValues={{
            first_name: personalOnboarding.first_name || auth().currentUser?.displayName?.split(' ')[0] || '',
            last_name: personalOnboarding.last_name || auth().currentUser?.displayName?.split(' ')[1] || '',
            date_of_birth: personalOnboarding.date_of_birth,
            gender: personalOnboarding.gender,
            description: personalOnboarding.description,
            photo: personalOnboarding.photo || auth().currentUser?.photoURL,
            group_invite_code: '',
          }}
          onSubmit={(values, actions) => {
            setIsLoading(true);
            if (!currentPositionState?.address) {
              setIsLoading(false);
              setLocationError(t('onboarding.location_error'));
              return;
            }

            const infoObject = {
              first_name: values.first_name,
              last_name: values.last_name,
              date_of_birth: values.date_of_birth,
              description: values.description,
              gender: values.gender,
              coords: {
                lat: currentPositionState?.latitude,
                long: currentPositionState?.longitude,
              },
              photo: values.photo,
            };
            setPersonalOnboardingProfileInfo(infoObject);
            navigation.navigate(SCREENS.ONBOARDING_GROUP);
          }}
          validationSchema={validationSchema}>
          {formikProps => (
            <View style={{flex: 1, width: '100%'}}>
              <ScrollView
                ref={scrollViewRef}
                style={{flex: 1}}
                bounces={false}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled">
                <ProfileImageComponent
                  value={formikProps.values.photo}
                  onChange={value => formikProps.handleChange('photo')(value)}
                  isFromOnboarding
                  errorText={formikProps.touched.photo && t(formikProps.errors.photo || '')}
                />
                <View style={{paddingHorizontal: 20, marginTop: 5, marginBottom: 10}}>
                  <Text style={{fontSize: 14}}>
                    Show us your cute face! If you’re shy, just upload a picture of a landscape
                  </Text>
                </View>
                <View
                  style={{
                    paddingHorizontal: 30,
                    marginTop: 20,
                  }}>
                  <CustomTextInput
                    description={t('onboarding.first_name')}
                    value={formikProps.values.first_name}
                    onChangeValue={formikProps.handleChange('first_name')}
                    errorText={formikProps.touched.first_name && t(formikProps.errors.first_name || '')}
                  />
                  <CustomTextInput
                    description={t('onboarding.last_name')}
                    value={formikProps.values.last_name}
                    onChangeValue={formikProps.handleChange('last_name')}
                  />
                  <CustomTextInput
                    description={t('onboarding.description1')}
                    // placeholder={t('onboarding.description2')}
                    value={formikProps.values.description}
                    onChangeValue={formikProps.handleChange('description')}
                    errorText={formikProps.touched.description && t(formikProps.errors.description || '')}
                    isMultiline
                  />
                  <CustomBox
                    description={t('onboarding.date_of_birth')}
                    value={
                      formikProps.values.date_of_birth
                        ? moment(formikProps.values.date_of_birth).format('DD/MM/YYYY')
                        : ''
                    }
                    handlePress={handleOpenDatePicker}
                    icon={BookIcon()}
                    errorText={formikProps.touched.date_of_birth && t(formikProps.errors.date_of_birth || '')}
                  />
                  <CustomBox
                    description={t('onboarding.gender1')}
                    value={formikProps.values.gender ? t(`onboarding.${formikProps.values.gender.split(' ')[0]}`) : ''}
                    handlePress={() => setModalIsVisible(true)}
                    icon={DropdownArrowIcon()}
                    errorText={formikProps.touched.gender && t(formikProps.errors.gender || '')}
                  />
                  <CustomBox
                    description={t('onboarding.location1')}
                    value={currentPositionState?.address || ''}
                    handlePress={() => {
                      getCurrentPosition();
                      setLocationModalIsVisible(true);
                      setLocationError('');
                    }}
                    icon={DropdownArrowIcon()}
                    errorText={locationError}
                  />
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'flex-end',
                      alignContent: 'flex-end',
                      justifyContent: 'flex-end',
                      marginTop: 20,
                    }}>
                    <CustomTextInput
                      description={t('settings.join_group_info')}
                      value={formikProps.values.group_invite_code}
                      editable={!joinedGroupId}
                      onChangeValue={formikProps.handleChange('group_invite_code')}
                      buttonWidth="70%"
                      errorText={formikProps.touched.group_invite_code && t(formikProps.errors.group_invite_code || '')}
                    />
                    <Button
                      label={joinedGroupId ? t('settings.leave') : t('settings.join')}
                      isLoading={isJoinLoading}
                      containerStyle={{
                        width: '30%',
                        borderRadius: 6,
                        marginStart: 10,
                        marginBottom: Platform.OS == 'android' ? 22 : 18,
                        backgroundColor: colors.statusBlue,
                        height: 50,
                      }}
                      onPress={async () => {
                        if (!formikProps.values.group_invite_code) {
                          Notifier.showNotification({
                            title: 'Please enter an invitation code.',
                            Component: NotifierComponents.Alert,
                            componentProps: {
                              alertType: 'info',
                            },
                          });
                          return;
                        }
                        if (joinedGroupId) {
                          try {
                            setIsJoinLoading(true);
                            await leaveGroup(joinedGroupId);
                            setIsJoinLoading(false);
                          } catch (error) {
                            setIsJoinLoading(false);
                            Notifier.showNotification({
                              title: t('settings.leave_error'),
                              Component: NotifierComponents.Alert,
                              componentProps: {
                                alertType: 'error',
                              },
                            });
                          }
                          return;
                        }
                        try {
                          setIsJoinLoading(true);
                          await joinGroup(formikProps.values.group_invite_code);
                          setIsJoinLoading(false);
                        } catch (error) {
                          setIsJoinLoading(false);
                          Notifier.showNotification({
                            title: t('settings.join_error'),
                            Component: NotifierComponents.Alert,
                            componentProps: {
                              alertType: 'error',
                            },
                          });
                        }
                      }}
                    />
                  </View>
                </View>
              </ScrollView>
              <View
                style={{
                  width: '100%',
                  paddingHorizontal: 30,
                  justifyContent: 'flex-end',
                  marginBottom: Platform.OS === 'android' ? 20 : bottom,
                }}>
                <Button
                  label={t('generic.continue')}
                  isLoading={isLoading}
                  onPress={e => {
                    if (Object.keys(formikProps.errors).length > 0) {
                      // Scroll to the first error field
                      scrollToErrorField();
                    } else if (!currentPositionState?.address) {
                      setLocationError(t('onboarding.location_error'));
                      scrollToErrorField();
                    }
                    formikProps.handleSubmit(e as any);
                  }}
                  textStyle={{
                    fontSize: 15,
                    fontWeight: '500',
                    color: colors.white,
                  }}
                  containerStyle={{
                    width: '100%',
                    height: 40,
                    borderRadius: 6,
                    backgroundColor: colors.primary,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 24,
                  }}
                />

                <ProgressDots dotsNumber={6} selectedDotNumber={1} />
              </View>
              <DateTimeModal
                isVisible={isDatePickerModalVisible}
                close={handleCloseDatePicker}
                onPress={() => {
                  if (birthdayDate) {
                    formikProps.handleChange('date_of_birth')(birthdayDate.format('YYYY-MM-DDTHH:mm:ss'));
                  }
                  handleCloseDatePicker();
                }}
                date={birthdayDate.toDate()}
                handleChooseMaxDate={handleChooseBirthdayDate}
              />
              <ModalWithGenders
                chosenGender={formikProps.values.gender}
                isVisible={modalIsVisible}
                close={() => setModalIsVisible(false)}
                onPress={value => () => {
                  formikProps.handleChange('gender')(value);
                  setModalIsVisible(false);
                }}
              />
              <LocationModal
                isVisible={locationModalIsVisible}
                close={() => {
                  setLocationModalIsVisible(false);
                }}
              />
            </View>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </View>
  );
}
