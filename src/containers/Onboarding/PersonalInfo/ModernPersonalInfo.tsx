import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import axios from 'axios';
import {Formik} from 'formik';
import moment from 'moment-timezone';
import {useCallback, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, KeyboardAvoidingView, Platform, ScrollView, Text, View, TouchableOpacity} from 'react-native';
import {BASE_API_URL} from '@env';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import * as yup from 'yup';
import {CalendarIcon} from '~assets/icons';
import Svg, {Path} from 'react-native-svg';
import ModernButton from '~components/ModernButton';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernTextInput from '~components/ModernTextInput/ModernTextInput';
import ModernLocationInput from '~components/ModernLocationInput/ModernLocationInput';
import ModernOnboardingHeader from '~components/ModernOnboarding/ModernOnboardingHeader';
import DateTimeModal from '~components/DateTimeModal';
import LocationModal from '~components/LocationModal';
import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
import {ModalWithGenders} from '~components/ModalWithItems';
import {ProfileImageComponent} from '~components/Settings';
import {SCREENS} from '~constants';
import {spacing, typography, borderRadius} from '~constants/design';
import {useMapsContext} from '~providers/maps/zustand';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';
import {NavigationProps} from '~types/navigation/navigation.type';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';
import {haptics} from '~Utils/haptics';
import Animated, {FadeInDown, SlideInUp} from 'react-native-reanimated';
import {SlideIn, FadeIn} from '~components/MicroInteractions/MicroInteractions';

// Simple UserIcon component
const UserIcon = ({color = '#666'}: {color?: string}) => (
  <Svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <Path
      d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M20.5899 22C20.5899 18.13 16.7399 15 11.9999 15C7.25991 15 3.40991 18.13 3.40991 22"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

// Simple LocationIcon component
const LocationIcon = ({color = '#666'}: {color?: string}) => (
  <Svg width="18" height="18" viewBox="0 0 24 24" fill="none">
    <Path
      d="M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.364 3.63604C20.0518 5.32387 21 7.61305 21 10Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const validationSchema = yup.object().shape({
  first_name: yup
    .string()
    .transform(value => value.trim())
    .matches(/^[a-zA-Z]+(?: [a-zA-Z]+)?$/, 'onboarding.first_name_error')
    .required('onboarding.first_name_error'),
  last_name: yup.string().optional(),
  date_of_birth: yup
    .string()
    .required('onboarding.dob_required')
    .test('min-age', 'onboarding.min_age_error', function (value) {
      if (!value) return false;
      const birthDate = moment(value);
      const age = moment().diff(birthDate, 'years');
      return age >= 13;
    }),
  gender: yup.string().required('onboarding.selection_error'),
  description: yup.string().optional(),
  photo: yup.string().required('onboarding.image_required'),
});

// Modern Field Component for better UX
interface ModernFieldProps {
  label: string;
  value: string;
  onPress: () => void;
  icon?: React.ReactNode;
  errorText?: string;
  placeholder?: string;
  required?: boolean;
}

const ModernField: React.FC<ModernFieldProps> = ({
  label,
  value,
  onPress,
  icon,
  errorText,
  placeholder,
  required = false,
}) => {
  const {colors} = useTheme();

  const handlePress = () => {
    haptics.light();
    onPress();
  };

  return (
    <View style={{marginBottom: spacing.lg}}>
      <Text
        style={{
          fontSize: typography.fontSize.sm,
          fontWeight: typography.fontWeight.medium,
          color: colors.textPrimary,
          marginBottom: spacing.xs,
        }}>
        {label} {required && <Text style={{color: colors.error}}>*</Text>}
      </Text>
      <TouchableOpacity
        onPress={handlePress}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: spacing.lg,
          paddingVertical: spacing.md,
          borderRadius: borderRadius.lg,
          borderWidth: 1,
          borderColor: errorText ? colors.error : colors.border + '40',
          backgroundColor: colors.surface,
          minHeight: 56,
        }}
        activeOpacity={0.7}>
        {icon && <View style={{marginRight: spacing.md}}>{icon}</View>}
        <Text
          style={{
            flex: 1,
            fontSize: typography.fontSize.base,
            color: value ? colors.textPrimary : colors.textSecondary,
            fontWeight: value ? typography.fontWeight.medium : typography.fontWeight.normal,
          }}>
          {value || placeholder}
        </Text>
      </TouchableOpacity>
      {errorText && (
        <Text
          style={{
            fontSize: typography.fontSize.sm,
            color: colors.error,
            marginTop: spacing.xs,
            marginLeft: spacing.sm,
          }}>
          {errorText}
        </Text>
      )}
    </View>
  );
};

export default function ModernPersonalInfo() {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();

  const [isDatePickerModalVisible, setIsDatePickerModalVisible] = useState(false);
  const [birthdayDate, setBirthdayDate] = useState<moment.Moment>(moment().subtract(18, 'years')); // Default to 18 years old
  const [modalIsVisible, setModalIsVisible] = useState(false);

  // Calculate maximum date for minimum age of 13 years (users must be 13 or older)
  const maxBirthdayDate = moment().subtract(13, 'years').toDate();
  const [locationModalIsVisible, setLocationModalIsVisible] = useState(false);
  const {currentPositionState, setCurrentPositionState} = useMapsContext();
  const [locationError, setLocationError] = useState('');
  const {personalOnboarding, setPersonalOnboardingProfileInfo} = useOnboardingStore();
  const scrollViewRef = useRef<ScrollView>(null);
  const [joinedGroupId, setJoinGroupId] = useState('');

  // Field refs for smart scrolling
  const fieldRefs = useRef<{[key: string]: View | null}>({});

  const {bottom} = useSafeAreaInsets();

  const [isLoading, setIsLoading] = useState(false);
  const [isJoinLoading, setIsJoinLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Personal Info', 'ModernPersonalInfo');
  }, []);

  const {getCurrentPosition} = useGetCurrentPosition();

  const joinGroup = async (invitationCode: string) => {
    try {
      setIsJoinLoading(true);
      const token = await FirebaseAuth.getAuthToken();
      const config = {
        headers: {Authorization: token, Accept: 'application/json'},
      };
      const response = await axios.post(
        `${BASE_API_URL}community/join`,
        {invitation_code: invitationCode},
        config,
      );
      if (response.data.community_uuid) {
        setJoinGroupId(response.data.community_uuid);
      }
      Notifier.showNotification({
        title: t('settings.join_success'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
    } catch (error) {
      Notifier.showNotification({
        title: t('settings.join_error'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'error',
        },
      });
    } finally {
      setIsJoinLoading(false);
    }
  };

  const leaveGroup = async (groupId: string) => {
    try {
      setIsJoinLoading(true);
      const token = await FirebaseAuth.getAuthToken();
      const config = {
        headers: {Authorization: token, Accept: 'application/json'},
      };
      await axios.delete(BASE_API_URL + `community/leave/${groupId}`, config);
      setJoinGroupId('');
      Notifier.showNotification({
        title: t('settings.leave_success'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
    } catch (error) {
      Notifier.showNotification({
        title: t('settings.leave_error'),
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'error',
        },
      });
    } finally {
      setIsJoinLoading(false);
    }
  };

  const handleChooseBirthdayDate = useCallback((value?: Date) => {
    if (value) {
      setBirthdayDate(moment(value));
      return;
    }
  }, []);

  const handleOpenDatePicker = useCallback(() => {
    setIsDatePickerModalVisible(true);
  }, []);

  const handleCloseDatePicker = useCallback(() => {
    setIsDatePickerModalVisible(false);
  }, []);

  // Smart scroll to error field function
  const scrollToErrorField = (errors: any) => {
    const errorFields = Object.keys(errors);
    if (errorFields.length === 0) return;

    const firstErrorField = errorFields[0];
    const fieldRef = fieldRefs.current[firstErrorField];

    if (fieldRef && scrollViewRef.current) {
      fieldRef.measureLayout(
        scrollViewRef.current as any,
        (x, y) => {
          scrollViewRef.current?.scrollTo({
            y: Math.max(0, y - 100), // Offset to show field clearly
            animated: true,
          });
        },
        () => {
          // Fallback to scroll to end if measurement fails
          scrollViewRef.current?.scrollToEnd({animated: true});
        },
      );
    }
  };

  const handleBack = () => {
    haptics.light();
    navigation.goBack();
  };

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernOnboardingHeader
        title={t('onboarding.personal_info') || 'Personal Information'}
        subtitle={t('onboarding.personal_info_subtitle') || 'Tell us a bit about yourself to get started'}
        step={2}
        totalSteps={5}
        onBackPress={handleBack}
        showSkip={false}
      />

      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{flex: 1}}>
        <Formik
          initialValues={{
            first_name: personalOnboarding.first_name || auth().currentUser?.displayName?.split(' ')[0] || '',
            last_name: personalOnboarding.last_name || auth().currentUser?.displayName?.split(' ')[1] || '',
            date_of_birth: personalOnboarding.date_of_birth,
            gender: personalOnboarding.gender,
            description: personalOnboarding.description,
            photo: personalOnboarding.photo || auth().currentUser?.photoURL,
            group_invite_code: '',
          }}
          onSubmit={(values, actions) => {
            setIsLoading(true);

            // Check location validation
            if (!currentPositionState?.address) {
              setIsLoading(false);
              setLocationError(t('onboarding.location_error'));
              // Scroll to location field
              scrollToErrorField({location: 'error'});
              return;
            }

            const infoObject = {
              first_name: values.first_name,
              last_name: values.last_name,
              date_of_birth: values.date_of_birth,
              description: values.description,
              gender: values.gender,
              coords: {
                lat: currentPositionState?.latitude,
                long: currentPositionState?.longitude,
              },
              photo: values.photo || '',
            };
            setPersonalOnboardingProfileInfo(infoObject);
            navigation.navigate(SCREENS.ONBOARDING_GROUP);
          }}
          validationSchema={validationSchema}>
          {formikProps => (
            <View style={{flex: 1}}>
              <ScrollView
                ref={scrollViewRef}
                style={{flex: 1}}
                contentContainerStyle={{
                  paddingHorizontal: spacing.md,
                  paddingBottom: spacing['6xl'],
                }}
                bounces={false}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled">
                {/* Profile Image Section */}
                <FadeIn delay={100} duration={600}>
                  <View
                    ref={ref => {
                      fieldRefs.current['photo'] = ref;
                    }}>
                    <ModernCard
                      variant="elevated"
                      padding="xl"
                      margin="xs"
                      style={{alignItems: 'center', marginHorizontal: 0}}>
                      <ProfileImageComponent
                        value={formikProps.values.photo || ''}
                        onChange={value => formikProps.handleChange('photo')(value)}
                        isFromOnboarding
                        errorText={
                          formikProps.touched.photo && formikProps.errors.photo
                            ? t(formikProps.errors.photo)
                            : undefined
                        }
                      />
                      <Text
                        style={{
                          fontSize: typography.fontSize.sm,
                          color: colors.textSecondary,
                          textAlign: 'center',
                          marginTop: spacing.md,
                          lineHeight: typography.fontSize.sm * 1.4,
                        }}>
                        {t('onboarding.photo_hint')}
                      </Text>
                    </ModernCard>
                  </View>
                </FadeIn>

                {/* Form Fields */}
                <SlideIn direction="up" delay={200} duration={500}>
                  <ModernCard variant="default" padding="xl" margin="xs" style={{marginHorizontal: 0}}>
                    <View
                      ref={ref => {
                        fieldRefs.current['first_name'] = ref;
                      }}
                      style={{marginBottom: spacing.lg}}>
                      <ModernTextInput
                        label={t('onboarding.first_name')}
                        placeholder={t('onboarding.first_name_placeholder') || 'Enter your first name'}
                        value={formikProps.values.first_name}
                        onChangeText={formikProps.handleChange('first_name')}
                        errorText={
                          formikProps.touched.first_name && formikProps.errors.first_name
                            ? t(formikProps.errors.first_name)
                            : undefined
                        }
                        leftIcon={<UserIcon color={colors.textSecondary} />}
                        variant="outlined"
                        size="lg"
                      />
                    </View>

                    <View style={{marginBottom: spacing.lg}}>
                      <ModernTextInput
                        label={t('onboarding.last_name')}
                        placeholder={t('onboarding.last_name_placeholder') || 'Enter your last name (optional)'}
                        value={formikProps.values.last_name}
                        onChangeText={formikProps.handleChange('last_name')}
                        leftIcon={<UserIcon color={colors.textSecondary} />}
                        variant="outlined"
                        size="lg"
                      />
                    </View>

                    <View style={{marginBottom: spacing.lg}}>
                      <ModernTextInput
                        label={t('onboarding.description1')}
                        placeholder={t('onboarding.description2') || 'Tell us about yourself...'}
                        value={formikProps.values.description}
                        onChangeText={formikProps.handleChange('description')}
                        errorText={
                          formikProps.touched.description && formikProps.errors.description
                            ? t(formikProps.errors.description)
                            : undefined
                        }
                        multiline
                        numberOfLines={4}
                        variant="outlined"
                        size="lg"
                        style={{minHeight: 100}}
                      />
                    </View>

                    <View
                      ref={ref => {
                        fieldRefs.current['date_of_birth'] = ref;
                      }}
                      style={{marginBottom: spacing.lg}}>
                      <ModernField
                        label={t('onboarding.date_of_birth')}
                        value={
                          formikProps.values.date_of_birth
                            ? moment(formikProps.values.date_of_birth).format('DD/MM/YYYY')
                            : ''
                        }
                        onPress={handleOpenDatePicker}
                        icon={<CalendarIcon color={colors.textSecondary} />}
                        errorText={
                          formikProps.touched.date_of_birth && formikProps.errors.date_of_birth
                            ? t(formikProps.errors.date_of_birth)
                            : undefined
                        }
                        placeholder={t('onboarding.select_birthday') || 'Select your birthday'}
                        required
                      />
                      <Text
                        style={{
                          fontSize: typography.fontSize.xs,
                          color: colors.textSecondary,
                          marginTop: spacing.xs,
                          marginLeft: spacing.sm,
                          fontStyle: 'italic',
                        }}>
                        You must be at least 13 years old to create an account
                      </Text>
                    </View>

                    <View
                      ref={ref => {
                        fieldRefs.current['gender'] = ref;
                      }}
                      style={{marginBottom: spacing.lg}}>
                      <ModernField
                        label={t('onboarding.gender1')}
                        value={
                          formikProps.values.gender ? t(`onboarding.${formikProps.values.gender.split(' ')[0]}`) : ''
                        }
                        onPress={() => setModalIsVisible(true)}
                        icon={<UserIcon color={colors.textSecondary} />}
                        errorText={
                          formikProps.touched.gender && formikProps.errors.gender
                            ? t(formikProps.errors.gender)
                            : undefined
                        }
                        placeholder={t('onboarding.select_gender') || 'Select your gender'}
                        required
                      />
                    </View>

                    <View
                      ref={ref => {
                        fieldRefs.current['location'] = ref;
                      }}
                      style={{marginBottom: spacing.lg}}>
                      <ModernLocationInput
                        label={t('onboarding.location1')}
                        value={currentPositionState?.address || ''}
                        onLocationSelect={location => {
                          setCurrentPositionState({
                            latitude: location.latitude,
                            longitude: location.longitude,
                            address: location.address,
                          });
                          setLocationError('');
                        }}
                        errorText={locationError}
                        placeholder={t('onboarding.location_search_placeholder')}
                        required
                      />
                    </View>

                    {/* Group Invite Section */}
                    <View
                      style={{
                        marginTop: spacing.xl,
                        paddingTop: spacing.lg,
                        borderTopWidth: 1,
                        borderTopColor: colors.border + '30',
                      }}>
                      <View style={{alignItems: 'center', marginBottom: spacing.lg}}>
                        <Text
                          style={{
                            fontSize: typography.fontSize.base,
                            fontWeight: typography.fontWeight.semibold,
                            color: colors.textPrimary,
                            marginBottom: spacing.xs,
                          }}>
                          {t('onboarding.join_group_title')}
                        </Text>
                        <Text
                          style={{
                            fontSize: typography.fontSize.sm,
                            color: colors.textSecondary,
                            textAlign: 'center',
                            lineHeight: typography.fontSize.sm * 1.4,
                          }}>
                          {t('onboarding.group_description')}
                        </Text>
                      </View>
                      <View style={{flexDirection: 'row', alignItems: 'center', gap: spacing.sm}}>
                        <View style={{flex: 1}}>
                          <ModernTextInput
                            placeholder={t('onboarding.group_code_placeholder')}
                            value={formikProps.values.group_invite_code}
                            onChangeText={formikProps.handleChange('group_invite_code')}
                            editable={!joinedGroupId}
                            variant="outlined"
                            size="md"
                          />
                        </View>
                        <ModernButton
                          title={joinedGroupId ? t('settings.leave') : t('settings.join')}
                          loading={isJoinLoading}
                          onPress={async () => {
                            if (!formikProps.values.group_invite_code) {
                              Notifier.showNotification({
                                title: 'Please enter an invitation code.',
                                Component: NotifierComponents.Alert,
                                componentProps: {
                                  alertType: 'info',
                                },
                              });
                              return;
                            }
                            if (joinedGroupId) {
                              try {
                                setIsJoinLoading(true);
                                await leaveGroup(joinedGroupId);
                                setIsJoinLoading(false);
                              } catch (error) {
                                setIsJoinLoading(false);
                                Notifier.showNotification({
                                  title: t('settings.leave_error'),
                                  Component: NotifierComponents.Alert,
                                  componentProps: {
                                    alertType: 'error',
                                  },
                                });
                              }
                              return;
                            }
                            try {
                              setIsJoinLoading(true);
                              await joinGroup(formikProps.values.group_invite_code);
                              setIsJoinLoading(false);
                            } catch (error) {
                              setIsJoinLoading(false);
                              Notifier.showNotification({
                                title: t('settings.join_error'),
                                Component: NotifierComponents.Alert,
                                componentProps: {
                                  alertType: 'error',
                                },
                              });
                            }
                          }}
                          variant="secondary"
                          size="md"
                          style={{minWidth: 100, height: 48}}
                        />
                      </View>
                    </View>
                  </ModernCard>
                </SlideIn>
              </ScrollView>

              {/* Footer with Continue Button */}
              <SlideIn direction="up" delay={400} duration={500}>
                <View
                  style={{
                    paddingHorizontal: spacing.md,
                    paddingTop: spacing.md,
                    paddingBottom: Platform.OS === 'android' ? spacing.lg : bottom,
                    backgroundColor: colors.background,
                    borderTopWidth: 1,
                    borderTopColor: colors.border + '20',
                  }}>
                  <ModernButton
                    title={t('generic.continue')}
                    loading={isLoading}
                    onPress={e => {
                      if (Object.keys(formikProps.errors).length > 0) {
                        // Scroll to the first error field
                        scrollToErrorField(formikProps.errors);
                      }
                      formikProps.handleSubmit(e as any);
                    }}
                    variant="primary"
                    size="lg"
                    fullWidth
                    hapticType="success"
                  />

                  {/* Progress Dots */}
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                      marginTop: spacing.lg,
                    }}>
                    {[...Array(5)].map((_, index) => (
                      <View
                        key={index}
                        style={{
                          width: 8,
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: index === 1 ? colors.primary : colors.border,
                          marginHorizontal: spacing.xs,
                        }}
                      />
                    ))}
                  </View>
                </View>
              </SlideIn>

              {/* Modals */}
              <DateTimeModal
                isVisible={isDatePickerModalVisible}
                close={handleCloseDatePicker}
                onPress={() => {
                  if (birthdayDate) {
                    formikProps.handleChange('date_of_birth')(birthdayDate.format('YYYY-MM-DDTHH:mm:ss'));
                  }
                  handleCloseDatePicker();
                }}
                date={birthdayDate.toDate()}
                handleChooseMaxDate={handleChooseBirthdayDate}
                maxAvailableDate={maxBirthdayDate}
                withoutMaxAvailableDate={false}
              />
              <ModalWithGenders
                chosenGender={formikProps.values.gender}
                isVisible={modalIsVisible}
                close={() => setModalIsVisible(false)}
                onPress={value => () => {
                  formikProps.handleChange('gender')(value);
                  setModalIsVisible(false);
                }}
              />
              <LocationModal
                isVisible={locationModalIsVisible}
                close={() => {
                  setLocationModalIsVisible(false);
                }}
              />
            </View>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </View>
  );
}
