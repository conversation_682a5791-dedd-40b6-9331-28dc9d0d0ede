import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, ScrollView, Text, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import * as yup from 'yup';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {reverseGeocode} from '~Utils/location';
import Button from '~components/Button';
import CustomBox from '~components/CustomBox';
import {CustomTextInput} from '~components/CustomTextInput';
import {ModernHeader} from '~components/ModernHeader';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernButton from '~components/ModernButton';
import ModernTextInput from '~components/ModernTextInput';
import LocationModal from '~components/LocationModal';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import useUpdateBusiness from '~hooks/business/useUpdateBusiness';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius, shadows} from '~constants/design';
import Animated, {FadeInDown, SlideInUp} from 'react-native-reanimated';

const PersonalInfoBusiness = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {data} = useGetBusinessAccount(auth().currentUser?.uid || '');
  const {mutateAsync: handleUpdateBusiness} = useUpdateBusiness();

  const validationSchema = yup.object().shape({
    name: yup.string().required('onboarding.first_name_error'),
    description: yup.string().min(40, 'onboarding.min_length_error').required('onboarding.description_error'),
  });

  const navigation = useNavigation();
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const {bottom} = useSafeAreaInsets();
  const [addressName, setAddressName] = useState('');
  const [coords, setCoords] = useState({latitude: data?.coords?.lat, longitude: data?.coords?.long});

  const [locationError, setLocationError] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  
  
  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    const func = async () => {
      if (data?.coords) {
        setCoords({latitude: data?.coords?.lat, longitude: data?.coords?.long});
        const address = await reverseGeocode({
          coords: {latitude: data?.coords?.lat, longitude: data?.coords?.long},
        });
        setAddressName(address || '');
      }
    };
    func();
  }, [data]);

  useEffect(() => {
    logScreenView('Personal Info Business', 'PesonalInfoBusiness');
  }, []);

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader
        title={t('settings.business_info') || 'Business Information'}
        subtitle={t('settings.business_info_subtitle') || 'Update your business details'}
        variant="default"
      />

      <Formik
        onSubmit={async values => {
          setIsLoading(true);
          try {
            if (coords.latitude && coords.longitude) {
              await handleUpdateBusiness({
                uid: auth().currentUser!.uid,
                name: values.name,
                description: values.description,
                coords: {lat: coords.latitude, long: coords.longitude},
                business_type_id: data?.business_type_id || 1,
                photo: data?.photo || '',
                is_registration_finished: !!data?.is_registration_finished,
                email: data?.email || '',
              });
              navigation.goBack();
            }
          } catch (e) {
            setIsLoading(false);
          }
        }}
        initialValues={{
          name: data?.name || '',
          description: data?.description || '',
          location: addressName || '',
        }}
        validationSchema={validationSchema}>
        {({values, handleChange, handleSubmit, errors}) => {
          return (
            <>
              <ScrollView
                style={{flex: 1}}
                contentContainerStyle={{
                  paddingTop: spacing.md,
                  paddingBottom: spacing['6xl'] + bottom,
                }}
                showsVerticalScrollIndicator={false}>
                {/* Header Section */}
                <Animated.View entering={FadeInDown.delay(100).duration(400)}>
                  <ModernCard variant="elevated" padding="xl" margin="md" style={{alignItems: 'center'}}>
                    <View
                      style={{
                        width: 80,
                        height: 80,
                        borderRadius: 40,
                        backgroundColor: colors.primary + '20',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: spacing.md,
                      }}>
                      <Text style={{fontSize: 32}}>🏢</Text>
                    </View>
                    <Text
                      style={{
                        fontSize: typography.fontSize['2xl'],
                        fontWeight: typography.fontWeight.bold,
                        color: colors.textPrimary,
                        marginBottom: spacing.sm,
                        textAlign: 'center',
                      }}>
                      {t('settings.business_info') || 'Business Information'}
                    </Text>
                    <Text
                      style={{
                        fontSize: typography.fontSize.base,
                        fontWeight: typography.fontWeight.normal,
                        color: colors.textSecondary,
                        lineHeight: typography.fontSize.base * 1.5,
                        textAlign: 'center',
                      }}>
                      {t('settings.business_info_description') ||
                        'Keep your business information up to date for better visibility.'}
                    </Text>
                  </ModernCard>
                </Animated.View>

                {/* Business Details Form */}
                <Animated.View entering={FadeInDown.delay(200).duration(400)}>
                  <ModernCard variant="default" padding="xl" margin="md">
                    <Text
                      style={{
                        fontSize: typography.fontSize.lg,
                        fontWeight: typography.fontWeight.bold,
                        color: colors.textPrimary,
                        marginBottom: spacing.md,
                      }}>
                      {t('settings.business_details') || 'Business Details'}
                    </Text>

                    <ModernTextInput
                      label={t('settings.business_name') || 'Business Name'}
                      placeholder={t('settings.business_name_placeholder') || 'Enter your business name'}
                      value={values.name}
                      onChangeText={handleChange('name')}
                      variant="outlined"
                      size="lg"
                      errorText={errors.name?.toString()}
                      style={{marginBottom: spacing.md}}
                    />

                    <ModernTextInput
                      label={t('settings.business_description') || 'Business Description'}
                      placeholder={t('settings.business_description_placeholder') || 'Describe your business'}
                      value={values.description}
                      onChangeText={handleChange('description')}
                      variant="outlined"
                      size="lg"
                      multiline
                      numberOfLines={4}
                      errorText={errors.description?.toString()}
                      style={{marginBottom: spacing.md}}
                    />

                    <CustomBox
                      description={t('settings.business_location') || 'Business Location'}
                      handlePress={() => {
                        setIsLocationModalOpen(true);
                        setLocationError('');
                      }}
                      value={values.location || addressName}
                      errorText={locationError}
                    />
                  </ModernCard>
                </Animated.View>
              </ScrollView>

              {/* Save Button */}
              <Animated.View
                entering={SlideInUp.delay(300).duration(400)}
                style={{
                  paddingHorizontal: spacing.md,
                  paddingBottom: Platform.OS === 'ios' ? bottom + spacing.md : spacing.md,
                }}>
                <ModernButton
                  title={t('settings.save_changes') || 'Save Changes'}
                  onPress={handleSubmit}
                  loading={isLoading}
                  variant="primary"
                  size="lg"
                  fullWidth
                  hapticFeedback
                  hapticType="success"
                />
              </Animated.View>

              <LocationModal
                isVisible={isLocationModalOpen}
                close={() => {
                  setIsLocationModalOpen(false);
                }}
                onLocationChange={object => {
                  if (object?.latitude && object.longitude) {
                    setCoords({latitude: object?.latitude, longitude: object?.longitude});
                    handleChange('location')(object?.address || '');
                  }
                }}
              />
            </>
          );
        }}
      </Formik>
    </View>
  );
};

export default PersonalInfoBusiness;
