import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View, TouchableOpacity, ScrollView, Platform} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {SCREENS} from '~constants';
import {useGetUserChildren} from '~hooks/user/useGetUserChildren';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useTheme} from '~contexts/ThemeContext';
import ModernButton from '~components/ModernButton';
import ModernOnboardingHeader from '~components/ModernOnboarding/ModernOnboardingHeader';
import Animated, {FadeIn} from 'react-native-reanimated';
import {spacing, typography, borderRadius} from '~constants/design';
import * as haptics from '~Utils/haptics';
import ModalWithQuantity from '~components/ModalWithItems/ModalWithQuantity';
import ModalWithAges from '~components/ModalWithItems/ModalWithAges';
import React from 'react';

// Modern Age Selection Card Component
const ModernAgeCard = ({
  index,
  value,
  onPress,
  isSelected,
}: {
  index: number;
  value?: string;
  onPress: () => void;
  isSelected: boolean;
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();

  return (
    <TouchableOpacity
      style={{
        flex: 1,
        marginHorizontal: spacing.xs,
        marginVertical: spacing.sm,
        minHeight: 60,
        borderRadius: borderRadius.lg,
        borderWidth: 1,
        borderColor: isSelected ? colors.primary : colors.border,
        backgroundColor: colors.white,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: colors.black,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}
      onPress={() => {
        try {
          haptics.light();
        } catch (error) {
          console.log('Haptic feedback not available:', error);
        }
        onPress();
      }}>
      <Text
        style={{
          fontSize: typography.fontSize.sm,
          fontWeight: typography.fontWeight.semibold,
          color: value ? colors.textPrimary : colors.textSecondary,
          textAlign: 'center',
        }}>
        {value || t('onboarding.select_age')}
      </Text>
      <Text
        style={{
          fontSize: typography.fontSize.xs,
          color: colors.textSecondary,
          marginTop: spacing.xs,
        }}>
        {t('onboarding.child')} {index + 1}
      </Text>
    </TouchableOpacity>
  );
};

const ModernChildrenQuantity = ({
  onSubmit,
  isFromSettings = false,
}: {
  onSubmit?: () => void;
  isFromSettings?: boolean;
}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const [quantityModalIsVisible, setQuantityModalIsVisible] = useState(false);
  const [children, setChildren] = useState<string[]>([]);
  const [ageModalIsVisible, setAgeModalIsVisible] = useState(false);
  const [childrenQuantity, setChildrenQuantity] = useState(0);
  const [chosenAgeIndex, setChosenAgeIndex] = useState(0);
  const {bottom} = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProps>();
  const {setPersonalChildren} = useOnboardingStore();
  const {data: personalChildren} = useGetUserChildren();

  const [isLoading, setIsLoading] = useState(false);
  const isFocused = useIsFocused();

  useEffect(() => {
    if (isFocused) {
      setIsLoading(false);
    }
  }, [isFocused]);

  const changeValueAtIndex = (index: number, newValue: string) => {
    setChildren(prevArr => {
      const newArr = [...prevArr];
      newArr[index] = newValue;
      return newArr;
    });
  };

  useEffect(() => {
    if (isFromSettings && personalChildren) {
      setChildren(personalChildren.map(child => child.child_age.toString()));
      setChildrenQuantity(personalChildren.length);
    }
  }, [personalChildren, isFromSettings]);

  useEffect(() => {
    childrenQuantity ? setChildren([...new Array(childrenQuantity)]) : setChildren([]);
  }, [childrenQuantity, setChildren]);

  const handleBack = () => {
    try {
      haptics.light();
    } catch (error) {
      console.log('Haptic feedback not available:', error);
    }
    navigation.goBack();
  };

  const handleContinue = () => {
    if (onSubmit) {
      onSubmit();
    } else {
      setIsLoading(true);
      setPersonalChildren(children);
      navigation.navigate(SCREENS.ONBOARDING_SUBCATEGORIES);
    }
  };

  const isDisabled = !!children.length && !!children.filter(age => age === undefined).length;

  const childrenAgeCards = useMemo(
    () =>
      children?.map((value, index) => (
        <View key={`${childrenQuantity}-${index}`} style={{width: children.length === 1 ? '100%' : '48%'}}>
          <ModernAgeCard
            index={index}
            value={value}
            onPress={() => {
              setAgeModalIsVisible(true);
              setChosenAgeIndex(index);
            }}
            isSelected={!!value}
          />
        </View>
      )),
    [children, childrenQuantity],
  );

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernOnboardingHeader
        title={t('onboarding.how_many_kids_desc') || 'Tell us about your children'}
        subtitle="This helps us recommend family-friendly events and activities"
        step={4}
        totalSteps={5}
        onBackPress={handleBack}
        showSkip={false}
      />

      <ScrollView
        style={{flex: 1}}
        contentContainerStyle={{
          paddingHorizontal: spacing.md,
          paddingBottom: spacing['6xl'],
        }}
        bounces={false}
        showsVerticalScrollIndicator={false}>
        {/* Quantity Selection */}
        <Animated.View entering={FadeIn.delay(100).duration(600)}>
          <View style={{marginBottom: spacing.xl}}>
            <Text
              style={{
                fontSize: typography.fontSize.lg,
                fontWeight: typography.fontWeight.semibold,
                color: colors.textPrimary,
                marginBottom: spacing.sm,
                textAlign: 'center',
              }}>
              {t('onboarding.how_many_kids')}
            </Text>

            <TouchableOpacity
              style={{
                borderRadius: borderRadius.lg,
                borderWidth: 1,
                borderColor: childrenQuantity > 0 ? colors.primary : colors.border,
                backgroundColor: colors.white,
                paddingVertical: spacing.lg,
                paddingHorizontal: spacing.md,
                alignItems: 'center',
                shadowColor: colors.black,
                shadowOffset: {width: 0, height: 2},
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 3,
              }}
              onPress={() => {
                try {
                  haptics.light();
                } catch (error) {
                  console.log('Haptic feedback not available:', error);
                }
                setQuantityModalIsVisible(true);
              }}>
              <Text
                style={{
                  fontSize: typography.fontSize.xl,
                  fontWeight: typography.fontWeight.bold,
                  color: childrenQuantity > 0 ? colors.primary : colors.textSecondary,
                }}>
                {childrenQuantity > 0 ? childrenQuantity : '?'}
              </Text>
              <Text
                style={{
                  fontSize: typography.fontSize.sm,
                  color: colors.textSecondary,
                  marginTop: spacing.xs,
                }}>
                {childrenQuantity === 1 ? t('onboarding.child') : t('onboarding.children')}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Age Selection Cards */}
        {children.length > 0 && (
          <Animated.View entering={FadeIn.delay(200).duration(500)}>
            <View style={{marginBottom: spacing.xl}}>
              <Text
                style={{
                  fontSize: typography.fontSize.lg,
                  fontWeight: typography.fontWeight.semibold,
                  color: colors.textPrimary,
                  marginBottom: spacing.lg,
                  textAlign: 'center',
                }}>
                {t('onboarding.select_ages')}
              </Text>

              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  marginHorizontal: -spacing.xs,
                }}>
                {childrenAgeCards}
              </View>
            </View>
          </Animated.View>
        )}

        {/* Info Text */}
        {children.length > 0 && (
          <Animated.View entering={FadeIn.delay(400).duration(600)}>
            <Text
              style={{
                fontSize: typography.fontSize.sm,
                color: colors.textSecondary,
                textAlign: 'center',
                lineHeight: typography.fontSize.sm * 1.4,
                fontStyle: 'italic',
              }}>
              {t('onboarding.we_are_asking_for_the_kids_age')}
            </Text>
          </Animated.View>
        )}
      </ScrollView>

      {/* Footer with Continue Button */}
      <Animated.View entering={FadeIn.delay(300).duration(500)}>
        <View
          style={{
            paddingHorizontal: spacing.md,
            paddingTop: spacing.md,
            paddingBottom: Platform.OS === 'android' ? spacing.lg : bottom,
            backgroundColor: colors.background,
            borderTopWidth: 1,
            borderTopColor: colors.border + '20',
          }}>
          <ModernButton
            title={t('generic.continue')}
            loading={isLoading}
            disabled={isDisabled}
            onPress={handleContinue}
            variant="primary"
            size="lg"
            fullWidth
            hapticType="success"
          />

          {/* Progress Dots */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              marginTop: spacing.lg,
            }}>
            {[...Array(5)].map((_, index) => (
              <View
                key={index}
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: index === 3 ? colors.primary : colors.border,
                  marginHorizontal: spacing.xs,
                }}
              />
            ))}
          </View>
        </View>
      </Animated.View>

      {/* Modals */}
      <ModalWithQuantity
        isVisible={quantityModalIsVisible}
        close={() => setQuantityModalIsVisible(false)}
        onPress={(value: number) => () => {
          setChildrenQuantity(value);
          setQuantityModalIsVisible(false);
        }}
        chosenQuantity={childrenQuantity}
      />
      <ModalWithAges
        isVisible={ageModalIsVisible}
        close={() => setAgeModalIsVisible(false)}
        onPress={value => () => {
          changeValueAtIndex(chosenAgeIndex, value);
          setAgeModalIsVisible(false);
        }}
        chosenAge={children[chosenAgeIndex] || ''}
      />
    </View>
  );
};

export default ModernChildrenQuantity;
