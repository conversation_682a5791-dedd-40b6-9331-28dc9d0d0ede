import OneSignal from 'react-native-onesignal';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import FirebaseAuth from '~services/FirebaseAuthService';

export const useHandleLogOut = () => {
  const {reset} = useOnboardingStore();
  const {resetUser} = useUserStore();

  const handleLogOut = async () => {
    reset();
    resetUser();
    try {
      // Use OneSignal v4 API for disabling push notifications
      OneSignal.disablePush(true);
      console.log('✅ OneSignal push notifications disabled');
    } catch (error) {
      console.error('❌ Failed to opt out OneSignal push subscription:', error);
    }
    await FirebaseAuth.logOut();
  };

  return {handleLogOut};
};
