import auth from '@react-native-firebase/auth';
import storage from '@react-native-firebase/storage';
import {RouteProp, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import {useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {SCREENS} from '~constants';
import {useCreateBusiness} from '~hooks/business/useCreateBusiness';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import {useUserStore} from '~providers/userStore/zustand';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {useHandleLogOut} from '../hooks/useHandleLogOut';
import React from 'react';
import CheckBox from '@react-native-community/checkbox';
import {useTheme} from '~contexts/ThemeContext';
import ModernButton from '~components/ModernButton';
import ModernOnboardingHeader from '~components/ModernOnboarding/ModernOnboardingHeader';
import Animated, {FadeIn} from 'react-native-reanimated';
import {spacing, typography, borderRadius} from '~constants/design';
import * as haptics from '~Utils/haptics';

interface IBox {
  id: number;
  title: string;
  imageUrl: any;
  sizes: number;
  selectedItems: number[];
  setSelectedItems: (value: number[]) => void;
}

const ModernBoxComponent = ({title, imageUrl, sizes, selectedItems, setSelectedItems, id}: IBox) => {
  const {colors} = useTheme();
  const isSelected = selectedItems.find(val => val === id);

  const onPress = () => {
    try {
      haptics.light();
    } catch (error) {
      console.log('Haptic feedback not available:', error);
    }
    if (setSelectedItems) {
      isSelected
        ? setSelectedItems([...selectedItems.filter(selectedGroup => selectedGroup !== id)])
        : setSelectedItems([...selectedItems, id]);
    }
  };

  return (
    <TouchableOpacity
      style={{
        flex: 1,
        aspectRatio: 1,
        marginHorizontal: spacing.xs,
        marginVertical: spacing.sm,
        borderRadius: borderRadius.lg,
        borderWidth: 1,
        borderColor: isSelected ? colors.primary : colors.border,
        backgroundColor: colors.white,
        overflow: 'hidden',
        elevation: 3,
      }}
      onPress={onPress}>
      <View style={{flex: 1, position: 'relative'}}>
        <FastImage
          style={{
            width: '100%',
            height: '70%',
            borderTopLeftRadius: borderRadius.lg - 2,
            borderTopRightRadius: borderRadius.lg - 2,
          }}
          source={imageUrl}
          resizeMode="cover"
        />
        <View
          style={{
            flex: 1,
            paddingHorizontal: spacing.sm,
            paddingVertical: spacing.sm,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: typography.fontSize.sm,
              fontWeight: typography.fontWeight.semibold,
              color: colors.textPrimary,
              textAlign: 'center',
              lineHeight: typography.fontSize.sm * 1.2,
            }}>
            {title}
          </Text>
        </View>
        {isSelected && (
          <View
            style={{
              position: 'absolute',
              top: spacing.sm,
              right: spacing.sm,
              width: 24,
              height: 24,
              borderRadius: 12,
              backgroundColor: colors.primary,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text
              style={{
                color: colors.white,
                fontSize: 16,
                fontWeight: 'bold',
              }}>
              ✓
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const ModernSelectTypeOfProfile = ({
  isModal,
  onSubmit,
  selectedGroups,
}: {
  isModal?: boolean;
  onSubmit?: (values: number[]) => void;
  withoutLabel?: boolean;
  selectedGroups?: number[];
  isFromSettings?: boolean;
}) => {
  const {params} = useRoute<RouteProp<RootStackParamsList, SCREENS.ONBOARDING_GROUP>>();
  const navigation = useNavigation<NavigationProps>();
  const {t} = useTranslation();
  const {colors} = useTheme();
  const {setPersonalGroups, personalOnboarding, businessOnboarding} = useOnboardingStore();
  const {user} = useUserStore();
  const {handleLogOut} = useHandleLogOut();
  const [isLoading, setIsLoading] = useState(false);
  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  const {mutateAsync} = useCreateBusiness();
  const [selectedItems, setSelectedItems] = useState<number[]>(selectedGroups || personalOnboarding.groups || []);

  const boxes = useMemo(
    () => [
      {
        title: t('onboarding.groups_2'),
        imageUrl: require('~assets/images/studentsImage.jpg'),
        sizes: 0.9615,
        id: 2,
      },
      {
        title: t('onboarding.groups_3'),
        imageUrl: require('~assets/images/professionalsImage.jpg'),
        sizes: 1.093,
        id: 3,
      },
      {
        title: t('onboarding.groups_1'),
        imageUrl: require('~assets/images/familiesImage.jpg'),
        sizes: 1.01,
        id: 1,
      },
      {
        title: t('onboarding.groups_4'),
        imageUrl: require('~assets/images/seniorsImage.jpg'),
        sizes: 1.101,
        id: 4,
      },
      {
        title: t('onboarding.groups_5'),
        imageUrl: require('~assets/images/couplesImage.jpg'),
        sizes: 1,
        id: 5,
      },
    ],
    [t],
  );

  const submit = async () => {
    setIsLoading(true);
    if (params?.isBusiness) {
      try {
        if (businessOnboarding.photo[0] != 'h') {
          await storage().ref('users/' + auth().currentUser!.uid + '/profile.png').putFile(businessOnboarding.photo);
        }

        const urlPhoto =
          businessOnboarding.photo[0] == 'h'
            ? auth().currentUser?.photoURL
            : await storage().ref('users/' + auth().currentUser!.uid + '/profile.png').getDownloadURL();

        await mutateAsync({
          ...businessOnboarding,
          photo: urlPhoto || '',
          email: auth().currentUser!.email!,
          target_audiences: selectedItems,
          uid: auth().currentUser!.uid,
          is_registration_finished: true,
          onboarding_answers: [],
        });
        return;
      } catch (e) {
        setIsLoading(false);
        console.error('Hello', e);
        return;
      }
    }
    setPersonalGroups(selectedItems);
    selectedItems.find(group => group === 1)
      ? navigation.navigate(SCREENS.ONBOARDING_CHILDREN)
      : navigation.navigate(SCREENS.ONBOARDING_SUBCATEGORIES, {isBusiness: false});
  };

  const handleBack = () => {
    try {
      haptics.light();
    } catch (error) {
      console.log('Haptic feedback not available:', error);
    }
    if (!user?.is_registration_finished) {
      handleLogOut();
    } else {
      navigation.goBack();
    }
  };

  const {bottom} = useSafeAreaInsets();

  if (isModal) {
    // Return modal version without header
    return <View style={{flex: 1, backgroundColor: colors.background}}>{/* Modal content will be added here */}</View>;
  }

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernOnboardingHeader
        title={t('onboarding.related_groups') || 'What do you relate with?'}
        subtitle="Select the groups that best describe you to get personalized event recommendations"
        step={3}
        totalSteps={5}
        onBackPress={handleBack}
        showSkip={false}
      />

      <ScrollView
        style={{flex: 1}}
        contentContainerStyle={{
          paddingHorizontal: spacing.md,
          paddingBottom: spacing['6xl'],
        }}
        bounces={false}
        showsVerticalScrollIndicator={false}>
        {/* Group Selection Grid */}
        <Animated.View entering={FadeIn.delay(200).duration(500)}>
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              marginHorizontal: -spacing.xs,
            }}>
            {boxes.map(box => (
              <View key={box.id} style={{width: '50%'}}>
                <ModernBoxComponent
                  id={box.id}
                  title={box.title}
                  imageUrl={box.imageUrl}
                  sizes={box.sizes}
                  selectedItems={selectedItems}
                  setSelectedItems={setSelectedItems}
                />
              </View>
            ))}
          </View>
        </Animated.View>
      </ScrollView>

      {/* Footer with Continue Button */}
      <Animated.View entering={FadeIn.delay(400).duration(500)}>
        <View
          style={{
            paddingHorizontal: spacing.md,
            paddingTop: spacing.md,
            paddingBottom: Platform.OS === 'android' ? spacing.lg : bottom,
            backgroundColor: colors.background,
            borderTopWidth: 1,
            borderTopColor: colors.border + '20',
          }}>
          <ModernButton
            title={params?.isBusiness ? t('generic.submit') : t('generic.continue')}
            loading={isLoading}
            disabled={!selectedItems?.length}
            onPress={() => {
              if (isModal && !!onSubmit) {
                onSubmit(selectedItems || []);
                return;
              }
              submit();
            }}
            variant="primary"
            size="lg"
            fullWidth
            hapticType="success"
          />

          {/* Progress Dots */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              marginTop: spacing.lg,
            }}>
            {[...Array(5)].map((_, index) => (
              <View
                key={index}
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: index === 2 ? colors.primary : colors.border,
                  marginHorizontal: spacing.xs,
                }}
              />
            ))}
          </View>
        </View>
      </Animated.View>
    </View>
  );
};

export default ModernSelectTypeOfProfile;
