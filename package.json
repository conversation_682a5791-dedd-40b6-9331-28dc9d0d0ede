{"name": "pyxida", "version": "12.3.10", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "start": "react-native start", "test": "jest", "pod": "cd ios && pod install && cd ..", "postinstall": "patch-package"}, "dependencies": {"@gorhom/bottom-sheet": "^4.6.4", "@invertase/react-native-apple-authentication": "^2.2.2", "@mapbox/geo-viewport": "^0.5.0", "@notifee/react-native": "^7.8.2", "@react-native-async-storage/async-storage": "^1.19.1", "@react-native-community/blur": "^4.4.1", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/eslint-config": "^3.2.0", "@react-native-community/netinfo": "^9.4.1", "@react-native-community/slider": "^4.4.3", "@react-native-firebase/analytics": "^14.5.0", "@react-native-firebase/app": "^14.5.0", "@react-native-firebase/auth": "^14.12.0", "@react-native-firebase/crashlytics": "^14.5.0", "@react-native-firebase/dynamic-links": "^14.5.0", "@react-native-firebase/firestore": "^14.5.0", "@react-native-firebase/messaging": "14.12.0", "@react-native-firebase/perf": "^14.5.0", "@react-native-firebase/storage": "^14.5.0", "@react-native-google-signin/google-signin": "^10.0.1", "@react-native-masked-view/masked-view": "^0.2.9", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.16", "@react-navigation/native-stack": "^6.9.13", "@shopify/flash-list": "^1.6.1", "@stripe/stripe-react-native": "^0.40.0", "@types/react-native-version-check": "^3.4.8", "add": "^2.0.6", "axios": "^1.5.0", "fetch-intercept": "^2.4.0", "firebase-admin": "^12.2.0", "firebase-functions": "^5.0.1", "formik": "^2.4.2", "haversine": "^1.1.1", "i18next": "^23.2.11", "immer": "^10.0.2", "js-base64": "^3.7.7", "lodash": "^4.17.21", "lottie-react-native": "^7.0.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.2.0", "react-hook-form": "^7.54.2", "react-i18next": "^13.0.2", "react-native": "0.72.3", "react-native-add-calendar-event": "^5.0.0", "react-native-animated-nav-tab-bar": "^3.1.10", "react-native-autocomplete-dropdown": "^4.4.0", "react-native-calendar-events": "^2.2.0", "react-native-camera": "^4.2.1", "react-native-config": "^1.5.5", "react-native-context-menu-view": "^1.14.1", "react-native-date-picker": "^4.3.7", "react-native-device-country": "^1.0.5", "react-native-device-info": "^10.9.0", "react-native-dotenv": "^3.4.11", "react-native-fast-image": "^8.6.3", "react-native-flash-message": "^0.4.2", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "2.12.1", "react-native-get-random-values": "^1.11.0", "react-native-haptic-feedback": "^2.3.3", "react-native-hyperlink": "^0.0.22", "react-native-image-crop-picker": "0.41.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-controller": "^1.8.0", "react-native-linear-gradient": "^2.8.3", "react-native-map-clustering": "^3.4.2", "react-native-maps": "1.11.3", "react-native-modal": "^13.0.1", "react-native-notifier": "^1.9.0", "react-native-onesignal": "^4.5.4", "react-native-pager-view": "^6.4.1", "react-native-permissions": "^4.1.5", "react-native-qrcode-svg": "^6.2.0", "react-native-radio-buttons-group": "^3.1.0", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "3.4.2", "react-native-recaptcha-that-works": "^2.0.0", "react-native-redash": "^18.1.0", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^4.7.1", "react-native-screens": "3.22.0", "react-native-share": "^11.0.3", "react-native-simple-dialogs": "^2.1.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-splash-screen": "^3.3.0", "react-native-star-rating-widget": "^1.9.2", "react-native-svg": "^13.14.1", "react-native-switch": "^1.5.1", "react-native-tab-view": "^3.5.2", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "10.2.0", "react-native-version-check": "^3.4.7", "react-native-wallet": "^1.0.8", "react-native-webview": "^13.12.0", "react-query": "^3.39.3", "styled-components": "^6.0.7", "supercluster": "^8.0.1", "text-encoding-polyfill": "^0.6.7", "util": "^0.12.5", "uuid": "^11.0.5", "yarn": "^1.22.19", "yup": "^1.2.0", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.9", "@tsconfig/react-native": "^3.0.0", "@types/haversine": "^1.1.8", "@types/lodash": "^4.14.197", "@types/react": "^18.0.24", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.7", "prettier": "^3.0.3", "react-native-svg-transformer": "^1.0.0", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}