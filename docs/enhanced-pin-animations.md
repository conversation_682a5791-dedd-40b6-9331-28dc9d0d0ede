# Enhanced Pin Animations

## Overview

The map pins have been significantly enhanced with smooth, prominent, and user-friendly animations that provide better visibility and interaction feedback while maintaining excellent performance.

## Key Enhancements

### 1. Enhanced Entry Animations

- **Subtle Bounce Entry**: Pins now appear with a gentle bounce effect (1.15x scale) that settles smoothly
- **Staggered Animation**: Multiple pins animate in with staggered delays (25ms between each) for a natural feel
- **Improved Timing**: Longer animation duration (300ms) for smoother visual experience

### 2. Improved Visibility

- **Larger Base Size**: Pins are 15% larger by default when `enhancedVisibility` is enabled
- **Enhanced Dimensions**: ModernEventPin uses larger base dimensions (38x48 vs 32x40) for better visibility
- **Subtle Breathing**: Gentle pulsing animation (1.05x scale) every 6 seconds for better discoverability

### 3. Interactive Feedback

- **Press Animations**: Pins scale to 1.25x when pressed with smooth spring animations
- **Selection Feedback**: Enhanced bounce and pulse animations for selected pins
- **Responsive Touch**: Quick 150ms response time for immediate user feedback

### 4. Smooth Performance

- **Native Driver**: All animations use the native driver for 60fps performance
- **Optimized Easing**: Custom easing curves for different animation types:
  - Entry: `{tension: 100, friction: 8}` - Smooth bounce
  - Interaction: `{tension: 150, friction: 6}` - Quick response
  - Pulse: `{tension: 80, friction: 10}` - Gentle breathing

### 5. Simple Cluster Display

- **Static Clusters**: Clusters appear instantly without animations for better performance
- **Clean Design**: Simple, clean cluster appearance that doesn't distract from individual pins
- **Immediate Response**: Clusters respond immediately to touch without animation delays

## Configuration Options

### Animation Config

```typescript
animation: {
  enabled: true,
  duration: 300,
  useNativeDriver: true,
  entryDelay: 50,           // Base delay for staggered entry
  bounceIntensity: 1.15,    // How much pins bounce on entry
  pulseEnabled: true,       // Enable subtle pulsing
  pulseIntensity: 1.08,     // Pulse scale factor
  pulseDuration: 2000,      // Pulse animation duration
  interactionScale: 1.25,   // Scale when pressed
  interactionDuration: 150, // Touch response time
}
```

### ModernEventPin Props

```typescript
interface ModernEventPinProps {
  enhancedVisibility?: boolean; // Make pin more prominent (default: true)
  subtlePulse?: boolean; // Enable breathing animation (default: true)
  // ... other existing props
}
```

## Animation Sequence

### Pin Entry

1. Pin starts at scale 0 with opacity 0
2. Staggered delay based on index (50ms + index \* 25ms)
3. Gentle bounce to 1.15x scale
4. Smooth settle to 1.0x scale
5. Opacity fades in over 80% of animation duration
6. Subtle breathing animation starts after entry

### User Interaction

1. Press down: Quick scale to 1.25x
2. Press up: Smooth return to normal scale
3. Selection: Enhanced bounce and pulse sequence

### Visibility Features

1. Continuous subtle breathing every 6 seconds
2. Enhanced base size for better visibility
3. Smooth transitions between all states

## Performance Considerations

- All animations use React Native's native driver
- Animations are properly cleaned up on unmount
- Staggered entry prevents animation conflicts
- Optimized for 60fps performance on all devices

## User Experience Benefits

- **Better Discoverability**: Larger pins with subtle animations help users notice events
- **Smooth Interactions**: Responsive feedback makes the app feel polished
- **Natural Feel**: Staggered animations and proper easing create organic movement
- **Accessibility**: Enhanced visibility helps users with visual impairments
- **Performance**: Native animations maintain smooth scrolling and interaction
