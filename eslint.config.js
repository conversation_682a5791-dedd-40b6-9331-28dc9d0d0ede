const typescript = require('@typescript-eslint/eslint-plugin');
const typescriptParser = require('@typescript-eslint/parser');
const reactHooks = require('eslint-plugin-react-hooks');

module.exports = [
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        // React Native globals
        __DEV__: 'readonly',
        console: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        fetch: 'readonly',
        AbortController: 'readonly',
        AbortSignal: 'readonly',
        RequestInit: 'readonly',
        Response: 'readonly',
        WebSocket: 'readonly',
        Image: 'readonly',
        global: 'readonly',

        // Node.js globals (for scripts and config files)
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',
        process: 'readonly',
        Buffer: 'readonly',

        // TypeScript globals
        NodeJS: 'readonly',
        React: 'readonly',
        JSX: 'readonly',

        // Web APIs
        URLSearchParams: 'readonly',
      },
    },
    plugins: {
      '@typescript-eslint': typescript,
      'react-hooks': reactHooks,
    },
    rules: {
      // Basic JavaScript/TypeScript rules
      'no-console': 'off',
      'no-debugger': 'warn',
      'no-unused-vars': 'off',
      'no-undef': 'error', // Enable undefined variable detection
      'no-shadow': 'off',

      // TypeScript rules
      '@typescript-eslint/no-shadow': 'off', // Disabled per user request
      '@typescript-eslint/no-explicit-any': 'off', // Disabled per user request
      '@typescript-eslint/no-unused-vars': 'off', // Disabled per user request
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',

      // React Hooks rules - disabled to avoid compatibility issues
      'react-hooks/rules-of-hooks': 'off',
      'react-hooks/exhaustive-deps': 'off',

      // Import/Export rules
      'import/no-unresolved': 'off',
      'import/extensions': 'off',
    },
  },
  {
    ignores: [
      'node_modules/**',
      'android/**',
      'ios/**',
      'lib/**',
      '*.config.js',
      'metro.config.js',
      'babel.config.js',
      '__tests__/**',
      'coverage/**',
    ],
  },
];
