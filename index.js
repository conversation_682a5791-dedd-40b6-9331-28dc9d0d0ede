/**
 * @format
 */

// Initialize URL polyfill for React Native
import 'react-native-url-polyfill/auto';

// Initialize TextEncoder polyfill for React Native (required for QR code generation)
import 'text-encoding-polyfill';

console.log('📱 [DEBUG] Index.js loaded - app is starting');

import { AppRegistry } from 'react-native';
import App from './src/containers/Core/App';
import { name as appName } from './app.json';

console.log('📱 [DEBUG] About to register component with name:', appName);
AppRegistry.registerComponent(appName, () => App);
console.log('📱 [DEBUG] Component registered successfully with name:', appName);
