#!/usr/bin/env node

/**
 * Script to migrate from react-native-config to react-native-dotenv
 * Usage: node scripts/migrate-config-to-env.js
 */

const fs = require('fs');
const path = require('path');

// File extensions to process
const EXTENSIONS = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to process
const DIRECTORIES = ['src'];

// Environment variables mapping
const ENV_VARS = [
  'BASE_API_URL',
  'WS_URL', 
  'GOOGLE_API_KEY',
  'IOS_CLIENT_ID',
  'WEB_CLIENT_ID',
  'ONE_SIGNAL_TOKEN',
  'ONE_SIGNAL_APP_ID',
  'RECAPTHA_SITE_KEY',
  'RECAPTCHA_SECRET_KEY',
  'STRIPE_PUBLISH_KEY',
  'NODE_ENV'
];

function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

function migrateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;

  // 1. Replace import statement
  const configImportRegex = /import\s+Config\s+from\s+['"]react-native-config['"];?\s*\n?/g;
  if (content.match(configImportRegex)) {
    // Find which env vars are used in this file
    const usedVars = ENV_VARS.filter(varName => 
      content.includes(`Config.${varName}`)
    );
    
    if (usedVars.length > 0) {
      const envImport = `import {${usedVars.join(', ')}} from '@env';\n`;
      content = content.replace(configImportRegex, envImport);
      hasChanges = true;
      
      // 2. Replace Config.VARIABLE_NAME with VARIABLE_NAME
      usedVars.forEach(varName => {
        const configUsageRegex = new RegExp(`Config\\.${varName}`, 'g');
        content = content.replace(configUsageRegex, varName);
      });
    } else {
      // Remove unused import
      content = content.replace(configImportRegex, '');
      hasChanges = true;
    }
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Migrated: ${filePath}`);
    return true;
  }

  return false;
}

function processDirectory(dirPath) {
  const files = getAllFiles(dirPath);
  let totalFiles = 0;
  let migratedFiles = 0;

  for (const file of files) {
    if (EXTENSIONS.some(ext => file.endsWith(ext))) {
      totalFiles++;
      if (migrateFile(file)) {
        migratedFiles++;
      }
    }
  }

  console.log(`📁 ${dirPath}: ${migratedFiles}/${totalFiles} files migrated`);
  return migratedFiles;
}

// Main execution
console.log('🔄 Starting migration from react-native-config to react-native-dotenv...\n');

let totalMigrated = 0;
for (const dir of DIRECTORIES) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    totalMigrated += processDirectory(dir);
  }
}

console.log(`\n✨ Migration completed! ${totalMigrated} files migrated.`);
console.log('\n📋 Next steps:');
console.log('1. Remove old module declaration: rm src/declarations/module.d.ts');
console.log('2. Restart Metro: npx react-native start --reset-cache');
console.log('3. Rebuild app: npx react-native run-android/ios');
