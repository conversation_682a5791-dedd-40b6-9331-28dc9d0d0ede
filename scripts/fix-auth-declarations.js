#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const DIRECTORIES = ['src'];
const EXTENSIONS = ['.ts', '.tsx'];

// Files that need auth declaration moved to the top
const PROBLEMATIC_FILES = [
  'src/containers/Event/CreateEvent/CreateEventPublishModal/CreateEventPublishModal.tsx',
  'src/containers/Event/CreateEvent/CreateEventInfo/CreateEventInfo.tsx',
  'src/containers/Onboarding/PersonalInfo/PersonalInfoBusiness.tsx',
  'src/containers/Settings/PersonalInfo/PersonalInfo.tsx',
  'src/containers/Settings/HelpCenter/HelpCenter.tsx',
  'src/containers/Settings/EditSubcategories/index.tsx',
  'src/containers/Settings/LogOut/LogOut.tsx',
  'src/containers/Settings/SettingsView/SettingsView.tsx',
  'src/containers/Onboarding/Subcategories/ModernSubcategories.tsx',
  'src/containers/Onboarding/Subcategories/Subcategories.tsx',
  'src/containers/Onboarding/Group/Group.tsx',
  'src/containers/Onboarding/Group/ModernGroup.tsx',
  'src/containers/Onboarding/Preferances/index.tsx',
  'src/containers/Onboarding/BusinessInfo/BusinessInfo.tsx',
  'src/containers/Auth/ModernAuth/ModernAuth.tsx',
  'src/containers/Auth/Login/Login.tsx',
  'src/components/Chat/ChatItem/ChatItem.tsx',
  'src/components/Chat/ChatRow/ChatRow.tsx',
  'src/components/Chat/ChatRow/ConciergeChatRow.tsx',
  'src/components/Chat/ModernChatRow/ModernChatRow.tsx',
  'src/components/HomeScreenComponent/tabComponents/components/ModernEventCard/ModernEventCard.tsx',
  'src/components/HomeScreenComponent/HomeScreenLocationButton.tsx'
];

function fixAuthDeclaration(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  const changes = [];

  // Check if file uses auth.currentUser but declares auth later
  const authUsagePattern = /auth\.currentUser/;
  const authDeclarationPattern = /const\s+auth\s*=\s*getAuth\(\);/;
  
  const hasAuthUsage = content.match(authUsagePattern);
  const authDeclarationMatch = content.match(authDeclarationPattern);
  
  if (hasAuthUsage && authDeclarationMatch) {
    // Find the first usage of auth.currentUser
    const firstUsageMatch = content.match(authUsagePattern);
    const firstUsageIndex = content.indexOf(firstUsageMatch[0]);
    
    // Find the auth declaration
    const declarationIndex = content.indexOf(authDeclarationMatch[0]);
    
    // If declaration comes after usage, we need to move it
    if (declarationIndex > firstUsageIndex) {
      // Remove the existing declaration
      content = content.replace(authDeclarationPattern, '');
      
      // Find a good place to insert it (after other const declarations at component start)
      const componentStartPatterns = [
        // React functional components
        /(const\s+\w+[^=]*=\s*\([^)]*\)\s*=>\s*{\s*)/,
        // Export function components
        /(export\s+function\s+\w+[^{]*{\s*)/,
        // Regular functions
        /(function\s+\w+[^{]*{\s*)/
      ];
      
      let insertIndex = -1;
      for (const pattern of componentStartPatterns) {
        const match = content.match(pattern);
        if (match) {
          insertIndex = match.index + match[0].length;
          break;
        }
      }
      
      if (insertIndex !== -1) {
        // Look for existing const declarations to insert after them
        const afterComponentStart = content.substring(insertIndex);
        const constPattern = /(const\s+[^=]*=\s*[^;]+;?\s*\n)/g;
        let lastConstEnd = insertIndex;
        let constMatch;
        
        while ((constMatch = constPattern.exec(afterComponentStart)) !== null) {
          // Skip if this const is inside a useEffect or other function
          const beforeMatch = afterComponentStart.substring(0, constMatch.index);
          const openBraces = (beforeMatch.match(/{/g) || []).length;
          const closeBraces = (beforeMatch.match(/}/g) || []).length;
          
          if (openBraces === closeBraces) {
            lastConstEnd = insertIndex + constMatch.index + constMatch[0].length;
          }
        }
        
        // Insert the auth declaration
        const authDeclaration = '  const auth = getAuth();\n';
        content = content.slice(0, lastConstEnd) + authDeclaration + content.slice(lastConstEnd);
        
        hasChanges = true;
        changes.push('Moved auth declaration to proper position');
      }
    }
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed ${filePath}:`);
    changes.forEach(change => console.log(`   - ${change}`));
    return true;
  }
  
  return false;
}

function main() {
  console.log('🔧 Fixing auth declaration positions...\n');
  
  let totalFixed = 0;
  
  PROBLEMATIC_FILES.forEach(filePath => {
    if (fixAuthDeclaration(filePath)) {
      totalFixed++;
    }
  });
  
  console.log(`\n✅ Fixed ${totalFixed} files with auth declaration issues.`);
}

if (require.main === module) {
  main();
}

module.exports = { fixAuthDeclaration };
