#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const DIRECTORIES = ['src'];
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Additional cleanup patterns for edge cases
const CLEANUP_PATTERNS = [
  // Fix any remaining auth() calls that might have been missed
  { from: /auth\(\)\.currentUser/g, to: 'auth.currentUser', description: 'auth().currentUser' },
  { from: /auth\(\)!/g, to: 'auth!', description: 'auth()!' },
  { from: /auth\(\)\?/g, to: 'auth?', description: 'auth()?' },
  
  // Fix any remaining firestore() calls
  { from: /firestore\(\)\.collection/g, to: 'firestore.collection', description: 'firestore().collection' },
  { from: /firestore\(\)\.doc/g, to: 'firestore.doc', description: 'firestore().doc' },
  { from: /firestore\(\)\.batch/g, to: 'firestore.batch', description: 'firestore().batch' },
  { from: /firestore\(\)\.runTransaction/g, to: 'firestore.runTransaction', description: 'firestore().runTransaction' },
  
  // Fix any remaining storage() calls
  { from: /storage\(\)\.ref/g, to: 'storage.ref', description: 'storage().ref' },
  { from: /storage\(\)\.refFromURL/g, to: 'storage.refFromURL', description: 'storage().refFromURL' },
  
  // Fix any remaining messaging() calls
  { from: /messaging\(\)\.getToken/g, to: 'messaging.getToken', description: 'messaging().getToken' },
  { from: /messaging\(\)\.onMessage/g, to: 'messaging.onMessage', description: 'messaging().onMessage' },
  { from: /messaging\(\)\.requestPermission/g, to: 'messaging.requestPermission', description: 'messaging().requestPermission' },
  
  // Fix any remaining database() calls
  { from: /database\(\)\.ref/g, to: 'database.ref', description: 'database().ref' },
  { from: /database\(\)\.goOffline/g, to: 'database.goOffline', description: 'database().goOffline' },
  { from: /database\(\)\.goOnline/g, to: 'database.goOnline', description: 'database().goOnline' },
  
  // Fix any remaining functions() calls
  { from: /functions\(\)\.httpsCallable/g, to: 'functions.httpsCallable', description: 'functions().httpsCallable' },
];

// Helper functions
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

function cleanupFirebaseFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  const changes = [];

  CLEANUP_PATTERNS.forEach(pattern => {
    const matches = content.match(pattern.from);
    if (matches) {
      content = content.replace(pattern.from, pattern.to);
      hasChanges = true;
      changes.push(`Fixed ${pattern.description} (${matches.length} occurrences)`);
    }
  });

  // Check for any remaining problematic patterns
  const problematicPatterns = [
    /auth\(\)/g,
    /firestore\(\)/g,
    /storage\(\)/g,
    /messaging\(\)/g,
    /database\(\)/g,
    /functions\(\)/g,
  ];

  problematicPatterns.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      console.warn(`⚠️  Still found ${pattern.source} in ${filePath} (${matches.length} occurrences)`);
      console.warn(`   Please manually review this file`);
    }
  });

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Cleaned up: ${filePath}`);
    changes.forEach(change => console.log(`   - ${change}`));
    return true;
  }

  return false;
}

function processDirectory(dirPath) {
  const files = getAllFiles(dirPath);
  let totalFiles = 0;
  let cleanedFiles = 0;

  for (const file of files) {
    if (EXTENSIONS.some(ext => file.endsWith(ext))) {
      totalFiles++;
      if (cleanupFirebaseFile(file)) {
        cleanedFiles++;
      }
    }
  }

  console.log(`📁 ${dirPath}: ${cleanedFiles}/${totalFiles} files cleaned`);
  return cleanedFiles;
}

// Main execution
console.log('🧹 Starting final Firebase cleanup...\n');
console.log('This will fix any remaining Firebase API issues:\n');

let totalCleaned = 0;
for (const dir of DIRECTORIES) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    totalCleaned += processDirectory(dir);
  }
}

console.log(`\n✨ Final Firebase cleanup completed! ${totalCleaned} files cleaned.`);

if (totalCleaned === 0) {
  console.log('🎉 No remaining Firebase API issues found!');
  console.log('\n📋 Your Firebase v22 migration is complete!');
  console.log('1. Restart Metro: npx react-native start --reset-cache');
  console.log('2. Rebuild app: npx react-native run-android/ios');
  console.log('3. Test the app thoroughly');
} else {
  console.log('\n📋 Next steps:');
  console.log('1. Review any warnings above');
  console.log('2. Restart Metro: npx react-native start --reset-cache');
  console.log('3. Rebuild app: npx react-native run-android/ios');
  console.log('4. Check for remaining deprecation warnings');
}
