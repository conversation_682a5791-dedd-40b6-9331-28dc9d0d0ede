#!/usr/bin/env node

const fs = require('fs');

// Files that need Firebase service declarations
const FIREBASE_FIXES = [
  {
    file: 'src/components/HomeScreenComponent/HomeScreenLocationButton.tsx',
    services: ['auth'],
    imports: ['getAuth']
  },
  {
    file: 'src/containers/Onboarding/Group/Group.tsx',
    services: ['storage'],
    imports: ['getStorage']
  },
  {
    file: 'src/containers/Onboarding/Group/ModernGroup.tsx',
    services: ['storage'],
    imports: ['getStorage']
  },
  {
    file: 'src/containers/Onboarding/Subcategories/Subcategories.tsx',
    services: ['storage'],
    imports: ['getStorage']
  },
  {
    file: 'src/containers/Settings/HelpCenter/HelpCenter.tsx',
    services: ['auth'],
    imports: ['getAuth']
  },
  {
    file: 'src/containers/Settings/PersonalInfo/PersonalInfo.tsx',
    services: ['auth'],
    imports: ['getAuth']
  },
  {
    file: 'src/containers/Settings/SettingsView/SettingsView.tsx',
    services: ['auth'],
    imports: ['getAuth']
  }
];

function addFirebaseDeclarations(filePath, services, imports) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;

  // Check if imports are missing and add them
  imports.forEach(importName => {
    const module = importName.replace('get', '').toLowerCase();
    const importPattern = new RegExp(`import.*${importName}.*from.*@react-native-firebase/${module}`);
    
    if (!content.match(importPattern)) {
      // Add import after existing imports
      const importStatement = `import { ${importName} } from '@react-native-firebase/${module}';\n`;
      const lastImportMatch = content.match(/^import.*from.*['"];?\s*$/gm);
      
      if (lastImportMatch) {
        const lastImport = lastImportMatch[lastImportMatch.length - 1];
        const insertIndex = content.lastIndexOf(lastImport) + lastImport.length + 1;
        content = content.slice(0, insertIndex) + importStatement + content.slice(insertIndex);
        hasChanges = true;
        console.log(`   - Added ${importName} import`);
      }
    }
  });

  // Add service declarations
  services.forEach(service => {
    const getter = `get${service.charAt(0).toUpperCase() + service.slice(1)}`;
    const declarationPattern = new RegExp(`const\\s+${service}\\s*=\\s*${getter}\\(\\)`);
    
    if (!content.match(declarationPattern)) {
      // Find component start
      const componentPattern = /(const\s+\w+[^=]*=\s*\([^)]*\)\s*=>\s*{)/;
      const componentMatch = content.match(componentPattern);
      
      if (componentMatch) {
        const insertIndex = componentMatch.index + componentMatch[0].length;
        const declaration = `\n  const ${service} = ${getter}();`;
        content = content.slice(0, insertIndex) + declaration + content.slice(insertIndex);
        hasChanges = true;
        console.log(`   - Added ${service} declaration`);
      }
    }
  });

  if (hasChanges) {
    fs.writeFileSync(filePath, content);
    return true;
  }
  return false;
}

function main() {
  console.log('🔧 Fixing remaining Firebase issues...\n');
  
  let totalFixed = 0;
  
  FIREBASE_FIXES.forEach(({file, services, imports}) => {
    console.log(`📁 Processing: ${file}`);
    if (addFirebaseDeclarations(file, services, imports)) {
      totalFixed++;
      console.log(`✅ Fixed: ${file}`);
    } else {
      console.log(`⏭️  No changes needed: ${file}`);
    }
    console.log('');
  });
  
  console.log(`✨ Fixed ${totalFixed} files with Firebase issues.`);
}

if (require.main === module) {
  main();
}
