#!/bin/bash

# Script to find files where firebase services are used before declaration
# Usage: ./find-service-usage-before-declaration.sh

echo "🔍 Searching for files where firebase services are used before declaration..."

# Directories to search
DIRECTORIES=("src")

# Services to check
SERVICES=("auth" "firestore" "storage" "messaging" "database" "functions")

# Find all TypeScript and JavaScript files
find "${DIRECTORIES[@]}" -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) | while read -r file; do
  for service in "${SERVICES[@]}"; do
    # Check if file uses service()
    if grep -q "${service}\s*(" "$file"; then
      # Find the position of first usage
      usage_line=$(grep -n "${service}\s*(" "$file" | head -1 | cut -d: -f1)
      # Find the position of declaration: const <anything> = service()
      decl_line=$(grep -n "const [^=]*= *${service}\s*(" "$file" | head -1 | cut -d: -f1)
      # If used before declared
      if [[ -n "$usage_line" && -n "$decl_line" && "$usage_line" -lt "$decl_line" ]]; then
        echo "⚠️  $file: ${service}() used on line $usage_line, but declared on line $decl_line"
      fi
    fi
  done
done

echo "✅ Search completed!"
