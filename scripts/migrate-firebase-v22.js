#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const DIRECTORIES = ['src'];
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Firebase migration patterns
const FIREBASE_MIGRATIONS = [
  // Auth migrations
  {
    oldImport: /import\s+auth\s+from\s+['"]@react-native-firebase\/auth['"];?\s*\n?/g,
    newImport: "import { getAuth } from '@react-native-firebase/auth';\n",
    replacements: [
      { from: /auth\(\)/g, to: 'auth' },
      { from: /const\s+auth\s*=\s*getAuth\(\);/g, to: 'const auth = getAuth();' }
    ]
  },
  // Firestore migrations
  {
    oldImport: /import\s+firestore\s+from\s+['"]@react-native-firebase\/firestore['"];?\s*\n?/g,
    newImport: "import { getFirestore } from '@react-native-firebase/firestore';\n",
    replacements: [
      { from: /firestore\(\)/g, to: 'firestore' },
      { from: /const\s+firestore\s*=\s*getFirestore\(\);/g, to: 'const firestore = getFirestore();' }
    ]
  },
  // Storage migrations
  {
    oldImport: /import\s+storage\s+from\s+['"]@react-native-firebase\/storage['"];?\s*\n?/g,
    newImport: "import { getStorage } from '@react-native-firebase/storage';\n",
    replacements: [
      { from: /storage\(\)/g, to: 'storage' },
      { from: /const\s+storage\s*=\s*getStorage\(\);/g, to: 'const storage = getStorage();' }
    ]
  },
  // Database migrations
  {
    oldImport: /import\s+database\s+from\s+['"]@react-native-firebase\/database['"];?\s*\n?/g,
    newImport: "import { getDatabase } from '@react-native-firebase/database';\n",
    replacements: [
      { from: /database\(\)/g, to: 'database' },
      { from: /const\s+database\s*=\s*getDatabase\(\);/g, to: 'const database = getDatabase();' }
    ]
  },
  // Functions migrations
  {
    oldImport: /import\s+functions\s+from\s+['"]@react-native-firebase\/functions['"];?\s*\n?/g,
    newImport: "import { getFunctions } from '@react-native-firebase/functions';\n",
    replacements: [
      { from: /functions\(\)/g, to: 'functions' },
      { from: /const\s+functions\s*=\s*getFunctions\(\);/g, to: 'const functions = getFunctions();' }
    ]
  },
  // Messaging migrations
  {
    oldImport: /import\s+messaging\s+from\s+['"]@react-native-firebase\/messaging['"];?\s*\n?/g,
    newImport: "import { getMessaging } from '@react-native-firebase/messaging';\n",
    replacements: [
      { from: /messaging\(\)/g, to: 'messaging' },
      { from: /const\s+messaging\s*=\s*getMessaging\(\);/g, to: 'const messaging = getMessaging();' }
    ]
  }
];

// Helper functions
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

function needsInstanceVariable(content, serviceName) {
  // Check if the service is used in the component/function
  const usagePatterns = [
    new RegExp(`${serviceName}\\.`, 'g'),
    new RegExp(`${serviceName}\\s*\\?\\s*\\.`, 'g'),
    new RegExp(`${serviceName}\\s*!\\s*\\.`, 'g')
  ];

  return usagePatterns.some(pattern => content.match(pattern));
}

function addInstanceVariable(content, serviceName, getterFunction) {
  // Check if instance variable already exists
  const existingVar = new RegExp(`const\\s+${serviceName}\\s*=\\s*${getterFunction}\\(\\);`);
  const existingClassVar = new RegExp(`this\\.${serviceName}\\s*=\\s*${getterFunction}\\(\\);`);
  if (content.match(existingVar) || content.match(existingClassVar)) {
    return content;
  }

  // Check if this is a class-based service
  const isClassBased = content.includes(`this.${serviceName}.`);

  if (isClassBased) {
    // For class-based services, add to constructor
    const constructorPattern = /(constructor\s*\([^)]*\)\s*{)/;
    const constructorMatch = content.match(constructorPattern);

    if (constructorMatch) {
      const insertIndex = constructorMatch.index + constructorMatch[0].length;
      const instanceVar = `\n    this.${serviceName} = ${getterFunction}();`;
      content = content.slice(0, insertIndex) + instanceVar + content.slice(insertIndex);
    }
  } else {
    // For functional components
    const patterns = [
      // React functional components with typed props
      /(const\s+\w+:\s*React\.FC<[^>]*>\s*=\s*\([^)]*\)\s*=>\s*{)/,
      // React functional components
      /(const\s+\w+[^=]*=\s*\([^)]*\)\s*=>\s*{)/,
      // Export function components
      /(export\s+function\s+\w+[^{]*{)/,
      // Regular functions
      /(function\s+\w+[^{]*{)/,
      // Class methods
      /(render\s*\(\s*\)\s*{)/
    ];

    for (const pattern of patterns) {
      const componentMatch = content.match(pattern);
      if (componentMatch) {
        // Look for existing hook declarations to insert after them
        const hookPattern = /(const\s+[^=]*=\s*use\w+\([^)]*\);?\s*\n)/g;
        const hooks = [];
        let hookMatch;

        while ((hookMatch = hookPattern.exec(content)) !== null) {
          if (hookMatch.index > componentMatch.index) {
            hooks.push({
              index: hookMatch.index + hookMatch[0].length,
              match: hookMatch[0]
            });
          }
        }

        let insertIndex;
        if (hooks.length > 0) {
          // Insert after the last hook
          insertIndex = hooks[hooks.length - 1].index;
        } else {
          // Insert right after component declaration
          insertIndex = componentMatch.index + componentMatch[0].length;
        }

        const instanceVar = `  const ${serviceName} = ${getterFunction}();\n`;
        content = content.slice(0, insertIndex) + instanceVar + content.slice(insertIndex);
        break;
      }
    }
  }

  return content;
}

function migrateFirebaseFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  const changes = [];

  FIREBASE_MIGRATIONS.forEach(migration => {
    // Check if file has the old import
    if (content.match(migration.oldImport)) {
      const serviceName = migration.newImport.match(/get(\w+)/)[1].toLowerCase();
      const getterFunction = migration.newImport.match(/(get\w+)/)[1];

      // Replace import
      content = content.replace(migration.oldImport, migration.newImport);
      hasChanges = true;
      changes.push(`Updated ${serviceName} import`);

      // Add instance variable if needed
      if (needsInstanceVariable(content, serviceName)) {
        content = addInstanceVariable(content, serviceName, getterFunction);
        changes.push(`Added ${serviceName} instance variable`);
      }

      // Apply replacements
      migration.replacements.forEach(replacement => {
        const matches = content.match(replacement.from);
        if (matches) {
          content = content.replace(replacement.from, replacement.to);
          changes.push(`Updated ${serviceName} usage (${matches.length} occurrences)`);
        }
      });
    }
  });

  // Check for commented imports that are still being used
  const commentedImportPattern = /\/\/\s*import\s*{\s*(\w+)\s*}\s*from\s*'@react-native-firebase\/(\w+)'/g;
  let commentedMatch;
  while ((commentedMatch = commentedImportPattern.exec(content)) !== null) {
    const getter = commentedMatch[1];
    const service = commentedMatch[2];
    const serviceInstance = service === 'auth' ? 'auth' :
      service === 'firestore' ? 'firestore' :
        service === 'storage' ? 'storage' :
          service === 'database' ? 'database' :
            service === 'functions' ? 'functions' :
              service === 'messaging' ? 'messaging' : service;

    // Check if the getter function is being used
    const usagePattern = new RegExp(`\\b${getter}\\(\\)`, 'g');
    const instanceUsagePattern = new RegExp(`\\b${serviceInstance}\\.`, 'g');

    if (content.match(usagePattern) || content.match(instanceUsagePattern)) {
      // Uncomment the import
      content = content.replace(commentedMatch[0], `import { ${getter} } from '@react-native-firebase/${service}'`);
      hasChanges = true;
      changes.push(`Uncommented ${getter} import from @react-native-firebase/${service}`);
    }
  }

  // Check for missing Firebase service instances and imports
  const serviceUsageChecks = [
    { service: 'auth', pattern: /\b(auth\.|this\.auth\.)/g, getter: 'getAuth', module: 'auth' },
    { service: 'firestore', pattern: /\b(firestore\.|this\.firestore\.)/g, getter: 'getFirestore', module: 'firestore' },
    { service: 'storage', pattern: /\b(storage\.|this\.storage\.)/g, getter: 'getStorage', module: 'storage' },
    { service: 'database', pattern: /\b(database\.|this\.database\.)/g, getter: 'getDatabase', module: 'database' },
    { service: 'functions', pattern: /\b(functions\.|this\.functions\.)/g, getter: 'getFunctions', module: 'functions' },
    { service: 'messaging', pattern: /\b(messaging\.|this\.messaging\.)/g, getter: 'getMessaging', module: 'messaging' },
  ];

  serviceUsageChecks.forEach(({ service, pattern, getter, module }) => {
    const hasUsage = content.match(pattern);
    const hasInstance = content.includes(`const ${service} = ${getter}()`);

    if (hasUsage) {
      // Add import if missing - check for both exact import and any import containing the getter
      const importRegex = new RegExp(`import\\s*{[^}]*\\b${getter}\\b[^}]*}\\s*from\\s*['"]@react-native-firebase/${module}['"]`, 'g');
      const hasExistingImport = content.match(importRegex);

      if (!hasExistingImport) {
        const importStatement = `import { ${getter} } from '@react-native-firebase/${module}';\n`;

        // Find where to insert the import
        const existingImports = content.match(/^import.*from.*['"];?\s*$/gm);
        if (existingImports && existingImports.length > 0) {
          // Insert after the last import
          const lastImport = existingImports[existingImports.length - 1];
          const lastImportIndex = content.lastIndexOf(lastImport);
          const insertIndex = lastImportIndex + lastImport.length + 1;
          content = content.slice(0, insertIndex) + importStatement + content.slice(insertIndex);
        } else {
          // Insert at the beginning
          content = importStatement + content;
        }
        hasChanges = true;
        changes.push(`Added missing ${getter} import`);
      }

      // Add instance variable if missing
      if (!hasInstance) {
        content = addInstanceVariable(content, service, getter);
        hasChanges = true;
        changes.push(`Added missing ${service} instance variable`);
      }
    }
  });

  // Remove duplicate imports
  const duplicateImportChecks = [
    { getter: 'getAuth', module: 'auth' },
    { getter: 'getFirestore', module: 'firestore' },
    { getter: 'getStorage', module: 'storage' },
    { getter: 'getDatabase', module: 'database' },
    { getter: 'getFunctions', module: 'functions' },
    { getter: 'getMessaging', module: 'messaging' },
  ];

  duplicateImportChecks.forEach(({ getter, module }) => {
    // More comprehensive regex to catch various import patterns
    const patterns = [
      // Standalone import: import { getAuth } from '@react-native-firebase/auth';
      new RegExp(`^\\s*import\\s*{\\s*${getter}\\s*}\\s*from\\s*['"]@react-native-firebase/${module}['"];?\\s*$`, 'gm'),
      // Multi-line import that only contains the getter
      new RegExp(`^\\s*import\\s*{[^}]*\\b${getter}\\b[^}]*}\\s*from\\s*['"]@react-native-firebase/${module}['"];?\\s*$`, 'gm')
    ];

    patterns.forEach(importRegex => {
      const matches = content.match(importRegex);

      if (matches && matches.length > 1) {
        // Remove all but the first occurrence
        let firstFound = false;
        content = content.replace(importRegex, (match) => {
          if (!firstFound) {
            firstFound = true;
            return match;
          }
          hasChanges = true;
          changes.push(`Removed duplicate ${getter} import`);
          return '';
        });
      }
    });
  });

  // Additional specific patterns that might be missed
  const additionalPatterns = [
    // Handle auth().currentUser patterns
    { from: /auth\(\)\.currentUser/g, to: 'auth.currentUser' },
    { from: /auth\(\)!/g, to: 'auth!' },
    { from: /auth\(\)\?/g, to: 'auth?' },
    // Handle firestore().collection patterns
    { from: /firestore\(\)\.collection/g, to: 'firestore.collection' },
    { from: /firestore\(\)\.doc/g, to: 'firestore.doc' },
    { from: /firestore\(\)\.batch/g, to: 'firestore.batch' },
    // Handle storage().ref patterns
    { from: /storage\(\)\.ref/g, to: 'storage.ref' },
    { from: /storage\(\)\.refFromURL/g, to: 'storage.refFromURL' },
  ];

  additionalPatterns.forEach(pattern => {
    const matches = content.match(pattern.from);
    if (matches) {
      content = content.replace(pattern.from, pattern.to);
      hasChanges = true;
      changes.push(`Fixed additional pattern: ${pattern.from.source} (${matches.length} occurrences)`);
    }
  });

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Migrated: ${filePath}`);
    changes.forEach(change => console.log(`   - ${change}`));
    return true;
  }

  return false;
}

function processDirectory(dirPath) {
  const files = getAllFiles(dirPath);
  let totalFiles = 0;
  let migratedFiles = 0;

  for (const file of files) {
    if (EXTENSIONS.some(ext => file.endsWith(ext))) {
      totalFiles++;
      if (migrateFirebaseFile(file)) {
        migratedFiles++;
      }
    }
  }

  console.log(`📁 ${dirPath}: ${migratedFiles}/${totalFiles} files migrated`);
  return migratedFiles;
}

// Main execution
console.log('🔥 Starting Firebase v22 migration...\n');
console.log('This will migrate from namespaced API to modular API:\n');
console.log('  auth() → getAuth()');
console.log('  firestore() → getFirestore()');
console.log('  storage() → getStorage()');
console.log('  database() → getDatabase()');
console.log('  functions() → getFunctions()');
console.log('  messaging() → getMessaging()\n');

let totalMigrated = 0;
for (const dir of DIRECTORIES) {
  if (fs.existsSync(dir)) {
    console.log(`Processing directory: ${dir}`);
    totalMigrated += processDirectory(dir);
  }
}

console.log(`\n✨ Firebase v22 migration completed! ${totalMigrated} files migrated.`);
console.log('\n📋 Next steps:');
console.log('1. Test the app thoroughly');
console.log('2. Check for any remaining deprecation warnings');
console.log('3. Restart Metro: npx react-native start --reset-cache');
console.log('4. Rebuild app: npx react-native run-android/ios');
