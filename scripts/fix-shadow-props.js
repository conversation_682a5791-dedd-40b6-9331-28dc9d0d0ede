#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix shadowOffset and other shadow props that are incorrectly used as direct props
 * Usage: node scripts/fix-shadow-props.js
 */

const fs = require('fs');
const path = require('path');

// File extensions to process
const EXTENSIONS = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to process
const DIRECTORIES = ['src/components', 'src/containers'];

// Shadow properties that should be in style objects
const SHADOW_PROPS = ['shadowOffset', 'shadowColor', 'shadowOpacity', 'shadowRadius', 'elevation'];

function fixShadowProps(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;

  // Check for direct shadow props usage (not in style objects)
  SHADOW_PROPS.forEach(prop => {
    // Pattern to match: prop={...} but not style={{prop: ...}}
    const directPropRegex = new RegExp(`\\s+${prop}=\\{([^}]+)\\}`, 'g');
    
    if (content.match(directPropRegex)) {
      console.log(`⚠️  Found direct ${prop} prop usage in: ${filePath}`);
      
      // This is a complex fix that would require AST parsing
      // For now, just log the issue
      console.log(`   Please manually fix: Move ${prop} prop to style object`);
      console.log(`   Change: ${prop}={...} to style={{${prop}: ...}}`);
    }
  });

  // Look for common patterns that might cause the warning
  const patterns = [
    // Check for spread operators that might include shadow props
    {
      regex: /\.\.\.(shadows?\.\w+)/g,
      description: 'Shadow spread operator usage'
    },
    // Check for style objects that might have shadow props at wrong level
    {
      regex: /style=\{[^}]*shadowOffset[^}]*\}/g,
      description: 'Style with shadowOffset'
    }
  ];

  patterns.forEach(pattern => {
    const matches = content.match(pattern.regex);
    if (matches) {
      console.log(`ℹ️  Found ${pattern.description} in: ${filePath}`);
      matches.forEach(match => {
        console.log(`   ${match}`);
      });
    }
  });

  return hasChanges;
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️  Directory not found: ${dirPath}`);
    return;
  }

  const files = fs.readdirSync(dirPath, {withFileTypes: true});

  for (const file of files) {
    const fullPath = path.join(dirPath, file.name);

    if (file.isDirectory()) {
      processDirectory(fullPath);
    } else if (EXTENSIONS.some(ext => file.name.endsWith(ext))) {
      try {
        fixShadowProps(fullPath);
      } catch (error) {
        console.error(`❌ Error processing ${fullPath}:`, error.message);
      }
    }
  }
}

// Main execution
console.log('🔍 Checking for shadow prop issues...\n');

for (const dir of DIRECTORIES) {
  console.log(`Processing directory: ${dir}`);
  processDirectory(dir);
}

console.log('\n✨ Shadow prop check completed!');
console.log('\n💡 Common fixes:');
console.log('   1. Move shadowOffset={...} to style={{shadowOffset: ...}}');
console.log('   2. Ensure shadow spreads are in style objects: style={{...shadows.md}}');
console.log('   3. Check third-party components for shadow prop conflicts');
