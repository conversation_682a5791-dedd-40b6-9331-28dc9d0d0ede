# Lib Directory

This directory contains local copies of third-party libraries that have been modified for compatibility with React Native's New Architecture.

## react-native-map-clustering

This is a local copy of the `react-native-map-clustering` library (v3.4.2) that has been modified to work with React Native's New Architecture.

### Changes Made:

1. **New Architecture Compatibility**: Updated ref handling and prop forwarding to work with the new architecture
2. **Error Handling**: Added proper null checks for callback functions and refs
3. **Styling**: Updated default cluster colors to match the app's orange theme (#FF8C42)
4. **TypeScript Support**: Added comprehensive TypeScript definitions

### Original Source:
- Repository: https://github.com/tomekvenits/react-native-map-clustering
- Version: 3.4.2

### Dependencies:
- `supercluster`: ^7.1.0
- `@mapbox/geo-viewport`: ^0.4.1

### Usage:

```javascript
import ClusteredMapView from '~lib';

<ClusteredMapView
  clusteringEnabled={true}
  radius={40}
  maxZoom={16}
  minPoints={2}
  clusterColor="#FF8C42"
  clusterTextColor="#FFFFFF"
  animationEnabled={true}
  spiralEnabled={true}
  onClusterPress={(cluster, markers) => {
    console.log('Cluster pressed with', markers.length, 'markers');
  }}
  onMarkersChange={(markers) => {
    console.log('Markers changed:', markers.length, 'total markers');
  }}>
  {/* Your markers here */}
</ClusteredMapView>
```

### Configuration:

The library is configured in:
- `babel.config.js`: Added `'~lib': './lib'` alias
- `tsconfig.json`: Added path mapping for `~lib`

This allows importing the library using `import ClusteredMapView from '~lib'` from anywhere in the app.
