import React, {memo} from 'react';
import {Text, View} from 'react-native';
import {Marker} from 'react-native-maps';
import {ClusterData, ClusterStyle, AnimationConfig} from '../types';
import {getClusterSize, formatClusterText} from '../utils/helpers';

interface ModernClusterMarkerProps {
  cluster: ClusterData;
  style: ClusterStyle;
  animation: AnimationConfig;
  onPress: () => void;
  index?: number;
}

const ModernClusterMarker: React.FC<ModernClusterMarkerProps> = memo(({cluster, style, onPress}) => {
  const {width, height, fontSize, color} = getClusterSize(cluster.count);
  const displayText = formatClusterText(cluster.count);

  return (
    <Marker coordinate={cluster.coordinate} onPress={onPress} tracksViewChanges={false}>
      <View
        style={{
          position: 'relative',
          width,
          height,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {/* Background circle with opacity - absolutely positioned */}
        <View
          style={{
            position: 'absolute',
            opacity: 0.5,
            zIndex: 0,
            width,
            height,
            borderRadius: width / 2,
            backgroundColor: color, // Use dynamic color based on count
            top: 0,
            left: 0,
          }}
        />

        {/* Foreground circle with text - absolutely positioned and centered */}
        <View
          style={{
            position: 'absolute',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1,
            width: width * 0.76,
            height: height * 0.76,
            borderRadius: (width * 0.76) / 2,
            backgroundColor: color, // Use dynamic color based on count
            top: '12%', // Center within the outer circle: (100% - 76%) / 2 = 12%
            left: '12%',
          }}>
          <Text
            style={{
              fontWeight: 'bold',
              color: style.textColor || '#FFFFFF',
              fontSize,
              textAlign: 'center',
            }}>
            {displayText}
          </Text>
        </View>
      </View>
    </Marker>
  );
});

ModernClusterMarker.displayName = 'ModernClusterMarker';

export {ModernClusterMarker};
