import React, {memo, useEffect, useRef, useState} from 'react';
import {Animated, Pressable} from 'react-native';
import {Marker} from 'react-native-maps';
import {MarkerData, AnimationConfig} from '../types';
import {createStaggeredDelay, getAnimationEasing} from '../utils/helpers';

interface ModernMarkerProps {
  marker: MarkerData;
  animation: AnimationConfig;
  onPress: () => void;
  children?: React.ReactNode;
  index?: number; // For staggered animations
}

const ModernMarker: React.FC<ModernMarkerProps> = memo(({marker, animation, onPress, children, index = 0}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const interactionAnim = useRef(new Animated.Value(1)).current;
  const pulseLoopRef = useRef<Animated.CompositeAnimation | null>(null);

  const [isPressed, setIsPressed] = useState(false);

  // Entry animation with enhanced bounce
  useEffect(() => {
    if (animation.enabled) {
      // Reset animations first
      scaleAnim.setValue(0);
      opacityAnim.setValue(0);

      // Staggered entry animation
      const delay = createStaggeredDelay(index, animation.entryDelay || 50);
      const bounceIntensity = animation.bounceIntensity || 1.15; // Use smaller default bounce
      const easing = getAnimationEasing('entry');

      const animationTimeout = setTimeout(() => {
        Animated.parallel([
          // Enhanced bounce entry
          Animated.sequence([
            Animated.spring(scaleAnim, {
              toValue: bounceIntensity, // Overshoot for dramatic effect
              tension: easing.tension,
              friction: easing.friction,
              useNativeDriver: animation.useNativeDriver,
            }),
            Animated.spring(scaleAnim, {
              toValue: 1, // Settle to normal size
              tension: easing.tension + 20,
              friction: easing.friction + 2,
              useNativeDriver: animation.useNativeDriver,
            }),
          ]),
          // Smooth opacity fade in
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: animation.duration * 0.8,
            useNativeDriver: animation.useNativeDriver,
          }),
        ]).start(() => {
          // Start subtle pulse animation after entry
          if (animation.pulseEnabled) {
            startPulseAnimation();
          }
        });
      }, delay);

      return () => {
        clearTimeout(animationTimeout);
        stopPulseAnimation();
      };
    } else {
      scaleAnim.setValue(1);
      opacityAnim.setValue(1);
    }
  }, [marker.id, animation.enabled]);

  // Pulse animation for better visibility
  const startPulseAnimation = () => {
    if (!animation.pulseEnabled) return;

    const pulseIntensity = animation.pulseIntensity || 1.08;
    const pulseDuration = animation.pulseDuration || 2000;

    pulseLoopRef.current = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: pulseIntensity,
          duration: pulseDuration / 2,
          useNativeDriver: animation.useNativeDriver,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: pulseDuration / 2,
          useNativeDriver: animation.useNativeDriver,
        }),
      ]),
    );
    pulseLoopRef.current.start();
  };

  const stopPulseAnimation = () => {
    if (pulseLoopRef.current) {
      pulseLoopRef.current.stop();
      pulseLoopRef.current = null;
    }
  };

  // Interaction animations
  const handlePressIn = () => {
    setIsPressed(true);
    const interactionScale = animation.interactionScale || 1.25;
    const interactionDuration = animation.interactionDuration || 150;

    Animated.spring(interactionAnim, {
      toValue: interactionScale,
      tension: getAnimationEasing('interaction').tension,
      friction: getAnimationEasing('interaction').friction,
      useNativeDriver: animation.useNativeDriver,
    }).start();
  };

  const handlePressOut = () => {
    setIsPressed(false);
    Animated.spring(interactionAnim, {
      toValue: 1,
      tension: getAnimationEasing('interaction').tension,
      friction: getAnimationEasing('interaction').friction,
      useNativeDriver: animation.useNativeDriver,
    }).start();
  };

  // Combined animated styles for smooth, prominent animations
  const animatedStyle = animation.enabled
    ? {
        transform: [
          {
            scale: Animated.multiply(scaleAnim, Animated.multiply(pulseAnim, interactionAnim)),
          },
        ],
        opacity: opacityAnim,
      }
    : {};

  // Use the original marker children if available, otherwise fall back to provided children
  const markerContent = marker.data?.originalChildren || children;

  // Extract original marker props, excluding the ones we want to override
  const {originalChildren, originalIndex, ...originalMarkerProps} = marker.data || {};

  const handlePress = () => {
    onPress();
  };

  return (
    <Marker coordinate={marker.coordinate} onPress={handlePress} tracksViewChanges={false} {...originalMarkerProps}>
      {animation.enabled ? (
        <Pressable onPressIn={handlePressIn} onPressOut={handlePressOut} onPress={handlePress}>
          <Animated.View style={animatedStyle}>{markerContent}</Animated.View>
        </Pressable>
      ) : (
        markerContent
      )}
    </Marker>
  );
});

ModernMarker.displayName = 'ModernMarker';

export {ModernMarker};
