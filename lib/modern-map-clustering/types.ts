import {ReactElement} from 'react';
import {MapViewProps, Region} from 'react-native-maps';

export interface MarkerData {
  id: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  data?: any;
}

export interface ClusterData {
  id: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  count: number;
  markers: MarkerData[];
  zoom?: number;
}

export interface ClusterPressEvent {
  cluster: ClusterData;
  markers: MarkerData[];
}

export interface ClusteringConfig {
  enabled: boolean;
  radius: number;
  maxZoom: number;
  minZoom: number;
  minPoints: number;
  extent: number;
  nodeSize: number;
}

export interface ClusterStyle {
  color: string;
  textColor: string;
  fontFamily?: string;
  borderWidth?: number;
  borderColor?: string;
}

export interface AnimationConfig {
  enabled: boolean;
  duration: number;
  useNativeDriver: boolean;
  // Enhanced animation properties
  entryDelay?: number;
  bounceIntensity?: number;
  pulseEnabled?: boolean;
  pulseIntensity?: number;
  pulseDuration?: number;
  interactionScale?: number;
  interactionDuration?: number;
}

export interface ModernClusteredMapViewProps extends Omit<MapViewProps, 'children'> {
  // Clustering configuration
  clustering?: Partial<ClusteringConfig>;

  // Styling
  clusterStyle?: Partial<ClusterStyle>;

  // Animation
  animation?: Partial<AnimationConfig>;

  // Event handlers
  onClusterPress?: (event: ClusterPressEvent) => void;
  onMarkerPress?: (marker: MarkerData) => void;
  onMarkersChange?: (markers: (MarkerData | ClusterData)[]) => void;

  // Custom rendering
  renderCluster?: (cluster: ClusterData) => ReactElement;
  renderMarker?: (marker: MarkerData) => ReactElement;

  // Performance
  enableViewportCulling?: boolean;
  maxMarkersToRender?: number;

  // Children (markers)
  children?: ReactElement | ReactElement[];
}

export interface ClusteringEngine {
  load(markers: MarkerData[]): void;
  getClusters(bbox: number[], zoom: number): (MarkerData | ClusterData)[];
  getLeaves(clusterId: string): MarkerData[];
}
