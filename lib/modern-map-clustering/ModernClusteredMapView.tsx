import React, {forwardRef, useCallback, useEffect, useMemo, useRef, useState, Children, ReactElement} from 'react';
import {Dimensions} from 'react-native';
import MapView, {Region} from 'react-native-maps';
import {
  ModernClusteredMapViewProps,
  MarkerData,
  ClusterData,
  ClusteringConfig,
  ClusterStyle,
  AnimationConfig,
} from './types';
import {ModernClusteringEngine, calculateBBox, calculateZoomFromRegion} from './utils/clustering';
import {isMarkerElement, extractMarkerData, debounce, areRegionsEqual, createDefaultConfig} from './utils/helpers';
import {ModernClusterMarker} from './components/ModernClusterMarker';
import {ModernMarker} from './components/ModernMarker';

/**
 * Modern ClusteredMapView - React 19 compatible with improved performance
 */
const ModernClusteredMapView = forwardRef<MapView, ModernClusteredMapViewProps>((props, ref) => {
  // Extract props with defaults
  const {
    clustering: clusteringProp,
    clusterStyle: clusterStyleProp,
    animation: animationProp,
    onClusterPress,
    onMarkerPress,
    onMarkersChange,
    onRegionChangeComplete,
    renderCluster,
    renderMarker,
    enableViewportCulling = true,
    maxMarkersToRender = 1000,
    children,
    ...mapProps
  } = props;

  // Create default configurations
  const defaultConfig = useMemo(() => createDefaultConfig(), []);

  const clusteringConfig: ClusteringConfig = useMemo(
    () => ({
      ...defaultConfig.clustering,
      ...clusteringProp,
    }),
    [clusteringProp, defaultConfig.clustering],
  );

  const clusterStyle: ClusterStyle = useMemo(
    () => ({
      ...defaultConfig.clusterStyle,
      ...clusterStyleProp,
    }),
    [clusterStyleProp, defaultConfig.clusterStyle],
  );

  const animationConfig: AnimationConfig = useMemo(
    () => ({
      ...defaultConfig.animation,
      ...animationProp,
    }),
    [animationProp, defaultConfig.animation],
  );

  // State
  const [currentRegion, setCurrentRegion] = useState<Region | null>(
    (props.region as Region) || (props.initialRegion as Region) || null,
  );
  const [renderedItems, setRenderedItems] = useState<(MarkerData | ClusterData)[]>([]);

  // Refs
  const mapRef = useRef<MapView>(null);
  const clusteringEngine = useRef<ModernClusteringEngine>(new ModernClusteringEngine(clusteringConfig));
  const lastRegion = useRef<Region | null>(null);

  // Extract markers from children
  const markers = useMemo(() => {
    if (!children) return [];

    const childArray = Children.toArray(children) as ReactElement[];
    return childArray.filter(isMarkerElement).map(extractMarkerData).slice(0, maxMarkersToRender);
  }, [children, maxMarkersToRender]);

  // Update clustering engine when markers change
  useEffect(() => {
    clusteringEngine.current.load(markers);
    if (currentRegion) {
      updateRenderedItems(currentRegion);
    }
  }, [markers]);

  // Update clustering engine configuration
  useEffect(() => {
    clusteringEngine.current = new ModernClusteringEngine(clusteringConfig);
    clusteringEngine.current.load(markers);
    if (currentRegion) {
      updateRenderedItems(currentRegion);
    }
  }, [clusteringConfig, markers]);

  // Initial render effect - ensure markers are rendered on first load
  useEffect(() => {
    if (markers.length > 0 && currentRegion) {
      updateRenderedItems(currentRegion);
    }
  }, [markers.length, currentRegion]);

  // Update rendered items based on region
  const updateRenderedItems = useCallback(
    (region: Region) => {
      if (!region) return;

      const bbox = calculateBBox(region);
      const {width, height} = Dimensions.get('window');
      const zoom = calculateZoomFromRegion(region, {width, height}); // Use proper viewport-based zoom calculation

      const items = clusteringEngine.current.getClusters(bbox, zoom);

      // Apply viewport culling if enabled - with expanded bounds for smoother experience
      const filteredItems = enableViewportCulling
        ? items.filter(item => {
            const {latitude, longitude} = item.coordinate;
            // Expand bounds by 20% to prevent pins from disappearing at edges
            const latPadding = (bbox[3] - bbox[1]) * 0.2;
            const lngPadding = (bbox[2] - bbox[0]) * 0.2;
            return (
              latitude >= bbox[1] - latPadding &&
              latitude <= bbox[3] + latPadding &&
              longitude >= bbox[0] - lngPadding &&
              longitude <= bbox[2] + lngPadding
            );
          })
        : items;

      // Only update if items have actually changed to prevent unnecessary re-renders
      setRenderedItems(prevItems => {
        if (prevItems.length !== filteredItems.length) {
          return filteredItems;
        }

        // Check if items are actually different
        const hasChanged = filteredItems.some((item, index) => {
          const prevItem = prevItems[index];
          if (!prevItem) return true;

          if ('count' in item && 'count' in prevItem) {
            // Compare clusters
            return item.id !== prevItem.id || item.count !== prevItem.count;
          } else if (!('count' in item) && !('count' in prevItem)) {
            // Compare markers
            return item.id !== prevItem.id;
          }
          return true;
        });

        return hasChanged ? filteredItems : prevItems;
      });

      // Notify parent of marker changes
      if (onMarkersChange) {
        onMarkersChange(filteredItems);
      }
    },
    [enableViewportCulling, onMarkersChange],
  );

  // Debounced region change handler - reduced delay for smoother pin loading
  const debouncedUpdateItems = useMemo(() => debounce(updateRenderedItems, 50), [updateRenderedItems]);

  // Handle region change
  const handleRegionChangeComplete = useCallback(
    (region: Region) => {
      // Avoid unnecessary updates
      if (areRegionsEqual(region, lastRegion.current)) {
        return;
      }

      lastRegion.current = region;
      setCurrentRegion(region);
      debouncedUpdateItems(region);

      // Call parent handler
      if (onRegionChangeComplete) {
        onRegionChangeComplete(region, {isGesture: true});
      }
    },
    [debouncedUpdateItems, onRegionChangeComplete],
  );

  // Handle map ready - ensure initial region is set
  const handleMapReady = useCallback(() => {
    if (!currentRegion && (props.initialRegion || props.region)) {
      const initialRegion = (props.region as Region) || (props.initialRegion as Region);
      setCurrentRegion(initialRegion);
      if (markers.length > 0) {
        updateRenderedItems(initialRegion);
      }
    }
  }, [currentRegion, props.region, props.initialRegion, markers.length]);

  // Handle cluster press
  const handleClusterPress = useCallback(
    (cluster: ClusterData) => {
      if (onClusterPress) {
        onClusterPress({
          cluster,
          markers: cluster.markers,
        });
      }
    },
    [onClusterPress],
  );

  // Handle marker press
  const handleMarkerPress = useCallback(
    (marker: MarkerData) => {
      if (onMarkerPress) {
        onMarkerPress(marker);
      }
    },
    [onMarkerPress],
  );

  // Render cluster marker
  const renderClusterMarker = useCallback(
    (cluster: ClusterData) => {
      if (renderCluster) {
        return renderCluster(cluster);
      }

      return (
        <ModernClusterMarker
          key={cluster.id}
          cluster={cluster}
          style={clusterStyle}
          animation={animationConfig}
          onPress={() => handleClusterPress(cluster)}
        />
      );
    },
    [renderCluster, clusterStyle, animationConfig, handleClusterPress],
  );

  // Render individual marker
  const renderIndividualMarker = useCallback(
    (marker: MarkerData, index: number = 0) => {
      if (renderMarker) {
        return renderMarker(marker);
      }

      return (
        <ModernMarker
          key={marker.id}
          marker={marker}
          animation={animationConfig}
          onPress={() => handleMarkerPress(marker)}
          index={index} // Pass index for staggered animations
        />
      );
    },
    [renderMarker, animationConfig, handleMarkerPress],
  );

  // Render all items with stable keys
  const renderItems = useMemo(() => {
    return renderedItems.map((item, index) => {
      if ('count' in item) {
        // It's a cluster - use cluster ID for stable key
        const cluster = item as ClusterData;
        return <React.Fragment key={`cluster-${cluster.id}`}>{renderClusterMarker(cluster)}</React.Fragment>;
      } else {
        // It's an individual marker - use marker ID for stable key
        const marker = item as MarkerData;
        return <React.Fragment key={`marker-${marker.id}`}>{renderIndividualMarker(marker, index)}</React.Fragment>;
      }
    });
  }, [renderedItems, renderClusterMarker, renderIndividualMarker]);

  // Expose map methods through ref
  React.useImperativeHandle(ref, () => mapRef.current!, []);

  return (
    <MapView
      ref={mapRef}
      {...mapProps}
      style={[
        mapProps.style,
        {overflow: 'visible'}, // Critical: Prevent cluster clipping at MapView level
      ]}
      onRegionChangeComplete={handleRegionChangeComplete}
      onMapReady={handleMapReady}>
      {renderItems}
    </MapView>
  );
});

ModernClusteredMapView.displayName = 'ModernClusteredMapView';

export default ModernClusteredMapView;
