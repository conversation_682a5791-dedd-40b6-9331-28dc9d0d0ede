{"name": "@pyxi/modern-map-clustering", "version": "1.0.0", "description": "Modern React 19 compatible map clustering for React Native", "main": "index.ts", "types": "index.ts", "scripts": {"build": "tsc", "lint": "eslint . --ext .ts,.tsx", "test": "jest"}, "keywords": ["react-native", "maps", "clustering", "react-19", "typescript"], "author": "Pyxi Team", "license": "MIT", "peerDependencies": {"react": ">=19.0.0", "react-native": ">=0.75.0", "react-native-maps": ">=1.0.0"}, "devDependencies": {"@types/react": "^19.0.0", "@types/react-native": "^0.75.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}