import {ReactElement} from 'react';
import {MarkerData} from '../types';

/**
 * Helper utilities for the modern clustering implementation
 */

export function isMarkerElement(child: ReactElement): boolean {
  return child && child.props && child.props.coordinate && child.props.cluster !== false;
}

export function extractMarkerData(child: ReactElement, index: number): MarkerData {
  const {coordinate, children, ...otherProps} = child.props;

  return {
    id: child.key || `marker_${index}`,
    coordinate: {
      latitude: coordinate.latitude,
      longitude: coordinate.longitude,
    },
    data: {
      ...otherProps,
      originalIndex: index,
      originalChildren: children, // Preserve the original marker children (ModernEventPin, etc.)
    },
  };
}

export function getClusterSize(count: number): {width: number; height: number; fontSize: number; color: string} {
  // Cluster sizes limited to max 32px to fit 64x64 limit with 2x map scale
  // Color intensity increases with event count
  if (count >= 50) {
    return {width: 29, height: 29, fontSize: 11, color: '#FF6B00'}; // Deep orange-red for highest count
  }
  if (count >= 20) {
    return {width: 28, height: 28, fontSize: 10, color: '#FF8C42'}; // Dark orange
  }
  if (count >= 10) {
    return {width: 27, height: 27, fontSize: 9, color: '#FFA366'}; // Light orange
  }
  if (count >= 8) {
    return {width: 26, height: 26, fontSize: 8, color: '#FFA366'}; // Lighter orange
  }
  if (count >= 4) {
    return {width: 25, height: 25, fontSize: 7, color: '#FFA366'}; // Very light orange
  }
  return {width: 25, height: 25, fontSize: 7, color: '#FFA366'}; // Lightest orange for smallest clusters
}

export function getInnerCircleSize(count: number): number {
  // Updated inner circle sizes to match 64x64 limit cluster sizes (76% of outer circle)
  if (count >= 50) {
    return 24; // 76% of 32
  }
  if (count >= 25) {
    return 23; // 76% of 30
  }
  if (count >= 15) {
    return 21; // 76% of 28
  }
  if (count >= 10) {
    return 20; // 76% of 26
  }
  if (count >= 8) {
    return 18; // 76% of 24
  }
  if (count >= 4) {
    return 17; // 76% of 22
  }
  return 15; // 76% of 20
}

export function formatClusterText(count: number): string {
  if (count >= 1000) {
    return `${Math.floor(count / 1000)}k+`;
  }
  if (count >= 100) {
    return '99+';
  }
  return count.toString();
}

export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

export function getDistance(
  coord1: {latitude: number; longitude: number},
  coord2: {latitude: number; longitude: number},
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = ((coord2.latitude - coord1.latitude) * Math.PI) / 180;
  const dLon = ((coord2.longitude - coord1.longitude) * Math.PI) / 180;

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((coord1.latitude * Math.PI) / 180) *
      Math.cos((coord2.latitude * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

export function areRegionsEqual(region1: any, region2: any, threshold: number = 0.0005): boolean {
  if (!region1 || !region2) return false;

  return (
    Math.abs(region1.latitude - region2.latitude) < threshold &&
    Math.abs(region1.longitude - region2.longitude) < threshold &&
    Math.abs(region1.latitudeDelta - region2.latitudeDelta) < threshold &&
    Math.abs(region1.longitudeDelta - region2.longitudeDelta) < threshold
  );
}

/**
 * Enhanced animation helpers for smooth pin interactions
 */
export function getEnhancedPinSize(
  baseSize: number = 40,
  isSelected: boolean = false,
  scaleFactor: number = 1,
): number {
  // Make pins more prominent by default
  const enhancedBaseSize = baseSize * 1.2; // 20% larger base size
  const selectionMultiplier = isSelected ? 1.15 : 1; // Subtle selection scaling
  return enhancedBaseSize * selectionMultiplier * scaleFactor;
}

export function createStaggeredDelay(index: number, baseDelay: number = 50): number {
  // Create natural staggered animation delays
  return baseDelay + index * 25; // 25ms between each pin
}

export function getAnimationEasing(type: 'entry' | 'interaction' | 'pulse' = 'entry') {
  switch (type) {
    case 'entry':
      return {tension: 100, friction: 8}; // Smooth bounce entry
    case 'interaction':
      return {tension: 150, friction: 6}; // Quick responsive interaction
    case 'pulse':
      return {tension: 80, friction: 10}; // Gentle pulsing
    default:
      return {tension: 100, friction: 8};
  }
}

export function createDefaultConfig() {
  const {width} = require('react-native').Dimensions.get('window');

  return {
    clustering: {
      enabled: true,
      radius: width * 0.06, // Same as original implementation
      maxZoom: 20,
      minZoom: 1,
      minPoints: 2,
      extent: 512,
      nodeSize: 64,
    },
    clusterStyle: {
      color: '#FF5722', // Very vibrant orange-red color for maximum visibility
      textColor: '#FFFFFF',
      borderWidth: 2,
      borderColor: '#FFFFFF',
    },
    animation: {
      enabled: true,
      duration: 300, // Slightly longer for smoother feel
      useNativeDriver: true,
      // Enhanced animation configuration
      entryDelay: 50, // Staggered entry for multiple pins
      bounceIntensity: 1.15, // Smaller bounce on entry (reduced from 1.4)
      pulseEnabled: true, // Enable subtle pulsing for visibility
      pulseIntensity: 1.08, // Subtle pulse scale
      pulseDuration: 2000, // Slow, gentle pulse
      interactionScale: 1.25, // Scale when pressed
      interactionDuration: 150, // Quick response to touch
    },
  };
}
