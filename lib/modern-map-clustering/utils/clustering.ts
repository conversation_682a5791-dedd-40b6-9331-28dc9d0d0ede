import Supercluster from 'supercluster';
import {viewport} from '@mapbox/geo-viewport';
import {MarkerData, ClusterData, ClusteringConfig} from '../types';

/**
 * Modern clustering implementation using Supercluster and @mapbox/geo-viewport
 * Same libraries as the original implementation but React 19 compatible
 */

export interface SuperclusterPoint {
  type: 'Feature';
  properties: {
    cluster?: boolean;
    cluster_id?: number;
    point_count?: number;
    point_count_abbreviated?: string;
    marker: MarkerData;
  };
  geometry: {
    type: 'Point';
    coordinates: [number, number];
  };
}

export class ModernClusteringEngine {
  private config: ClusteringConfig;
  private supercluster: Supercluster;
  private markers: MarkerData[] = [];

  constructor(config: ClusteringConfig) {
    this.config = config;
    this.supercluster = new Supercluster({
      radius: config.radius,
      maxZoom: config.maxZoom,
      minZoom: config.minZoom,
      minPoints: config.minPoints,
      extent: config.extent,
      nodeSize: config.nodeSize,
    });
  }

  load(markers: MarkerData[]): void {
    this.markers = markers;

    if (!this.config.enabled) {
      return;
    }

    // Convert markers to GeoJSON points for Supercluster
    const points: SuperclusterPoint[] = markers.map(marker => ({
      type: 'Feature',
      properties: {
        marker,
      },
      geometry: {
        type: 'Point',
        coordinates: [marker.coordinate.longitude, marker.coordinate.latitude],
      },
    }));

    this.supercluster.load(points);
  }

  getClusters(bbox: number[], zoom: number): (MarkerData | ClusterData)[] {
    if (!this.config.enabled) {
      // Return all markers if clustering is disabled
      return this.markers.filter(marker => this.isInBounds(marker, bbox));
    }

    // Clamp zoom to valid range to prevent clustering issues
    const clampedZoom = Math.max(this.config.minZoom, Math.min(this.config.maxZoom, Math.floor(zoom)));

    // Use Supercluster to get clusters
    const clusters = this.supercluster.getClusters(bbox, clampedZoom);

    return clusters.map((cluster: any) => {
      if (cluster.properties.cluster) {
        // It's a cluster - create stable ID based on coordinates and count
        const lat = Math.round(cluster.geometry.coordinates[1] * 10000) / 10000;
        const lng = Math.round(cluster.geometry.coordinates[0] * 10000) / 10000;
        const clusterData: ClusterData = {
          id: `cluster_${lat}_${lng}_${cluster.properties.point_count}`,
          coordinate: {
            latitude: lat, // Use rounded coordinates for stability
            longitude: lng, // Use rounded coordinates for stability
          },
          count: cluster.properties.point_count || 0,
          markers: this.getLeaves(cluster.properties.cluster_id!),
          zoom: clampedZoom, // Include current zoom level
        };
        return clusterData;
      } else {
        // It's an individual marker
        return cluster.properties.marker;
      }
    });
  }

  getLeaves(clusterId: number): MarkerData[] {
    const leaves = this.supercluster.getLeaves(clusterId, Infinity);
    return leaves.map((leaf: any) => leaf.properties.marker);
  }

  private isInBounds(marker: MarkerData, bbox: number[]): boolean {
    const [minLng, minLat, maxLng, maxLat] = bbox;
    const {latitude, longitude} = marker.coordinate;

    return longitude >= minLng && longitude <= maxLng && latitude >= minLat && latitude <= maxLat;
  }
}

export function calculateBBox(region: any): number[] {
  const {latitude, longitude, latitudeDelta, longitudeDelta} = region;

  return [
    longitude - longitudeDelta / 2, // minLng
    latitude - latitudeDelta / 2, // minLat
    longitude + longitudeDelta / 2, // maxLng
    latitude + latitudeDelta / 2, // maxLat
  ];
}

export function calculateZoom(latitudeDelta: number): number {
  return Math.round(Math.log(360 / latitudeDelta) / Math.LN2);
}

export function calculateZoomFromRegion(region: any, mapDimensions: {width: number; height: number}): number {
  const {latitude, longitude, latitudeDelta, longitudeDelta} = region;

  const bbox = [
    longitude - longitudeDelta / 2,
    latitude - latitudeDelta / 2,
    longitude + longitudeDelta / 2,
    latitude + latitudeDelta / 2,
  ];

  const vp = viewport(bbox, [mapDimensions.width, mapDimensions.height]);
  return vp.zoom;
}
