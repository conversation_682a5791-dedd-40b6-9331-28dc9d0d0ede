# Offline Chat Duplicate Message Fixes

## Problem Analysis

The excessive duplicate chats when in offline mode and transitioning from offline to online were caused by several issues:

### 1. **Multiple Retry Triggers**
- Both `OfflineChatInput` and `ChatWithUsers` components had their own retry logic
- Network state changes triggered multiple concurrent retry attempts
- No coordination between different retry mechanisms

### 2. **Race Conditions**
- Multiple components listening to network changes independently
- Concurrent retry attempts for the same messages
- No mechanism to prevent duplicate sends

### 3. **Lack of Deduplication**
- No checking for duplicate messages by `tempMessageId`
- No tracking of messages currently being sent
- Missing retry count limits and exponential backoff

### 4. **Poor Error Handling**
- No timeout mechanisms for retry attempts
- Insufficient cleanup when messages fail to send
- No proper synchronization between components

## Solution Implementation

### 1. **Enhanced OfflineChatStorage Service**

**File:** `src/services/OfflineChatStorage/OfflineChatStorage.ts`

**Changes:**
- Added `isBeingSent` and `lastRetryAttempt` fields to `UnsentMessage` interface
- Implemented duplicate message detection by `tempMessageId`
- Added retry count limits and exponential backoff logic
- Created `getMessagesReadyForRetry()` method with intelligent filtering
- Added `isMessageBeingSent()` method to prevent concurrent sends

**Key Features:**
```typescript
interface UnsentMessage {
  // ... existing fields
  lastRetryAttempt?: string;
  isBeingSent?: boolean; // Flag to prevent concurrent sending
}
```

### 2. **Centralized MessageRetryService**

**File:** `src/services/MessageRetryService/MessageRetryService.ts`

**Features:**
- **Singleton Pattern**: Ensures only one retry service instance
- **Network Monitoring**: Centralized network state handling with debouncing
- **Deduplication**: Prevents concurrent retry attempts for the same chat
- **Exponential Backoff**: Implements intelligent retry scheduling
- **Status Tracking**: Comprehensive message status management
- **Error Handling**: Proper cleanup and timeout mechanisms

**Key Methods:**
```typescript
class MessageRetryService {
  // Prevent concurrent retries
  async retryChatMessages(chatId: string, sendMessageFn: Function, callbacks?: RetryCallbacks)
  
  // Initialize network monitoring
  initialize()
  
  // Cleanup resources
  cleanup()
  
  // Schedule retries with exponential backoff
  private scheduleRetry(message: UnsentMessage, ...)
}
```

### 3. **Updated Component Integration**

**Files:**
- `src/components/Chat/OfflineChatInput/OfflineChatInput.tsx`
- `src/containers/Chats/ChatWithUsers/ChatWithUsers.tsx`

**Changes:**
- Removed duplicate retry logic from components
- Integrated centralized `MessageRetryService`
- Simplified network state monitoring
- Removed race condition-prone code

**Before:**
```typescript
// Multiple components had their own retry logic
const retryUnsentMessages = async () => {
  // Duplicate retry implementation
  // No deduplication
  // Race conditions possible
}
```

**After:**
```typescript
// Centralized retry service
useEffect(() => {
  const retryService = MessageRetryService.getInstance();
  retryService.initialize();
  return () => retryService.cleanup();
}, []);

// Use centralized retry
if (isOnline) {
  retryService.retryChatMessages(chatId, onSendMessage, callbacks);
}
```

## Key Improvements

### 1. **Duplicate Prevention**
- ✅ Messages are checked for duplicates by `tempMessageId` before storage
- ✅ Concurrent retry attempts are prevented with `isBeingSent` flag
- ✅ Only one retry service instance can run at a time

### 2. **Intelligent Retry Logic**
- ✅ Exponential backoff prevents overwhelming the server
- ✅ Maximum retry count (3) prevents infinite loops
- ✅ Time-based retry scheduling with increasing delays

### 3. **Better Error Handling**
- ✅ Proper cleanup of failed messages
- ✅ Status tracking throughout the retry process
- ✅ Timeout mechanisms for retry attempts

### 4. **Network State Management**
- ✅ Debounced network state changes (1-second delay)
- ✅ Centralized network monitoring
- ✅ Reduced redundant retry triggers

### 5. **Resource Management**
- ✅ Proper cleanup of timeouts and listeners
- ✅ Memory leak prevention
- ✅ Singleton pattern for service management

## Testing

**File:** `src/services/MessageRetryService/__tests__/MessageRetryService.test.ts`

**Test Coverage:**
- ✅ Duplicate prevention scenarios
- ✅ Concurrent retry attempt handling
- ✅ Successful retry and cleanup
- ✅ Failed retry error handling
- ✅ Service initialization and cleanup

## Usage

### For New Chat Components

```typescript
import MessageRetryService from '~services/MessageRetryService';

// Initialize in component
useEffect(() => {
  const retryService = MessageRetryService.getInstance();
  retryService.initialize();
  return () => retryService.cleanup();
}, []);

// Retry messages when needed
const retryMessages = async () => {
  const retryService = MessageRetryService.getInstance();
  await retryService.retryChatMessages(chatId, sendMessageFunction, {
    onMessageStatusChange: handleStatusChange,
    onOfflineMessagesSent: handleNotification,
  });
};
```

### For Offline Message Storage

```typescript
import OfflineChatStorage from '~services/OfflineChatStorage';

// Store message (automatically prevents duplicates)
await OfflineChatStorage.storeUnsentMessage({
  chatId,
  text,
  tempMessageId, // Used for deduplication
  // ... other fields
});

// Get messages ready for retry (intelligent filtering)
const readyMessages = await OfflineChatStorage.getMessagesReadyForRetry(chatId);
```

## Benefits

1. **Eliminates Duplicate Messages**: Comprehensive deduplication at multiple levels
2. **Improves Performance**: Reduces unnecessary API calls and network traffic
3. **Better User Experience**: Consistent message status and reliable delivery
4. **Maintainable Code**: Centralized logic is easier to debug and extend
5. **Resource Efficient**: Proper cleanup prevents memory leaks
6. **Scalable**: Can easily be extended for additional chat types

## Migration Notes

- Existing offline messages will continue to work
- No breaking changes to existing APIs
- Components automatically benefit from the new retry logic
- Old retry code has been safely removed to prevent conflicts
